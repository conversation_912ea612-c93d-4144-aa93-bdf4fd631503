# 天气与空气质量分析与预测应用 - 运行过程说明

本文档旨在详细描述本应用的核心运行流程，从数据采集到用户交互，以及所涉及的关键技术组件。

## 一、 核心组件与技术栈

*   **Web框架**: Flask (Python) - 用于构建后端API和渲染前端页面。
*   **数据采集**:
    *   `aqi_spider.py`: 用于爬取历史和实时空气质量指数 (AQI) 数据。
    *   `weather_spider.py`: 用于爬取历史和实时天气数据。
    *   依赖库: `requests`, `BeautifulSoup4`, `lxml` (可能)。
*   **数据存储**: SQLite - 通过 `data.db` 文件存储采集到的天气和AQI数据。
*   **数据预处理与模型训练**:
    *   `train_models.py`: 核心模型训练脚本，用于训练AQI和天气的预测模型。
    *   `utils.py`: 包含数据处理、特征工程等辅助函数。
    *   模型类型: Prophet, LSTM (Keras/TensorFlow), GRU (Keras/TensorFlow), LightGBM。
    *   数据处理库: `pandas`, `numpy`, `scikit-learn`。
*   **前端展示**:
    *   HTML模板 (位于 `templates/` 目录)。
    *   CSS (Bootstrap, 自定义样式位于 `static/css/`)。
    *   JavaScript (jQuery, Chart.js, `predict_dashboard.js` 等位于 `static/js/`)。
*   **数据库管理**: `create_db.py` 用于初始化数据库表结构。
*   **配置文件**: `.env` 存储敏感配置 (如 Flask `SECRET_KEY`)。

## 二、 应用运行流程

### 阶段一：数据准备与模型训练 (通常为离线/后台操作)

1.  **初始化数据库**:
    *   **操作**: 运行 `python create_db.py`。
    *   **作用**: 在项目根目录下创建 `data.db` 数据库文件，并建立所需的数据表（如 `weather_data`, `aqi_data`）。

2.  **数据采集**:
    *   **操作**:
        *   运行 `python aqi_spider.py` 采集AQI数据。
        *   运行 `python weather_spider.py` 采集天气数据。
    *   **作用**: 脚本会访问目标数据源网站，解析HTML内容，提取所需数据，并将其存储到 `data.db` 数据库中对应的表中。
    *   **注意**:
        *   爬虫脚本内有建表逻辑，通常作为数据库不存在时的后备。
        *   部分爬虫脚本支持指定采集的城市和时间范围。
        *   `DEBUG_SAVE_HTML` 选项在脚本中用于保存原始HTML，辅助调试爬虫逻辑（输出到 `debug_html/` 目录）。

3.  **数据预处理与模型训练**:
    *   **操作**: 运行 `python train_models.py`。
    *   **作用**:
        1.  从 `data.db` 加载原始天气和AQI数据。
        2.  执行数据清洗、缺失值处理、特征工程（如时间特征提取、数据平滑等，具体逻辑在 `train_models.py` 和 `utils.py` 中）。
        3.  使用处理后的数据训练多种预测模型 (Prophet, LSTM, GRU, LightGBM) 分别针对不同的指标 (如PM2.5, O3, AQI指数, 平均温度)。
        4.  保存训练好的模型文件 (如 `.h5`, `.pkl`, `.json`) 到 `trained_models/` 目录。
        5.  保存用于数据标准化的scalers (如 `scaler_pm25.pkl`) 到 `trained_models/` 目录。
        6.  生成并保存模型评估指标到 `trained_models/model_metrics.json`。
        7.  可能生成评估图表到 `trained_models/evaluation_plots/` 目录。
    *   **依赖**: 需要 `trained_models/` 目录预先存在。

### 阶段二：Web应用启动与用户交互 (在线服务)

1.  **启动Flask应用**:
    *   **操作**: 在项目根目录下运行 `python app.py` (或者通过Flask CLI: `flask run`，具体取决于 `app.py` 的结构)。
    *   **作用**: 启动Flask开发服务器，应用开始监听指定的端口（默认为5000）。
    *   **初始化**:
        *   `app.py` 会加载 `.env` 文件中的配置。
        *   `app.py` 会注册蓝图 (Blueprints)，如 `predict_api.py` (处理预测请求) 和 `data_api.py` (处理数据查询请求)。
        *   `app.py` (或其调用的模块) 会在启动时加载 `trained_models/` 目录下的预训练模型和scalers到内存中，以便快速响应预测请求。

2.  **用户访问网页**:
    *   用户通过浏览器访问应用的URL (例如 `http://localhost:5000/`)。
    *   **首页 (`/`)**: 通常展示项目概览或导航。由 `app.py` 中的路由处理，渲染 `templates/index.html` (或其他指定的首页模板)。
    *   **数据展示页面 (例如 `/history_data`)**:
        *   用户通过导航访问历史数据展示页面。
        *   后端从 `data.db` 查询数据。
        *   前端使用Chart.js等库将数据可视化（如图表）。
    *   **预测仪表盘 (`/predict_dashboard` 或类似路由)**:
        *   用户访问预测仪表盘。
        *   页面 (如 `templates/predict_dashboard.html`) 加载，并通过JavaScript (`static/js/predict_dashboard.js`) 发起API请求到后端。
        *   **API请求 (例如 `/api/predict_aqi` 或 `/api/predict_weather`)**:
            *   这些请求由 `blueprints/predict_api.py` 中的路由处理。
            *   后端接收请求参数 (如预测的未来天数、城市等)。
            *   调用加载到内存中的预训练模型进行预测。
            *   使用对应的scaler对输入数据进行预处理 (如果模型训练时使用了标准化)，并对模型的原始输出进行反向转换。
            *   将预测结果以JSON格式返回给前端。
        *   前端JavaScript接收到JSON数据后，动态更新页面内容，如使用Chart.js将预测数据显示为图表。

## 三、 数据流总结

1.  **原始数据**: 外部网站 -> `aqi_spider.py` / `weather_spider.py`
2.  **数据存储**: 爬虫脚本 -> `data.db`
3.  **模型训练数据**: `data.db` -> `train_models.py` (经过预处理)
4.  **训练产物**: 训练脚本 -> `trained_models/` (模型文件, scalers, 评估指标)
5.  **应用数据输入**: `data.db` (历史数据展示), 用户输入 (预测请求)
6.  **预测流程**: 用户请求 -> `app.py` / `predict_api.py` -> 已加载的模型 -> 预测结果 (JSON) -> 前端JavaScript -> 用户界面

## 四、 注意事项与维护点

*   **依赖管理**: `requirements.txt` 文件定义了项目运行所需的Python包。应保持更新，并在新环境部署时通过 `pip install -r requirements.txt` 安装。
*   **环境变量**: 敏感信息 (如 `SECRET_KEY`) 应通过 `.env` 文件管理，并且 `.env` 文件不应提交到版本控制系统。
*   **模型更新**: 当有新的数据或改进的算法时，需要重新运行 `train_models.py` 来更新 `trained_models/` 目录中的模型。应用重启后会加载新的模型。
*   **爬虫维护**: 数据源网站的结构可能会发生变化，导致爬虫失效。需要定期检查并维护 `aqi_spider.py` 和 `weather_spider.py`。
*   **日志与调试**: 应用和脚本中的日志输出对于问题排查非常重要。

这个流程描述了应用从数据采集到最终用户交互的完整生命周期。 