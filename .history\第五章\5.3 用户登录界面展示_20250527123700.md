# 5.3 用户登录界面展示

为了保障系统数据的安全和提供个性化的用户体验，本系统设计并实现了用户登录功能。该功能主要依赖于 Flask-Login 扩展，并结合自定义的用户数据管理方式。相关实现主要分布在`app.py`、`models.py`、`database.py`以及`blueprints/auth.py`和相应的 HTML 模板文件（如`templates/login.html`）。

### 登录界面设计

系统的登录界面旨在提供简洁、友好的用户交互体验。界面通常包含用户名输入框、密码输入框以及登录按钮。如图 5.3-1 所示为本系统登录页面的一个示例设计。

前端界面（`templates/login.html`或类似命名的文件）使用 HTML、CSS 和可能的 JavaScript 构建。表单通过 POST 请求将用户输入的凭证提交到后端进行验证。界面设计考虑了响应式布局，以适应不同设备的显示需求。

图 5.3-1：用户登录界面示例

### 认证流程实现

用户认证的核心流程在`blueprints/auth.py`蓝图定义的路由函数中实现，例如`/login`路由。

1.  **用户提交凭证**：用户在登录页面输入用户名和密码，点击登录。
2.  **后端接收与验证**：Flask 后端通过`request.form`获取提交的用户名和密码。
3.  **用户信息查询**：系统会连接用户数据库（`user_info.db`，通过`database.py`中的`get_user_db()`函数获取连接）。在`user_info.db`的`user`表中查询是否存在与输入用户名匹配的用户记录，并验证密码是否正确。密码验证通常会涉及哈希密码的比对，例如使用 Werkzeug 提供的`check_password_hash`函数，确保密码的安全性。图 5.3-2 展示了用户认证的流程。
4.  **会话管理**：如果用户名和密码验证通过，Flask-Login 的`login_user()`函数会被调用，将用户标记为已登录状态，并在用户会话中存储用户 ID。
5.  **页面跳转**：登录成功后，用户通常会被重定向到系统的主页面或其尝试访问的受保护页面。如果验证失败，则会向用户显示错误提示信息，并重新渲染登录页面。

`app.py`中配置了`LoginManager`实例，包括设置登录视图（`login_manager.login_view = "auth.login"`，指向认证蓝图的登录路由）和未登录时的提示信息。

图 5.3-2：用户认证流程图

### 用户信息管理

本系统的用户信息存储在名为`user_info.db`的 SQLite 数据库中，该数据库包含一个`user`表，用于存储用户名和（哈希后的）密码等信息。

- **User 模型**：`models.py`中定义了`User`类，该类通常继承自 Flask-Login 的`UserMixin`。这个类封装了用户对象的核心属性和方法，如`id` (通常是用户名)、`is_authenticated`、`is_active`等。如图 5.3-3 展示了`User`类的核心定义。
- **用户加载器**：`app.py`中定义了`@login_manager.user_loader`修饰的`load_user(user_id)`函数。该函数负责在每个请求开始时，根据存储在会话中的`user_id`从数据库中加载对应的用户对象。如果找不到用户或发生错误，则返回`None`。
- **数据库交互**：所有对`user_info.db`的读写操作都通过`database.py`中定义的函数（如`get_user_db`）进行，确保数据库连接的正确管理。用户注册功能（如果实现）也会涉及向该表写入新的用户记录。

通过上述设计，系统能够安全有效地管理用户信息，并为授权用户提供服务。

图 5.3-3：User 模型类定义示例
