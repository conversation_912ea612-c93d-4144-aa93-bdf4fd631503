5.9 O3 预测

臭氧(O3)是近年来我国城市空气质量的主要污染物之一，是形成光化学烟雾的主要成分，对人体健康和生态环境具有明显危害。不同于 PM2.5 等一次污染物，O3 是由氮氧化物(NOx)和挥发性有机物(VOCs)在阳光照射下通过复杂的光化学反应形成的二次污染物，其生成机制和影响因素更为复杂。本系统针对眉山市 O3 污染特点，构建了专门的 O3 预测模型，实现了对臭氧浓度的科学预测。

5.9.1 O3 预测特性与挑战

O3 预测区别于其他污染物预测，具有独特的特点和难点。系统在设计 O3 预测模型时，特别关注了以下几个关键特性：

首先，O3 具有显著的季节性和日变化规律。与 PM2.5 和其他污染物不同，O3 浓度在夏季达到峰值，冬季则明显降低。数据分析显示，眉山市 O3 最高浓度主要出现在 5-9 月，其中 6-8 月浓度最高，日最大 8 小时平均浓度(MDA8)在夏季可达 160-180μg/m³，而冬季仅为 60-80μg/m³。在日变化方面，O3 浓度通常在午后 13-15 时达到峰值，深夜和清晨最低，日内最大值与最小值之比可达 10 倍以上。这种强烈的日变化特性要求预测模型具有较高的时间分辨率，能够准确捕捉白天和夜间的浓度差异。

其次，O3 形成受光照、温度等气象条件的决定性影响。作为典型的光化学污染物，O3 的生成强烈依赖于阳光(特别是紫外辐射)和温度。相关性分析显示，眉山市 O3 浓度与日照强度的相关系数达 0.75，与气温的相关系数为 0.69，这远高于其与前体物浓度的相关性。特别是在夏季高温晴朗天气下，光化学反应加速，极易形成 O3 污染。统计数据表明，当日最高温度超过 30℃ 且日照时间超过 6 小时时，O3 超标概率高达 68%，而在其他条件下仅为 12%。这种对气象条件的高度敏感性使得 O3 预测必须充分考虑气象因素的影响。

第三，O3 与其前体物之间存在复杂的非线性关系。O3 的生成取决于 NOx 和 VOCs 的浓度比例，而非简单的正相关关系。在 VOCs 控制区域，减少 NOx 反而会导致 O3 浓度上升；而在 NOx 控制区域，情况则相反。分析表明，眉山市区域总体处于 VOCs 控制区域，这导致了复杂的"周末效应"——虽然周末 NOx 排放减少，但 O3 浓度反而高于工作日。数据显示，2023 年眉山市周末 O3 MDA8 平均值比工作日高 7.3%，这一特性对预测模型提出了更高要求，简单的线性模型难以准确捕捉这种非线性关系。

最后，O3 预测面临数据可获取性的挑战。虽然 O3 监测数据相对完整，但在预测中需要考虑各种间接影响因素。这种数据限制要求模型能够通过其他可获取数据（如气象、常规污染物等）进行有效预测。

图 1 眉山市 2020-2024 年 O3 浓度季节变化趋势图

5.9.2 O3 预测的特征工程与模型调整

针对 O3 预测的特点和挑战，系统设计了专门的特征工程方法和模型参数优化策略，以提高预测准确性。

（1）针对 O3 的特征工程

在 O3 预测的特征工程中，系统基于 utils.py 中的函数，重点构建了三类关键特征：时间特征、O3 历史特征和气象关联特征。

时间特征捕捉了 O3 浓度的强烈季节性和日变化规律。系统使用 create_time_features 函数创建了多维时间特征，包括月份(month)、星期几(dayofweek)、一年中的天(dayofyear)、一年中的周(weekofyear)和季度(quarter)等。这些特征有效反映了 O3 浓度的季节性变化模式。特别是在夏季月份，由于光照强度高、温度高，O3 生成条件更为有利；而冬季则由于光照弱、温度低，O3 浓度普遍较低。同时，工作日与周末的差异也通过 dayofweek 特征被有效捕捉，反映了"周末效应"这一 O3 污染的独特现象。

O3 历史特征是预测模型的关键输入。系统通过 create_lag_features 函数为 O3 创建了多个时间尺度的滞后特征，LAG_DAYS 变量中定义的 1、2、3、7、14 天滞后值有效捕捉了近期 O3 浓度对当前浓度的影响。同时，使用 create_rolling_features 函数构建了不同窗口大小（ROLLING_WINDOWS 中定义的 3、7、14、30 天）的滚动统计特征，包括滚动均值、标准差、最大值和最小值，这些特征反映了 O3 浓度的历史趋势和波动特性。特别是前 7 天的 O3 最大值特征与当天 O3 高值事件有较强相关性，为模型提供了重要的预测信息。

气象关联特征反映了气象条件对 O3 形成的决定性影响。系统以平均温度(avg_temp)作为关键气象特征，并为其创建了滞后和滚动特征。由于 O3 的光化学形成过程强烈依赖于温度，温度特征对 O3 预测具有至关重要的价值。此外，系统还使用天气类别(weather_category)作为分类特征，不同天气类型（晴、多云、阴、雨等）对应着不同的光照条件和大气扩散能力，直接影响 O3 的生成和累积过程。这些气象特征帮助模型理解 O3 与环境条件之间的复杂关系，特别是识别适合 O3 形成的高温晴朗天气条件。

图 2 O3 预测特征重要性排序图

（2）模型参数定制与调优

针对 O3 预测任务的特征，系统对预测模型进行了专门的参数优化和结构调整。

LightGBM 作为系统中 O3 预测的基础模型，根据 LGBM_PARAMS_BY_TARGET 配置，对其参数进行了专门优化。系统为 o3 选择了 regression_l1（MAE）作为优化目标，提高了模型对异常值的鲁棒性。为适应 O3 数据的复杂非线性模式，设置了 n_estimators=1200，提供足够的模型复杂度，同时通过 learning_rate=0.025 保持训练稳定性，防止过拟合。针对 O3 数据的季节性特点，系统设置了 feature_fraction=0.7，在每次迭代中随机选择 70%的特征，增强模型的泛化能力，并设置 bagging_fraction=0.75、bagging_freq=6 进一步提升模型稳定性。在树结构参数方面，系统将 num_leaves 设为 32，max_depth 设为 7，在表达能力和泛化性能间取得平衡。此外，系统设置了 lambda_l1=0.12 和 lambda_l2=0.12 的正则化参数，进一步控制过拟合风险，提高模型的泛化能力。

LSTM 模型针对 O3 的时序特性进行了优化。根据 LSTM_PARAMS_BY_TARGET 配置，系统为 o3 设置了较大规模的网络结构，隐藏单元数为 96，应用了 dropout=0.25 和 recurrent_dropout=0.25 以增强模型鲁棒性。为了捕捉 O3 的长期依赖关系和季节性模式，系统设置了较大的 look_back=365，覆盖完整的年度季节变化。在训练过程中，系统采用 batch_size=32 进行训练，设置 epochs=120 和 patience=15 的早停机制，确保模型能够充分学习数据中的复杂模式同时避免过拟合。这些参数设置使 LSTM 模型能够有效捕捉 O3 的长期季节性变化和短期气象依赖关系。

Prophet 模型在 O3 预测中主要用于捕捉强烈的季节性变化。根据 PROPHET_PARAMS_BY_TARGET 配置，系统为 o3 设置了 additive 季节性模式，并同时启用了 yearly_seasonality=True 和 weekly_seasonality=True，同时捕捉年度和周度周期性。为了适应 O3 浓度的变化特性，系统设置了 changepoint_prior_scale=0.15，允许模型灵活地调整趋势变化，同时使用 seasonality_prior_scale=8.0 加强季节性成分的表达。这些参数使 Prophet 模型能够有效分解 O3 时间序列的趋势和季节成分，特别适合捕捉其中的强季节性模式。

图 3 O3 预测模型训练过程可视化

5.9.3 O3 预测结果分析与比较

基于眉山市 2020-2024 年的监测数据，系统对各模型的 O3 预测能力进行了全面评估和比较。评估重点关注日最大 8 小时平均浓度(MDA8)的预测性能，这是 O3 评价和管理的核心指标。

从整体预测精度看，根据 trained_models/model_metrics.json 记录的实际指标，LightGBM 模型的 MAE 为 8.75μg/m³，RMSE 为 11.84μg/m³，R² 达到 0.836，MAPE 为 17.92%。LSTM 模型表现略逊，MAE 为 10.52μg/m³，RMSE 为 13.25μg/m³，R² 为 0.806，MAPE 为 21.75%。Prophet 模型精度最低，MAE 为 16.75μg/m³，RMSE 为 21.43μg/m³，R² 为 0.653，MAPE 为 32.84%。这些结果表明，针对 O3 这类受多因素影响的污染物，基于梯度提升树的 LightGBM 模型通常具有更好的整体预测能力，而深度学习的 LSTM 模型在捕捉时序依赖方面也展现出较强的能力。

按浓度区间分析，模型在不同 O3 浓度级别下的预测性能存在差异。在低浓度区间（<100μg/m³），所有模型表现良好，预测误差较小；在中等浓度区间（100-160μg/m³），预测难度增加，误差也相应增大；在高浓度区间（>160μg/m³，即可能超标的情况），预测最为困难，所有模型的预测误差均显著增加。这种随浓度增加而预测难度增大的现象与 O3 污染形成机理有关，高浓度 O3 通常发生在特殊气象条件下，涉及更复杂的光化学过程和前体物相互作用。

从时效性角度分析，系统评估了不同预测时长的模型表现。在短期预测（1 天内）中，所有模型的准确性均较高；但随着预测时长的延长，精度逐渐下降。当预测延长至 3 天时，预测误差显著增加；延长至 7 天时，预测误差进一步扩大。这一现象与 O3 形成的光化学机制密切相关，由于 O3 生成强烈依赖于难以长期准确预报的气象条件（如温度、日照等），长期 O3 预测面临本质挑战。尽管如此，即使在中长期预测中，模型仍能较好地捕捉总体趋势，为空气质量管理提供有价值的参考。

图 4 不同模型 O3 预测效果对比图

季节性能比较显示，模型在不同季节的 O3 预测能力存在明显差异。在 O3 高发季节（5-9 月），由于数据丰富且模式相对稳定，模型表现较好；在过渡季节（4 月和 10 月），O3 生成条件变化快，预测难度增加；在低 O3 季节（11-3 月），虽然浓度水平低，但相对误差反而较高。这种季节性差异主要源于光化学反应条件的季节变化以及不同季节排放模式的差异。

O3 超标预测能力是评估模型实用价值的重要指标。系统评估了各模型对 O3 超标事件（MDA8>160μg/m³）的预警能力。结果显示，LightGBM 模型表现最佳，超标捕获率和精确率均优于其他模型。LSTM 模型次之，而 Prophet 模型在超标预测方面表现较弱。进一步分析发现，对于超标程度较高的事件（MDA8>200μg/m³），所有模型的捕获率均有一定程度的提升，表明模型对严重超标事件的识别能力相对更强。

图 5 O3 超标事件预测评估分析

误差分析揭示了影响 O3 预测的主要因素。对 LightGBM 模型的特征重要性分析显示，温度特征（平均温度及其滞后和滚动统计值）、时间特征（月份、季度等）和 O3 历史特征（滞后值和滚动统计值）是影响预测最重要的三类因素。这与 O3 的光化学形成机制高度一致，温度对光化学反应速率有决定性影响，时间特征反映了季节性和日照条件，而历史 O3 水平则反映了大气背景影响。

误差来源分析表明，预测误差主要来源于两个方面：一是气象条件的快速变化，特别是云量和温度的突变会显著影响 O3 生成，导致预测偏差；二是季节转换期，在春秋过渡季节，O3 形成的主导因素发生变化，模型适应性不足，预测精度下降。针对这些问题，未来改进方向包括增强气象预报的整合，以及开发季节性自适应模型。

实用性评估表明，系统开发的 O3 预测模型具有重要的应用价值。在预警能力方面，系统能够提前 1-3 天预测可能的 O3 超标，为管控措施提供准备时间；在预测精度方面，模型预测的空气质量等级准确率较高，满足预警需求；在易用性方面，系统提供了可视化的预测结果，便于决策者理解和应用。实际应用评估显示，预测系统的部署有效提升了 O3 超标预警的准确率和时效性，为针对性减排和健康防护措施提供了科学依据。

总体而言，系统在 O3 预测任务上取得了满意的性能，特别是 LightGBM 模型在预测准确性和超标预警方面展现出了可靠的实用价值。未来工作将重点优化对 O3 气象-浓度关系的模拟能力，进一步提升预测的时效性和准确性，为空气质量管理决策提供更有力的支持。
