# 代码清单（基于程序真实情况）

## 代码 5-1: 温度数据预处理核心代码

```python
# 数据缩放 (仅对数值目标)
scalers = {}  # 存储 scaler
y_train_scaled = pd.DataFrame(index=y_train_raw.index)
y_test_scaled = pd.DataFrame(index=y_test_raw.index)

for target in numerical_targets:
    scaler = MinMaxScaler(feature_range=(0, 1))
    y_train_target_scaled = scaler.fit_transform(y_train_raw[[target]])
    y_test_target_scaled = scaler.transform(y_test_raw[[target]])
    y_train_scaled[target] = y_train_target_scaled.flatten()
    y_test_scaled[target] = y_test_target_scaled.flatten()
    scalers[target] = scaler

    # 保存scaler
    scaler_save_path = os.path.join(MODEL_SAVE_DIR, f"scaler_{target}.pkl")
    joblib.dump(scaler, scaler_save_path)
    logging.info(f"Scaler for '{target}' 已保存到: {scaler_save_path}")
```

## 代码 5-2: 温度预测特征工程实现

```python
def create_time_features(df):
    """从日期列创建各种时间特征（月份、星期几等）"""
    # 检查数据是否有日期列且格式正确
    if "date" not in df.columns or not pd.api.types.is_datetime64_any_dtype(df["date"]):
        logging.error("输入数据缺少日期列或日期格式不对")
        return df  # 返回原始数据避免出错

    # 复制数据避免修改原始数据
    df_copy = df.copy()

    # 创建各种时间特征
    df_copy["month"] = df_copy["date"].dt.month  # 月份 (1-12)
    df_copy["day"] = df_copy["date"].dt.day  # 日 (1-31)
    df_copy["dayofweek"] = df_copy["date"].dt.dayofweek  # 星期几 (0-6，0是周一)
    df_copy["dayofyear"] = df_copy["date"].dt.dayofyear  # 一年中的第几天 (1-366)
    df_copy["weekofyear"] = (
        df_copy["date"].dt.isocalendar().week.astype(int)
    )  # 一年中的第几周
    df_copy["quarter"] = df_copy["date"].dt.quarter  # 季度 (1-4)

    logging.info("成功创建了时间特征")
    return df_copy

def create_lag_features(df, target_cols, lag_days):
    """创建滞后特征（比如前1天、前3天的数据）"""
    # 复制并排序数据
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")

    # 为每个目标列创建滞后特征
    for col in target_cols:
        # 检查列是否存在
        if col not in df_copy.columns:
            logging.warning(f"列 '{col}' 不存在，跳过")
            continue

        # 为每个滞后天数创建特征
        for lag in lag_days:
            # 例如：如果lag=3，则创建"3天前的值"作为新特征
            df_copy[f"{col}_lag_{lag}"] = df_copy[col].shift(lag)

    logging.info(f"为{target_cols}创建了滞后天数{lag_days}的特征")
    return df_copy

def create_rolling_features(df, target_cols, windows):
    """创建滚动统计特征（如过去7天的平均值、最大值等）"""
    # 复制并排序数据
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")

    # 为每个目标列创建滚动特征
    for col in target_cols:
        # 检查列是否存在
        if col not in df_copy.columns:
            logging.warning(f"列 '{col}' 不存在，跳过")
            continue

        # 为每个窗口大小创建特征
        for window in windows:
            # 滚动对象仅使用过去的数据（不包括当天）
            rolling_obj = (
                df_copy[col]
                .shift(1)  # 先移动一天，确保不用当天数据
                .rolling(window=window, min_periods=1, closed="left")
            )

            # 创建各种统计特征
            df_copy[f"{col}_roll_mean_{window}"] = rolling_obj.mean()  # 平均值
            df_copy[f"{col}_roll_std_{window}"] = rolling_obj.std()  # 标准差
            df_copy[f"{col}_roll_min_{window}"] = rolling_obj.min()  # 最小值
            df_copy[f"{col}_roll_max_{window}"] = rolling_obj.max()  # 最大值

    logging.info(f"为{target_cols}创建了窗口大小{windows}的滚动特征")
    return df_copy
```

## 代码 5-3: LightGBM 模型参数配置

```python
# 针对不同目标变量的LightGBM回归参数
LGBM_PARAMS_BY_TARGET = {
    "avg_temp": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1200,
        "learning_rate": 0.02,
        "feature_fraction": 0.85,
        "bagging_fraction": 0.85,
        "bagging_freq": 7,
        "lambda_l1": 0.05,
        "lambda_l2": 0.05,
        "num_leaves": 40,
        "max_depth": 8,
        "verbose": -1,
        "n_jobs": -1,
        "seed": 42,
        "boosting_type": "gbdt",
    }
}

# 训练LightGBM模型
lgbm_reg = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET[target])
logging.info(f"开始训练 LGBM for {target}...")
eval_set = [(X_test_lgbm, y_test_target)]
callbacks = [lgb.early_stopping(stopping_rounds=50, verbose=False)]
lgbm_reg.fit(
    X_train_lgbm,
    y_train_target,
    eval_set=eval_set,
    eval_metric="mae",
    callbacks=callbacks,
)
logging.info(f"LGBM for {target} 训练完成 (最佳迭代: {lgbm_reg.best_iteration_}).")
```

## 代码 5-4: LSTM 模型数据序列化实现

```python
def create_dataset_lstm_gru(data, look_back=180):
    """
    为 LSTM/GRU 创建序列数据集。
    Args:
        data (numpy array): 输入的时间序列数据 (通常是单变量，形状为 (n_samples,))
        look_back (int): 回看的时间步长。
    Returns:
        X (numpy array): 输入序列 (形状: [样本数, look_back, 1])
        y (numpy array): 目标值 (形状: [样本数,])
    """
    X, y = [], []
    if len(data) <= look_back:
        logging.warning(
            f"数据长度 ({len(data)}) 不足以创建 look_back={look_back} 的序列。"
        )
        return np.array(X), np.array(y)
    # 生成 N - look_back 个样本
    for i in range(len(data) - look_back):
        X.append(data[i : (i + look_back)])  # 获取 look_back 个历史点
        y.append(data[i + look_back])  # 获取下一个点作为目标
    X, y = np.array(X), np.array(y)
    # 将 X 重塑为 LSTM/GRU 需要的 3D 格式 [样本数, 时间步长, 特征数]
    # 因为我们通常一次只预测一个目标，所以特征数是 1
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))
    return X, y
```

## 代码 5-5: LSTM 模型架构设计

```python
# 针对不同目标变量的LSTM参数
LSTM_PARAMS_BY_TARGET = {
    "avg_temp": {
        "units": 128,
        "dropout": 0.2,
        "recurrent_dropout": 0.2,
        "batch_size": 32,
        "epochs": 150,
        "patience": 15,
        "look_back": 365,  # 捕捉年度季节性
    }
}

# 定义 LSTM 模型
model_lstm = Sequential([
    LSTM(LSTM_PARAMS_BY_TARGET[target]["units"],
         input_shape=(LOOK_BACK, 1),
         dropout=LSTM_PARAMS_BY_TARGET[target]["dropout"],
         recurrent_dropout=LSTM_PARAMS_BY_TARGET[target]["recurrent_dropout"]),
    Dense(1)  # 输出层，预测一个值
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# 使用 EarlyStopping 回调函数
early_stopping = EarlyStopping(
    monitor="val_loss",
    patience=LSTM_PARAMS_BY_TARGET[target]["patience"],
    restore_best_weights=True,
    verbose=0,
)
```

## 代码 5-6: Prophet 模型数据准备

```python
# 准备Prophet所需格式的数据
prophet_df = df[['date', target]].rename(columns={'date': 'ds', target: 'y'})
prophet_df = prophet_df.dropna()

# 划分训练集和测试集
train_size = int(len(prophet_df) * (1 - TEST_SET_RATIO))
train_prophet_df = prophet_df[:train_size]
test_prophet_df = prophet_df[train_size:]

# Prophet参数配置
PROPHET_PARAMS_BY_TARGET = {
    "avg_temp": {
        "seasonality_mode": "multiplicative",
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.05,
        "seasonality_prior_scale": 10.0,
    }
}

# 创建Prophet模型
model_prophet = Prophet(**PROPHET_PARAMS_BY_TARGET[target])
model_prophet.fit(train_prophet_df)
```

## 代码 5-7: AQI 预测特征构建

```python
def create_aqi_features(df):
    """创建AQI预测的特征集"""
    # 污染物相关特征
    pollutants = ['pm25', 'pm10', 'so2', 'no2', 'co', 'o3']

    # 为每种污染物创建滞后特征
    for pollutant in pollutants:
        if pollutant in df.columns:
            for lag in [1, 2, 3, 7]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

    # 添加污染物间的交互特征
    if 'pm25' in df.columns and 'pm10' in df.columns:
        df['pm_ratio'] = df['pm25'] / df['pm10'].replace(0, 0.001)

    # 添加气象影响特征
    weather_features = ['avg_temp', 'humidity', 'wind_speed']
    for feature in weather_features:
        if feature in df.columns:
            for lag in [1, 3]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

    return df
```

## 代码 5-8: AQI 预测的 LightGBM 模型参数配置

```python
# AQI预测的LightGBM参数配置
LGBM_PARAMS_BY_TARGET = {
    "aqi_index": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1000,
        "learning_rate": 0.03,
        "feature_fraction": 0.8,
        "bagging_fraction": 0.8,
        "bagging_freq": 5,
        "lambda_l1": 0.1,
        "lambda_l2": 0.1,
        "num_leaves": 35,
        "max_depth": 7,
        "verbose": -1,
        "n_jobs": -1,
        "seed": 42,
        "boosting_type": "gbdt",
    }
}

# 训练模型
lgbm_reg = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['aqi_index'])
logging.info(f"开始训练 LGBM for aqi_index...")
eval_set = [(X_test_lgbm, y_test_target)]
callbacks = [lgb.early_stopping(stopping_rounds=50, verbose=False)]
lgbm_reg.fit(
    X_train_lgbm,
    y_train_target,
    eval_set=eval_set,
    eval_metric="mae",
    callbacks=callbacks,
)
logging.info(f"LGBM for aqi_index 训练完成 (最佳迭代: {lgbm_reg.best_iteration_}).")
```

## 代码 5-9: AQI 预测的 LSTM 序列数据准备

```python
def create_multivariate_lstm_data(df, target, feature_cols, sequence_length=180):
    """创建多变量LSTM输入序列"""
    X, y = [], []

    # 提取目标列和特征列
    data = df[feature_cols + [target]].values

    # 创建序列样本
    for i in range(len(data) - sequence_length):
        X.append(data[i:i+sequence_length, :-1])  # 所有特征
        y.append(data[i+sequence_length, -1])    # 目标值

    return np.array(X), np.array(y)

# AQI预测的LSTM参数
LSTM_PARAMS_BY_TARGET = {
    "aqi_index": {
        "units": 96,
        "dropout": 0.25,
        "recurrent_dropout": 0.25,
        "batch_size": 32,
        "epochs": 100,
        "patience": 12,
        "look_back": 180,
    }
}
```

## 代码 5-10: AQI 预测的 LSTM 模型结构

```python
# 构建AQI预测的LSTM模型
model_lstm = Sequential([
    LSTM(LSTM_PARAMS_BY_TARGET["aqi_index"]["units"],
         input_shape=(LOOK_BACK, 1),
         dropout=LSTM_PARAMS_BY_TARGET["aqi_index"]["dropout"],
         recurrent_dropout=LSTM_PARAMS_BY_TARGET["aqi_index"]["recurrent_dropout"]),
    Dense(1)  # 输出层，预测一个值
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# 使用 EarlyStopping 回调函数
early_stopping = EarlyStopping(
    monitor="val_loss",
    patience=LSTM_PARAMS_BY_TARGET["aqi_index"]["patience"],
    restore_best_weights=True,
    verbose=0,
)

# 训练模型
history = model_lstm.fit(
    X_train_lstm,
    y_train_lstm,
    epochs=LSTM_PARAMS_BY_TARGET["aqi_index"]["epochs"],
    batch_size=LSTM_PARAMS_BY_TARGET["aqi_index"]["batch_size"],
    validation_data=(X_test_lstm, y_test_lstm),
    callbacks=[early_stopping],
    verbose=0,
)
```

## 代码 5-11: AQI 预测的 Prophet 模型参数配置

```python
# AQI预测的Prophet参数配置
PROPHET_PARAMS_BY_TARGET = {
    "aqi_index": {
        "seasonality_mode": "additive",
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.1,
        "seasonality_prior_scale": 5.0,
    }
}

# 创建Prophet模型
model_prophet = Prophet(**PROPHET_PARAMS_BY_TARGET["aqi_index"])

# 添加国家法定假日(可选)
try:
    from prophet.holidays import add_country_holidays
    model_prophet.add_country_holidays(country_name='China')
except:
    logging.warning("无法添加中国节假日，继续训练...")

# 训练模型
model_prophet.fit(train_prophet_df)

# 创建未来日期框架进行预测
future = model_prophet.make_future_dataframe(periods=N_FORECAST_DAYS)
forecast = model_prophet.predict(future)
```

## 代码 5-12: PM2.5 预测特征构建

```python
def create_pm25_features(df):
    """创建PM2.5预测的特征集"""
    # 气象相关特征
    weather_features = ['avg_temp', 'humidity', 'pressure', 'wind_speed', 'wind_direction']

    # 为气象特征创建滞后和滚动特征
    for feature in weather_features:
        if feature in df.columns:
            # 滞后特征
            for lag in [1, 2, 3, 7]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

            # 滚动统计特征
            for window in [3, 7, 14]:
                df[f'{feature}_rolling_mean_{window}'] = df[feature].rolling(window=window).mean()
                if feature != 'wind_direction':  # 对非方向性特征计算统计量
                    df[f'{feature}_rolling_std_{window}'] = df[feature].rolling(window=window).std()

    # 污染物相互影响特征
    other_pollutants = ['pm10', 'so2', 'no2', 'co', 'o3']
    for pollutant in other_pollutants:
        if pollutant in df.columns:
            # 滞后特征
            for lag in [1, 2, 3]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

            # 与PM2.5的比值特征
            if 'pm25' in df.columns:
                df[f'pm25_{pollutant}_ratio'] = df['pm25'] / df[pollutant].replace(0, 0.001)

    # 添加时间特征
    if 'date' in df.columns:
        # 季节相关编码，使用三角函数将循环特性编码
        df['month_sin'] = np.sin(2 * np.pi * df['date'].dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['date'].dt.month / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['date'].dt.day / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['date'].dt.day / 31)

    return df
```

## 代码 5-13: PM2.5 预测的 LightGBM 模型参数

```python
# PM2.5预测的LightGBM参数
LGBM_PARAMS_BY_TARGET = {
    "pm25": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1000,
        "learning_rate": 0.03,
        "feature_fraction": 0.75,
        "bagging_fraction": 0.8,
        "bagging_freq": 5,
        "lambda_l1": 0.15,
        "lambda_l2": 0.15,
        "num_leaves": 31,
        "max_depth": 6,
        "verbose": -1,
        "n_jobs": -1,
        "seed": 42,
        "boosting_type": "gbdt",
    }
}

# 训练模型
model_lgbm = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['pm25'])
model_lgbm.fit(
    X_train, y_train,
    eval_set=[(X_test, y_test)],
    eval_metric='mae',
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
)
```

## 代码 5-14: PM2.5 预测的 LSTM 数据预处理

```python
def prepare_pm25_lstm_data(df, sequence_length=180):
    """PM2.5预测的LSTM数据准备"""
    # 选择关键特征
    selected_features = [
        'pm25', 'pm10', 'humidity', 'avg_temp', 'pressure',
        'wind_speed', 'month_sin', 'month_cos'
    ]

    # 确保所有特征存在
    available_features = [f for f in selected_features if f in df.columns]

    # 提取数据
    data = df[available_features].values

    # 归一化数据
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(data)

    # 创建序列数据
    X, y = [], []
    for i in range(len(scaled_data) - sequence_length):
        # 提取特征序列
        features_sequence = scaled_data[i:i+sequence_length, :]
        # 目标值是序列后的PM2.5值(假设PM2.5在第一列)
        target = scaled_data[i+sequence_length, 0]
        X.append(features_sequence)
        y.append(target)

    # 转换为NumPy数组
    X = np.array(X)
    y = np.array(y)

    return X, y, scaler

# PM2.5预测的LSTM参数
LSTM_PARAMS_BY_TARGET = {
    "pm25": {
        "units": 64,
        "dropout": 0.3,
        "recurrent_dropout": 0.3,
        "batch_size": 32,
        "epochs": 100,
        "patience": 12,
        "look_back": 180,
    }
}
```

## 代码 5-15: PM2.5 预测的 LSTM 网络结构

```python
# 构建PM2.5预测的LSTM模型
model_lstm = Sequential([
    LSTM(LSTM_PARAMS_BY_TARGET["pm25"]["units"],
         input_shape=(LOOK_BACK, 1),
         dropout=LSTM_PARAMS_BY_TARGET["pm25"]["dropout"],
         recurrent_dropout=LSTM_PARAMS_BY_TARGET["pm25"]["recurrent_dropout"]),
    Dense(1)  # 输出层，预测一个值
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# 使用 EarlyStopping 回调函数
early_stopping = EarlyStopping(
    monitor="val_loss",
    patience=LSTM_PARAMS_BY_TARGET["pm25"]["patience"],
    restore_best_weights=True,
    verbose=0,
)

# 训练模型
history = model_lstm.fit(
    X_train_lstm,
    y_train_lstm,
    epochs=LSTM_PARAMS_BY_TARGET["pm25"]["epochs"],
    batch_size=LSTM_PARAMS_BY_TARGET["pm25"]["batch_size"],
    validation_data=(X_test_lstm, y_test_lstm),
    callbacks=[early_stopping],
    verbose=0,
)
```

## 代码 5-16: PM2.5 预测的 Prophet 模型配置

```python
# PM2.5预测的Prophet参数配置
PROPHET_PARAMS_BY_TARGET = {
    "pm25": {
        "seasonality_mode": "additive",
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.1,
        "seasonality_prior_scale": 5.0,
    }
}

# 创建Prophet模型
model_prophet = Prophet(**PROPHET_PARAMS_BY_TARGET["pm25"])

# 添加国家法定假日(可选)
try:
    from prophet.holidays import add_country_holidays
    model_prophet.add_country_holidays(country_name='China')
except:
    logging.warning("无法添加中国节假日，继续训练...")

# 训练模型
model_prophet.fit(train_prophet_df)

# 创建未来日期框架进行预测
future = model_prophet.make_future_dataframe(periods=N_FORECAST_DAYS)
forecast = model_prophet.predict(future)
```

## 代码 5-17: O3 预测特征构建

```python
def create_o3_features(df):
    """创建O3预测的特征集"""
    # 气象相关特征（O3与温度、湿度、风速等密切相关）
    weather_features = ['avg_temp', 'humidity', 'pressure', 'wind_speed', 'wind_direction']

    # 为气象特征创建滞后和滚动特征
    for feature in weather_features:
        if feature in df.columns:
            # 滞后特征
            for lag in [1, 2, 3, 7]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

            # 滚动统计特征
            for window in [3, 7, 14]:
                df[f'{feature}_rolling_mean_{window}'] = df[feature].rolling(window=window).mean()
                if feature != 'wind_direction':
                    df[f'{feature}_rolling_std_{window}'] = df[feature].rolling(window=window).std()

    # 其他污染物特征（O3与NOx、VOCs等前体物相关）
    other_pollutants = ['pm25', 'pm10', 'so2', 'no2', 'co']
    for pollutant in other_pollutants:
        if pollutant in df.columns:
            # 滞后特征
            for lag in [1, 2, 3]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

    # 添加时间特征（O3有强烈的季节性和日变化）
    if 'date' in df.columns:
        df['month_sin'] = np.sin(2 * np.pi * df['date'].dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['date'].dt.month / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['date'].dt.day / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['date'].dt.day / 31)

    return df
```

## 代码 5-18: O3 预测的 LightGBM 模型参数

```python
# O3预测的LightGBM参数
LGBM_PARAMS_BY_TARGET = {
    "o3": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1200,
        "learning_rate": 0.025,
        "feature_fraction": 0.7,
        "bagging_fraction": 0.75,
        "bagging_freq": 6,
        "lambda_l1": 0.12,
        "lambda_l2": 0.12,
        "num_leaves": 32,
        "max_depth": 7,
        "verbose": -1,
        "n_jobs": -1,
        "seed": 42,
        "boosting_type": "gbdt",
    }
}

# 训练模型
model_lgbm = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['o3'])
model_lgbm.fit(
    X_train, y_train,
    eval_set=[(X_test, y_test)],
    eval_metric='mae',
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
)
```

## 代码 5-19: O3 预测的 LSTM 模型构建

```python
# O3预测的LSTM参数
LSTM_PARAMS_BY_TARGET = {
    "o3": {
        "units": 96,
        "dropout": 0.25,
        "recurrent_dropout": 0.25,
        "batch_size": 32,
        "epochs": 120,
        "patience": 15,
        "look_back": 365,  # 臭氧有强季节性
    }
}

# 构建O3预测的LSTM模型
model_lstm = Sequential([
    LSTM(LSTM_PARAMS_BY_TARGET["o3"]["units"],
         input_shape=(LOOK_BACK, 1),
         dropout=LSTM_PARAMS_BY_TARGET["o3"]["dropout"],
         recurrent_dropout=LSTM_PARAMS_BY_TARGET["o3"]["recurrent_dropout"]),
    Dense(1)  # 输出层，预测一个值
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# 使用 EarlyStopping 回调函数
early_stopping = EarlyStopping(
    monitor="val_loss",
    patience=LSTM_PARAMS_BY_TARGET["o3"]["patience"],
    restore_best_weights=True,
    verbose=0,
)

# 训练模型
history = model_lstm.fit(
    X_train_lstm,
    y_train_lstm,
    epochs=LSTM_PARAMS_BY_TARGET["o3"]["epochs"],
    batch_size=LSTM_PARAMS_BY_TARGET["o3"]["batch_size"],
    validation_data=(X_test_lstm, y_test_lstm),
    callbacks=[early_stopping],
    verbose=0,
)
```

## 代码 5-20: O3 预测的 Prophet 模型配置

```python
# O3预测的Prophet参数配置
PROPHET_PARAMS_BY_TARGET = {
    "o3": {
        "seasonality_mode": "additive",
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.15,
        "seasonality_prior_scale": 8.0,
    }
}

# 创建Prophet模型
model_prophet = Prophet(**PROPHET_PARAMS_BY_TARGET["o3"])

# 添加国家法定假日(可选)
try:
    from prophet.holidays import add_country_holidays
    model_prophet.add_country_holidays(country_name='China')
except:
    logging.warning("无法添加中国节假日，继续训练...")

# 训练模型
model_prophet.fit(train_prophet_df)

# 创建未来日期框架进行预测
future = model_prophet.make_future_dataframe(periods=N_FORECAST_DAYS)
forecast = model_prophet.predict(future)
```

## 代码 5-21: 天气类别预测特征构建

```python
def create_weather_category_features(df):
    """创建天气类别预测的特征集"""
    # 气象相关特征
    weather_features = ['avg_temp', 'humidity', 'pressure', 'wind_speed', 'wind_direction']

    # 为气象特征创建滞后和滚动特征
    for feature in weather_features:
        if feature in df.columns:
            # 滞后特征
            for lag in [1, 2, 3, 7]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

            # 滚动统计特征
            for window in [3, 7]:
                df[f'{feature}_rolling_mean_{window}'] = df[feature].rolling(window=window).mean()
                if feature != 'wind_direction':
                    df[f'{feature}_rolling_std_{window}'] = df[feature].rolling(window=window).std()

    # 污染物特征（空气质量影响能见度等天气现象）
    pollutants = ['pm25', 'pm10', 'so2', 'no2', 'co', 'o3', 'aqi_index']
    for pollutant in pollutants:
        if pollutant in df.columns:
            # 滞后特征
            for lag in [1, 2, 3]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

    # 添加时间特征
    if 'date' in df.columns:
        df['month_sin'] = np.sin(2 * np.pi * df['date'].dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['date'].dt.month / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['date'].dt.day / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['date'].dt.day / 31)
        df['dayofweek'] = df['date'].dt.dayofweek
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)

    return df
```

## 代码 5-22: 天气类别预测的 LightGBM 模型参数

```python
# LightGBM分类参数
LGBM_PARAMS_CLASSIFICATION = {
    "objective": "multiclass",
    "metric": "multi_logloss",
    "num_class": 10,  # 根据 LabelEncoder 动态更新
    "n_estimators": 1000,
    "learning_rate": 0.04,
    "feature_fraction": 0.8,
    "bagging_fraction": 0.8,
    "bagging_freq": 4,
    "lambda_l1": 0.08,
    "lambda_l2": 0.08,
    "num_leaves": 35,
    "max_depth": 7,
    "verbose": -1,
    "n_jobs": -1,
    "seed": 42,
    "boosting_type": "gbdt",
    "class_weight": "balanced",  # 处理类别不平衡
}

# 训练模型
model_lgbm_clf = lgb.LGBMClassifier(**LGBM_PARAMS_CLASSIFICATION)
model_lgbm_clf.fit(
    X_train, y_train,
    eval_set=[(X_test, y_test)],
    eval_metric='multi_logloss',
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
)
```

## 代码 5-23: 天气类别预测的 GRU 模型数据准备

```python
# 天气类别预测的GRU参数
GRU_PARAMS = {
    "units": 96,
    "dropout": 0.25,
    "recurrent_dropout": 0.25,
    "batch_size": 32,
    "epochs": 100,
    "patience": 12,
    "look_back": 180,
}

# 为GRU准备序列数据
def prepare_gru_weather_data(df, target_col, look_back=180):
    """为GRU模型准备天气类别预测数据"""
    # 提取目标变量（已编码的天气类别）
    target_data = df[target_col].values

    # 创建序列数据
    X, y = [], []
    for i in range(len(target_data) - look_back):
        X.append(target_data[i:(i + look_back)])
        y.append(target_data[i + look_back])

    X, y = np.array(X), np.array(y)
    # 重塑为GRU需要的3D格式 [样本数, 时间步长, 特征数=1]
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))

    return X, y
```

## 代码 5-24: 天气类别预测的 GRU 模型结构

```python
# 定义 GRU 模型
model_gru = Sequential([
    GRU(GRU_PARAMS["units"],
        input_shape=(LOOK_BACK, 1),
        dropout=GRU_PARAMS["dropout"],
        recurrent_dropout=GRU_PARAMS["recurrent_dropout"]),
    Dense(num_classes, activation="softmax")  # 输出层，对应天气类别数
])

# 编译模型。因为 y 是整数编码，使用 sparse_categorical_crossentropy
model_gru.compile(
    optimizer="adam",
    loss="sparse_categorical_crossentropy",
    metrics=["accuracy"],
)

# 使用 EarlyStopping 回调函数
early_stopping_gru = EarlyStopping(
    monitor="val_accuracy",
    patience=GRU_PARAMS["patience"],
    restore_best_weights=True,
    mode="max",
    verbose=0,
)

# 训练模型
history_gru = model_gru.fit(
    X_train_gru,
    y_train_gru,
    epochs=GRU_PARAMS["epochs"],
    batch_size=GRU_PARAMS["batch_size"],
    validation_data=(X_test_gru, y_test_gru),
    callbacks=[early_stopping_gru],
    verbose=0,
)
```

## 代码 5-25: 天气类别预测的 TCN 模型数据准备

```python
# TCN模型参数
TCN_PARAMS = {
    "nb_filters": 64,
    "kernel_size": 3,
    "dilations": [1, 2, 4, 8],
    "dropout_rate": 0.1,
    "batch_size": 32,
    "epochs": 100,
    "patience": 12,
    "look_back": 180,
}

# 为TCN准备序列数据（与GRU类似）
def prepare_tcn_weather_data(df, target_col, look_back=180):
    """为TCN模型准备天气类别预测数据"""
    # 提取目标变量（已编码的天气类别）
    target_data = df[target_col].values

    # 创建序列数据
    X, y = [], []
    for i in range(len(target_data) - look_back):
        X.append(target_data[i:(i + look_back)])
        y.append(target_data[i + look_back])

    X, y = np.array(X), np.array(y)
    # 重塑为TCN需要的3D格式 [样本数, 时间步长, 特征数=1]
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))

    return X, y
```

## 代码 5-26: 天气类别预测的 TCN 模型实现

```python
import tensorflow as tf
from tensorflow.keras.layers import Conv1D, LayerNormalization, Activation, Add, Input
from tensorflow.keras.layers import GlobalAveragePooling1D, Dense, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam

def residual_block(x, dilation_rate, nb_filters, kernel_size, padding, dropout_rate=0.1):
    """TCN残差块实现"""
    # 跳跃连接
    prev_x = x

    # 第一层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 第二层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 如果输入和输出维度不匹配，进行投影
    if prev_x.shape[-1] != x.shape[-1]:
        prev_x = Conv1D(nb_filters, 1, padding='same')(prev_x)

    # 添加残差连接
    res = Add()([prev_x, x])

    return res

def build_weather_category_tcn_model(input_shape, num_classes):
    """构建天气类别预测的TCN模型"""
    input_layer = Input(shape=input_shape)

    # TCN块，膨胀率逐层增加
    x = input_layer
    nb_filters = TCN_PARAMS["nb_filters"]
    kernel_size = TCN_PARAMS["kernel_size"]

    for dilation_rate in TCN_PARAMS["dilations"]:
        x = residual_block(x, dilation_rate, nb_filters, kernel_size, 'causal',
                          TCN_PARAMS["dropout_rate"])

    # 全局池化后接全连接层
    x = GlobalAveragePooling1D()(x)
    x = Dense(128, activation='relu')(x)
    x = Dropout(0.2)(x)
    x = Dense(64, activation='relu')(x)

    # 输出层
    output_layer = Dense(num_classes, activation='softmax')(x)

    model = Model(inputs=input_layer, outputs=output_layer)

    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )

    return model

# 创建TCN模型
model_tcn = build_weather_category_tcn_model((LOOK_BACK, 1), num_classes)

# 使用 EarlyStopping 回调函数
early_stopping_tcn = EarlyStopping(
    monitor="val_accuracy",
    patience=TCN_PARAMS["patience"],
    restore_best_weights=True,
    mode="max",
    verbose=0,
)

# 训练模型
history_tcn = model_tcn.fit(
    X_train_tcn,
    y_train_tcn,
    epochs=TCN_PARAMS["epochs"],
    batch_size=TCN_PARAMS["batch_size"],
    validation_data=(X_test_tcn, y_test_tcn),
    callbacks=[early_stopping_tcn],
    verbose=0,
)
```
