5.2 爬虫数据的清洗

本系统通过网络爬虫获取的原始天气和空气质量数据往往存在缺失值、异常值以及格式不统一等问题，需要进行系统性的数据清洗和预处理，以确保后续分析和模型训练的数据质量。数据清洗主要通过 fix_db_quality.py 和 fix_weather_csv.py 等脚本实现。

5.2.1 数据质量问题识别

系统对爬取的原始数据进行全面检查，主要识别以下几类数据质量问题：数值型数据的缺失和异常，如温度数据中的空值或超出合理范围的极端值；日期格式不一致，如不同格式的日期表示；文本数据不规范，如天气描述中的多余空格或特殊字符；重复记录，如由于爬虫重试机制导致的数据重复。系统通过 check_imported_data.py 和 check_weather.py 脚本自动扫描数据并生成质量报告，为后续清洗提供依据。

5.2.2 数据清洗流程

数据清洗遵循"检测-处理-验证"的标准流程。首先，系统扫描数据库中的各类记录，检测存在问题的数据项；然后针对不同类型的问题采用相应的处理策略；最后通过数据验证确认清洗效果。清洗流程如图 5.2-1 所示。

对于数值型数据的清洗，系统优先采用统计学方法处理异常值，如使用 3σ 原则识别温度数据中的离群点，并根据时间序列上下文进行修正。当数据缺失时，系统根据时间连续性特征，采用前后均值填充或线性插值等方法补充缺失值。特别是对于 AQI 指数和 PM2.5 等关键指标的数据，系统实现了多级验证机制，确保数据的有效性。

对于文本型数据的清洗，系统实现了文本标准化处理，将天气描述规范化为预定义的类别，如将"晴间多云"、"多云转晴"等变体统一为标准类别。同时，系统还对天气文本进行编码，便于后续机器学习模型处理。

图 5.2-1 数据清洗流程图

5.2.3 数据整合与转换

完成基础清洗后，系统通过 csv_to_db.py 脚本将处理后的 CSV 数据导入 SQLite 数据库（data.db），实现数据的集中管理。数据库设计采用规范化原则，建立了城市、天气记录、空气质量记录等相关表结构，并通过外键关系维护数据完整性。

针对机器学习模型的训练需求，系统实现了数据特征工程模块，包括特征提取、特征选择和特征变换等功能。例如，从日期中提取季节、月份、星期几等时间特征；对天气文本进行 One-Hot 编码；对温度、湿度等连续变量进行标准化处理等。这些转换后的特征数据存储在 trained_models 目录下，供模型训练使用。

数据清洗和转换完成后，系统通过数据质量仪表板提供直观的数据质量评估结果，包括完整性、准确性、一致性等维度的评分，以及异常值分布、缺失值处理统计等可视化图表，如图 5.2-2 所示。

图 5.2-2 数据质量评估仪表板

5.2.4 增量数据处理

针对定期更新的新数据，系统实现了增量数据处理机制，避免对整个数据集重复清洗。通过记录上次处理的时间戳，系统能够识别新增数据并仅对这部分数据执行清洗流程，显著提高了数据处理效率。同时，系统还维护数据处理日志，记录每次清洗操作的详细信息，便于问题追踪和处理流程优化。

通过上述数据清洗和预处理流程，本系统能够将原始爬虫数据转换为结构化、标准化、高质量的数据集，为后续的数据分析和模型训练奠定坚实基础。
