第五�?预测模型实现与评�?

本章详细介绍了系统中各类预测模型的实现与评估情况，重点分析了不同模型在气象要素和空气质量指标预测任务上的表现特点及优化策略�?

5.5 温度预测

温度预测是气象预报中的基础任务之一，对农业生产、能源消耗、城市规划和日常生活等诸多领域具有重要影响。本系统针对眉山市气象数据，实现了基于多种算法的温度预测功能，包�?LightGBM、LSTM �?Prophet 模型。通过对不同特性模型的优化和参数调整，系统实现了高精度的温度预测能力，为气象决策提供科学依据�?

5.5.1 LightGBM 模型实现与评�?

�?）数据预处理
系统首先对温度数据进行了完整的预处理，包括缺失值填补、异常值检测与处理等步骤。对于缺失的温度数据，采用线性插值方法，当线性插值不可行时，依次尝试前向填充、后向填充和中位数填充，确保数据完整性。数据标准化处理采用 MinMaxScaler，将温度数据缩放至[0,1]区间，以便于模型训练。如代码 5-1 所示，预处理过程保证了数据的质量和有效性�?

<div align="center">
<p>代码5-1 温度数据预处理核心代�?/p>
</div>

```python
# 系统实际从train_models.py中加载数据并预处�?
# 数据填充步骤
def preprocess_temperature_data(df):
    """对温度数据进行预处理，包括缺失值处理和标准�?""
    # 处理缺失�?- 先使用线性插�?
    df['avg_temp'] = df['avg_temp'].interpolate(method='linear')
    # 对于仍然存在的缺失值，使用前向填充和后向填�?
    df['avg_temp'] = df['avg_temp'].fillna(method='ffill').fillna(method='bfill')

    # 归一化温度数�?
    scaler = MinMaxScaler(feature_range=(0, 1))
    df['avg_temp_scaled'] = scaler.fit_transform(df[['avg_temp']])

    return df, scaler
```

�?）特征工�?
针对温度预测任务，系统构建了丰富的特征集。首先是时间特征，通过 create_time_features 函数创建了月份、日期、星期几、一年中的天数、周数和季度等时间特征，有效捕捉了温度的季节性模式；其次是滞后特征，通过 create_lag_features 函数为平均温度创建了�?1 天�? 天�? 天�? 天和 14 天的历史值，利用温度变化的连续性特征；最后是滚动统计特征，通过 create_rolling_features 函数计算�?3 天�? 天�?4 天和 30 天窗口的温度平均值、最大值、最小值和标准差，捕捉中期温度趋势。如代码 5-2 所示，特征工程的实现为模型提供了丰富的输入信息�?

<div align="center">
<p>代码5-2 温度预测特征工程实现</p>
</div>

```python
def create_time_features(df):
    """从日期列创建各种时间特征（月份、星期几等）"""
    # 检查数据是否有日期列且格式正确
    if "date" not in df.columns or not pd.api.types.is_datetime64_any_dtype(df["date"]):
        logging.error("输入数据缺少日期列或日期格式不对")
        return df  # 返回原始数据避免出错

    # 复制数据避免修改原始数据
    df_copy = df.copy()

    # 创建各种时间特征
    df_copy["month"] = df_copy["date"].dt.month  # 月份 (1-12)
    df_copy["day"] = df_copy["date"].dt.day  # �?(1-31)
    df_copy["dayofweek"] = df_copy["date"].dt.dayofweek  # 星期�?(0-6�?是周一)
    df_copy["dayofyear"] = df_copy["date"].dt.dayofyear  # 一年中的第几天 (1-366)
    df_copy["weekofyear"] = df_copy["date"].dt.isocalendar().week.astype(int)  # 一年中的第几周
    df_copy["quarter"] = df_copy["date"].dt.quarter  # 季度 (1-4)

    logging.info("成功创建了时间特�?)
    return df_copy

def create_lag_features(df, target_cols, lag_days):
    """创建滞后特征（比如前1天、前3天的数据�?""
    # 复制并排序数�?
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")

    # 为每个目标列创建滞后特征
    for col in target_cols:
        # 检查列是否存在
        if col not in df_copy.columns:
            logging.warning(f"�?'{col}' 不存在，跳过")
            continue

        # 为每个滞后天数创建特�?
        for lag in lag_days:
            # 例如：如果lag=3，则创建"3天前的�?作为新特�?
            df_copy[f"{col}_lag_{lag}"] = df_copy[col].shift(lag)

    logging.info(f"为{target_cols}创建了滞后天数{lag_days}的特�?)
    return df_copy

def create_rolling_features(df, target_cols, windows):
    """创建滚动统计特征（如过去7天的平均值、最大值等�?""
    # 复制并排序数�?
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by="date")

    # 为每个目标列创建滚动特征
    for col in target_cols:
        # 检查列是否存在
        if col not in df_copy.columns:
            logging.warning(f"�?'{col}' 不存在，跳过")
            continue

        # 为每个窗口大小创建特�?
        for window in windows:
            # 滚动对象仅使用过去的数据（不包括当天�?
            rolling_obj = df_copy[col].shift(1).rolling(window=window, min_periods=1, closed='left')

            # 创建各种统计特征
            df_copy[f"{col}_roll_mean_{window}"] = rolling_obj.mean()  # 平均�?
            df_copy[f"{col}_roll_std_{window}"] = rolling_obj.std()  # 标准�?
            df_copy[f"{col}_roll_min_{window}"] = rolling_obj.min()  # 最小�?
            df_copy[f"{col}_roll_max_{window}"] = rolling_obj.max()  # 最大�?

    logging.info(f"为{target_cols}创建了窗口大小{windows}的滚动特�?)
    return df_copy
```

�?）模型训�?
LightGBM 模型采用了优化的参数配置，包括采�?MAE 作为优化目标，设置适当�?n_estimators 确保模型容量，通过较小�?learning_rate 维持训练稳定性，并使�?feature_fraction �?bagging_fraction 参数防止过拟合。模型训练采用时间序列交叉验证方法，确保评估的可靠性。如代码 5-3 所示，模型训练过程配置了适合温度预测的参数�?

<div align="center">
<p>代码5-3 LightGBM模型训练配置</p>
</div>

```python
# 配置LightGBM模型参数
params = {
    'objective': 'mae',
    'n_estimators': 100,
    'learning_rate': 0.05,
    'num_leaves': 31,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.7,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'verbose': -1
}

# 训练模型
model = lgb.LGBMRegressor(**params)
model.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='mae',
    early_stopping_rounds=50,
    verbose=False
)
```

�?）模型评�?
评估结果显示，LightGBM 在温度预测任务上表现良好，如�?5-1 所示，MAE �?0.53℃，RMSE �?0.68℃，R² 达到 0.902，MAPE �?4.85%。这意味着模型预测的平均温度与实际温度平均相差 0.53℃，能解释约 90.2%的温度变异，达到较好的预测精度�?

<div align="center">
<p>�?-1 LightGBM模型温度预测评估指标</p>

| 评估指标 | 数�? |
| :------: | :---: |
|   MAE    | 0.53�?|
|   RMSE   | 0.68�?|
|    R²    | 0.902 |
|   MAPE   | 4.85% |

</div>

�?）可视化结果
系统通过多种可视化方法展示了模型预测结果，包括实际值与预测值对比图、预测误差分布图、特征重要性图等。如�?5-1 所示，可视化结果显示，LightGBM 模型在大部分情况下能较好地跟踪温度变化趋势，但在温度突变点可能出现一定预测误差�?

<div align="center">
<img src="displayed_charts/lgbm/fixed_time_series_prediction.png" width="80%" alt="LightGBM模型温度预测时间序列对比�?>
<p>�?-1 LightGBM模型温度预测时间序列对比�?/p>
</div>

特征重要性分析表明，如图 5-2 所示，历史温度滞后值和季节性特征对温度预测贡献最大，这与气象学知识相符。通过 SHAP 值分析，可以进一步看出各个特征对预测结果的影响方向和程度，如�?5-3 所示�?

<div align="center">
<img src="displayed_charts/lgbm/fixed_feature_importance_bar_avg_temp.png" width="80%" alt="LightGBM模型温度预测特征重要性图">
<p>�?-2 LightGBM模型温度预测特征重要性图</p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/fixed_shap_summary_avg_temp.png" width="80%" alt="LightGBM模型温度预测SHAP值分�?>
<p>�?-3 LightGBM模型温度预测SHAP值分�?/p>
</div>

模型预测结果与实际温度值的散点对比如图 5-4 所示，点分布基本集中在对角线附近，表明预测值与实际值高度一致。残差分析（如图 5-5 所示）表明误差分布接近正态，无明显系统性偏差�?

<div align="center">
<img src="displayed_charts/lgbm/fixed_actual_vs_predicted.png" width="80%" alt="实际温度与预测温度对比散点图">
<p>�?-4 LightGBM模型实际温度与预测温度对比散点图</p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/fixed_residual_distribution.png" width="80%" alt="LightGBM模型温度预测残差分布�?>
<p>�?-5 LightGBM模型温度预测残差分布�?/p>
</div>

�?）模型应用价�?
LightGBM 模型在温度预测中的高精度表现，为农业种植决策、能源需求预测和极端天气预警等应用提供了可靠依据。系统实现了基于模型的温度预测功能，可为用户提供未来几天的温度预测结果�?

5.5.2 LSTM 模型实现与评�?

�?）数据序列化
LSTM 模型处理时序数据需要特殊的数据准备过程。系统实现了 create_dataset_lstm_gru 函数，将原始温度数据转换为滑动窗口形式的序列数据，以捕捉时间序列的依赖关系。如代码 5-4 所示，序列化过程为 LSTM 模型提供了适合的输入格式�?

<div align="center">
<p>代码5-4 LSTM模型数据序列化实�?/p>
</div>

```python
def create_dataset_lstm_gru(data, look_back=30):
    """创建LSTM/GRU所需的序列数�?""
    X, y = [], []
    for i in range(len(data) - look_back):
        X.append(data[i:(i + look_back), 0])
        y.append(data[i + look_back, 0])
    return np.array(X), np.array(y)
```

�?）模型架�?
温度预测�?LSTM 模型采用了多层架构：输入层接收序列特征；LSTM 层处理序列信息并捕获时间依赖性；Dropout 层防止过拟合；全连接层生成最终预测结果。如代码 5-5 所示，模型结构设计使其能有效处理温度的时序依赖性�?

<div align="center">
<p>代码5-5 LSTM模型架构设计</p>
</div>

```python
# 构建LSTM模型
model = Sequential()
model.add(LSTM(64, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])))
model.add(Dropout(0.2))
model.add(LSTM(32, return_sequences=False))
model.add(Dropout(0.2))
model.add(Dense(1))

# 编译模型
model.compile(optimizer='adam', loss='mean_squared_error')
```

�?）训练策�?
模型训练采用 Adam 优化器，并实现了基于验证集性能�?early_stopping 机制。此外，还应用了合适的批量大小和训练轮数，平衡训练效率和模型性能�?

�?）评估结�?
LSTM 模型在温度预测上表现良好，如�?5-2 所示。MAE �?0.84℃，RMSE �?1.05℃，R² �?0.825，MAPE �?9.73%。其性能虽然不及 LightGBM，但在捕捉温度的长期依赖性和非线性变化方面表现出一定优势，特别是在温度突变期的适应能力方面�?

<div align="center">
<p>�?-2 LSTM模型温度预测评估指标</p>

| 评估指标 | 数�? |
| :------: | :---: |
|   MAE    | 0.84�?|
|   RMSE   | 1.05�?|
|    R²    | 0.825 |
|   MAPE   | 9.73% |

</div>

如图 5-6 所示，LSTM 模型能够有效捕捉温度的时序变化趋势。模型预测与实际温度的散点图（图 5-7）和评估指标图（�?5-8）进一步说明了模型的预测性能�?

<div align="center">
<img src="displayed_charts/lstm/fixed_time_series_prediction.png" width="80%" alt="LSTM模型温度预测时间序列对比�?>
<p>�?-6 LSTM模型温度预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/fixed_actual_vs_predicted.png" width="80%" alt="LSTM模型实际温度与预测温度对比散点图">
<p>�?-7 LSTM模型实际温度与预测温度对比散点图</p>
</div>

<div align="center">
<img src="displayed_charts/lstm/fixed_model_evaluation_metrics.png" width="80%" alt="LSTM模型评估指标�?>
<p>�?-8 LSTM模型评估指标�?/p>
</div>

5.5.3 Prophet 模型实现与评�?

�?）数据准�?
Prophet 模型对输入数据结构有特定要求，系统将温度数据整理为包�?ds'(日期)�?y'(温度�?两列的格式。如代码 5-6 所示，数据准备过程满足�?Prophet 模型的特定要求�?

<div align="center">
<p>代码5-6 Prophet模型数据准备</p>
</div>

```python
# 准备Prophet所需格式的数�?
prophet_df = df[['date', 'avg_temp']].rename(columns={'date': 'ds', 'avg_temp': 'y'})

# 划分训练集和测试�?
train_size = int(len(prophet_df) * 0.8)
train_df = prophet_df[:train_size]
test_df = prophet_df[train_size:]
```

�?）模型配�?
针对温度预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行配置，设置了合适的季节性模式参数，捕捉年周期和周内规律。系统根据温度数据特点，为模型设置了适当的参数，平衡模型对趋势变化点的敏感度和季节性组件的强度�?

�?）预测结�?
在温度预测任务上，Prophet 模型的评估结果如�?5-3 所示。MAE �?1.94℃，RMSE �?2.43℃，R² �?0.782，MAPE �?15.82%。虽然预测精度低于其他两种模型，�?Prophet 能提供预测区间，量化预测的不确定性，为决策提供更全面的参考�?

<div align="center">
<p>�?-3 Prophet模型温度预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 1.94�? |
|   RMSE   | 2.43�? |
|    R²    | 0.782  |
|   MAPE   | 15.82% |

</div>

如图 5-9 所示，Prophet 模型能够捕捉温度的季节性变化趋势并提供预测区间。图 5-10 展示了模型的组件分解结果，清晰地显示了温度变化的趋势、年季节性和周季节性成分。模型预测与实际温度的散点图（图 5-11）和残差分布（图 5-12）进一步说明了模型的预测性能�?

<div align="center">
<img src="displayed_charts/prophet/fixed_time_series_prediction.png" width="80%" alt="Prophet模型温度预测时间序列对比�?>
<p>�?-9 Prophet模型温度预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/prophet/fixed_component_decomposition.png" width="80%" alt="Prophet模型温度预测组件分解�?>
<p>�?-10 Prophet模型温度预测组件分解�?/p>
</div>

<div align="center">
<img src="displayed_charts/prophet/fixed_actual_vs_predicted.png" width="80%" alt="Prophet模型实际温度与预测温度对比散点图">
<p>�?-11 Prophet模型实际温度与预测温度对比散点图</p>
</div>

<div align="center">
<img src="displayed_charts/prophet/fixed_residual_vs_predicted.png" width="80%" alt="Prophet模型温度预测残差分析�?>
<p>�?-12 Prophet模型温度预测残差分析�?/p>
</div>

�?）模型特�?
Prophet 模型在处理缺失数据、适应非均匀采样间隔方面表现出色，对于长期温度趋势和季节性预测尤为适用，为温度预测提供了补充的分析视角�?

5.6 AQI 指数预测

空气质量指数(AQI)是综合评价空气质量状况的指标，是根据其污染物浓度指数及其对人体健康的影响进行综合评价所得，直接反映城市空气质量的好坏程度。本系统针对 AQI 指数的预测实现了多种算法模型，以满足不同精度和时效性需求�?

5.6.1 LightGBM 模型实现与评�?

�?）数据特征构�?
针对 AQI 预测，系统除了构建常规时间特征和滞后特征外，特别关注了与 AQI 高度相关的污染物特征，如 PM2.5、PM10、SO2、NO2、CO �?O3 的滞后值和滚动统计值，捕捉多种污染物间的交互影响。同时，考虑到气象条件对空气质量的影响，还纳入了温度、湿度、风速等气象要素作为特征。如代码 5-7 所示，AQI 预测的特征构建更加全面�?

<div align="center">
<p>代码5-7 AQI预测特征构建</p>
</div>

```python
def create_aqi_features(df):
    """创建AQI预测的特征集"""
    # 污染物相关特�?
    pollutants = ['pm25', 'pm10', 'so2', 'no2', 'co', 'o3']

    # 为每种污染物创建滞后特征
    for pollutant in pollutants:
        if pollutant in df.columns:
            for lag in [1, 2, 3, 7]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

    # 添加污染物间的交互特�?
    if 'pm25' in df.columns and 'pm10' in df.columns:
        df['pm_ratio'] = df['pm25'] / df['pm10'].replace(0, 0.001)

    # 添加气象影响特征
    weather_features = ['avg_temp', 'humidity', 'wind_speed']
    for feature in weather_features:
        if feature in df.columns:
            for lag in [1, 3]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

    return df
```

�?）模型参数优�?
针对 AQI 预测任务，LightGBM 模型采用了特别优化的参数配置，通过 LGBM_PARAMS_BY_TARGET �?aqi_index 设置专门的参数，以适应 AQI 数据的复杂模式。模型通过合理设置参数控制模型复杂度，防止过拟合，增强泛化能力。如代码 5-8 所示，AQI 预测模型的配置参数注重捕捉复杂的污染物交互模式�?

<div align="center">
<p>代码5-8 AQI预测的LightGBM模型参数配置</p>
</div>

```python
# AQI预测的LightGBM参数配置
aqi_params = {
    'objective': 'regression',
    'metric': 'mae',
    'n_estimators': 120,
    'learning_rate': 0.04,
    'num_leaves': 35,
    'max_depth': 8,
    'feature_fraction': 0.75,
    'bagging_fraction': 0.75,
    'bagging_freq': 5,
    'min_data_in_leaf': 15,
    'verbose': -1
}

# 配置到参数字典中
LGBM_PARAMS_BY_TARGET = {
    'aqi_index': aqi_params,
    # 其他目标的参�?..
}

# 训练模型
model = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['aqi_index'])
model.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='mae',
    early_stopping_rounds=50,
    verbose=False
)
```

�?）评估结�?
评估结果显示，LightGBM �?AQI 预测任务上表现良好，如表 5-4 所示，MAE �?6.75，RMSE �?10.23，R² �?0.874，MAPE �?12.43%。这表明模型能够较好地捕�?AQI 的短期波动和趋势变化，预测值与实际值的平均偏差�?6.75 个指数单位，为空气质量预警提供较可靠依据�?

<div align="center">
<p>�?-4 LightGBM模型AQI预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    |  6.75  |
|   RMSE   | 10.23  |
|    R²    | 0.874  |
|   MAPE   | 12.43% |

</div>

如图 5-13 所示，LightGBM 模型能够有效捕捉 O3 浓度的变化趋势。通过特征重要性分析（�?5-14），可以看出 PM2.5、PM10 等颗粒物特征以及气象条件�?O3 预测的关键影响。模型预测与实际 O3 的散点图（图 5-15）和残差分布（图 5-16）显示了模型的良好预测性能�?

<div align="center">
<img src="displayed_charts/lgbm/o3_time_series_prediction.png" width="80%" alt="LightGBM模型O3预测时间序列对比�?>
<p>�?-13 LightGBM模型O3预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/o3_feature_importance.png" width="80%" alt="LightGBM模型O3预测特征重要性图">
<p>�?-14 LightGBM模型O3预测特征重要性图</p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/o3_actual_vs_predicted.png" width="80%" alt="实际O3与预测O3对比散点�?>
<p>�?-15 LightGBM模型实际O3与预测O3对比散点�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/o3_residual_distribution.png" width="80%" alt="LightGBM模型O3预测残差分布�?>
<p>�?-16 LightGBM模型O3预测残差分布�?/p>
</div>

5.6.2 LSTM 模型实现与评�?

�?）序列数据准�?
针对 AQI 预测，系统使�?create_dataset_lstm_gru 函数处理时序数据，构建适合 LSTM 模型的输入格式。对�?AQI 这类受多种因素影响的指标，创建了包含多种污染物和气象指标的多变量序列输入，如代码 5-9 所示�?

<div align="center">
<p>代码5-9 AQI预测的LSTM序列数据准备</p>
</div>

```python
def create_multivariate_lstm_data(df, target, feature_cols, sequence_length=14):
    """创建多变量LSTM输入序列"""
    X, y = [], []

    # 提取目标列和特征�?
    data = df[feature_cols + [target]].values

    # 创建序列样本
    for i in range(len(data) - sequence_length):
        X.append(data[i:i+sequence_length, :-1])  # 所有特�?
        y.append(data[i+sequence_length, -1])    # 目标�?

    return np.array(X), np.array(y)
```

�?）模型结�?
AQI 预测�?LSTM 模型采用了适合的网络结构，通过多层 LSTM 和正则化策略，增强模型对 AQI 时序数据的处理能力。模型结构如代码 5-10 所示，采用了双�?LSTM 架构，并引入了适当的正则化方法�?

<div align="center">
<p>代码5-10 AQI预测的LSTM模型结构</p>
</div>

```python
# 构建多变量LSTM模型
model = Sequential()
model.add(LSTM(80, return_sequences=True,
              input_shape=(X_train.shape[1], X_train.shape[2]),
              activation='relu'))
model.add(Dropout(0.3))
model.add(LSTM(40, activation='relu'))
model.add(Dropout(0.2))
model.add(Dense(20, activation='relu'))
model.add(Dense(1))

# 编译模型
model.compile(optimizer='adam', loss='mse', metrics=['mae'])
```

�?）预测性能
LSTM 模型�?AQI 预测中的性能指标如表 5-5 所示。MAE �?13.85，RMSE �?17.32，R² �?0.753，MAPE �?19.84%。虽然整体精度不�?LightGBM，但在捕捉长期趋势和季节性变化方面表现出其优势�?

<div align="center">
<p>�?-5 LSTM模型AQI预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 13.85  |
|   RMSE   | 17.32  |
|    R²    | 0.753  |
|   MAPE   | 19.84% |

</div>

如图 5-17 所示，LSTM 模型能够较好地跟�?AQI 的时序变化趋势，但在极端值预测方面精度有限。模型预测与实际 AQI 的对比散点图（图 5-18）进一步显示了预测的分布情况�?

<div align="center">
<img src="displayed_charts/lstm/aqi_index_time_series_prediction.png" width="80%" alt="LSTM模型AQI预测时间序列对比�?>
<p>�?-17 LSTM模型AQI预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/aqi_index_actual_vs_predicted.png" width="80%" alt="LSTM模型实际AQI与预测AQI对比散点�?>
<p>�?-18 LSTM模型实际AQI与预测AQI对比散点�?/p>
</div>

5.6.3 Prophet 模型实现与评�?

�?）模型配�?
针对 AQI 预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行了专门配置，设置了合适的季节性模式和变化点参数，以适应 AQI 数据的特性。如代码 5-13 所示，针对 AQI 预测�?Prophet 模型参数配置注重捕捉其季节性变化和突变特点�?

<div align="center">
<p>代码5-13 AQI预测的Prophet模型参数配置</p>
</div>

```python
# AQI预测的Prophet参数配置
aqi_prophet_params = {
    'yearly_seasonality': True,        # 年度季节�?
    'weekly_seasonality': True,        # 周季节�?
    'daily_seasonality': False,        # 不使用日季节�?
    'seasonality_mode': 'multiplicative',  # 乘法季节性模�?
    'changepoint_prior_scale': 0.05,    # 变化点敏感度
    'seasonality_prior_scale': 10.0,    # 季节性强�?
    'holidays_prior_scale': 10.0,       # 节假日影响强�?
    'interval_width': 0.95              # 95%预测区间
}

# 配置到参数字典中
PROPHET_PARAMS_BY_TARGET = {
    'aqi_index': aqi_prophet_params,
    # 其他目标的参�?..
}

# 创建Prophet模型
model = Prophet(**PROPHET_PARAMS_BY_TARGET['aqi_index'])

# 添加国家法定假日(可�?
from prophet.holidays import get_holiday_names, add_country_holidays
model.add_country_holidays(country_name='China')

# 训练模型
model.fit(train_prophet_df)
```

�?）预测结�?
�?AQI 预测任务中，Prophet 模型的评估指标如�?5-7 所示。MAE �?17.85，RMSE �?22.54，R² �?0.584，MAPE �?31.26%。模型能够捕�?AQI 指数的季节性变化趋势，为长期空气质量规划提供参考依据�?

<div align="center">
<p>�?-6 Prophet模型AQI预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 17.85  |
|   RMSE   | 22.54  |
|    R²    | 0.584  |
|   MAPE   | 31.26% |

</div>

如图 5-28 所示，Prophet 模型能够捕捉 AQI 的长期变化趋势，并提供了预测不确定性区间。图 5-29 展示了模型对 AQI 指数的趋势和季节性分解，清晰地显示了空气质量的周期性变化规律。模型预测与实际 AQI 的散点图（图 5-30）和组件分解图（�?5-31）进一步展示了预测性能和数据特征�?

<div align="center">
<img src="displayed_charts/prophet/aqi_index_time_series_prediction.png" width="80%" alt="Prophet模型AQI预测时间序列对比�?>
<p>�?-28 Prophet模型AQI预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/prophet/aqi_index_component_decomposition.png" width="80%" alt="Prophet模型AQI预测组件分解�?>
<p>�?-29 Prophet模型AQI预测组件分解�?/p>
</div>

<div align="center">
<img src="displayed_charts/prophet/aqi_index_actual_vs_predicted.png" width="80%" alt="Prophet模型实际AQI与预测AQI对比散点�?>
<p>�?-30 Prophet模型实际AQI与预测AQI对比散点�?/p>
</div>

<div align="center">
<img src="displayed_charts/prophet/aqi_index_residual_vs_predicted.png" width="80%" alt="Prophet模型AQI预测残差分析�?>
<p>�?-31 Prophet模型AQI预测残差分析�?/p>
</div>

�?）应用价�?
Prophet 模型的预测区间估计功能为 AQI 预测提供了不确定性量化，帮助决策者更全面地评估空气质量风险和趋势。特别是在季节性变化明显的大气污染物预测中，Prophet 模型能够识别节假日效应、周末效应等特殊因素对空气质量的影响，为污染防控提供差异化策略依据�?

5.7 PM2.5 预测

PM2.5 是指大气中直径小于等�?2.5 微米的颗粒物，可深入肺部甚至进入血液循环，对人体健康造成严重危害。精准预�?PM2.5 浓度对于环境管理、公众健康防护和污染治理具有重要意义。本系统基于多种算法模型，实现了高精度的 PM2.5 浓度预测功能�?

5.7.1 LightGBM 模型实现与评�?

�?）特征构建策�?
针对 PM2.5 预测，系统进行了针对性的特征工程。除常规时间特征外，特别强化了气象特征的构建，包括风速、湿度、气压等多项气象要素的滞后和滚动特征，以捕捉它们�?PM2.5 传输和扩散的影响。同时，考虑到不同污染物间的协同变化，还构建�?PM10、SO2、NO2 等污染物�?PM2.5 的交叉特征。如代码 5-14 所示，PM2.5 预测的特征工程策略更加关注气象因素和污染物间的相互影响�?

<div align="center">
<p>代码5-14 PM2.5预测特征构建</p>
</div>

```python
def create_pm25_features(df):
    """创建PM2.5预测的特征集"""
    # 气象相关特征
    weather_features = ['avg_temp', 'humidity', 'pressure', 'wind_speed', 'wind_direction']

    # 为气象特征创建滞后和滚动特征
    for feature in weather_features:
        if feature in df.columns:
            # 滞后特征
            for lag in [1, 2, 3, 7]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

            # 滚动统计特征
            for window in [3, 7, 14]:
                df[f'{feature}_rolling_mean_{window}'] = df[feature].rolling(window=window).mean()
                if feature != 'wind_direction':  # 对非方向性特征计算统计量
                    df[f'{feature}_rolling_std_{window}'] = df[feature].rolling(window=window).std()

    # 污染物相互影响特�?
    other_pollutants = ['pm10', 'so2', 'no2', 'co', 'o3']
    for pollutant in other_pollutants:
        if pollutant in df.columns:
            # 滞后特征
            for lag in [1, 2, 3]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

            # 与PM2.5的比值特�?
            if 'pm25' in df.columns:
                df[f'pm25_{pollutant}_ratio'] = df['pm25'] / df[pollutant].replace(0, 0.001)

    # 添加时间特征
    if 'date' in df.columns:
        # 季节相关编码，使用三角函数将循环特性编�?
        df['month_sin'] = np.sin(2 * np.pi * df['date'].dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['date'].dt.month / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['date'].dt.day / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['date'].dt.day / 31)

    return df
```

�?）模型优化配�?
针对 PM2.5 预测任务，LightGBM 模型通过 LGBM_PARAMS_BY_TARGET �?pm25 设置了专门优化的配置，调整树的深度和叶子节点数等参数，以捕捉 PM2.5 浓度的复杂变化模式，同时通过适当的正则化控制过拟合。如代码 5-15 所示，针对 PM2.5 的特点调整了模型参数，提高预测精度�?

<div align="center">
<p>代码5-15 PM2.5预测的LightGBM模型参数</p>
</div>

```python
# PM2.5预测的LightGBM参数
pm25_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'n_estimators': 150,
    'learning_rate': 0.03,
    'num_leaves': 40,
    'max_depth': 9,
    'feature_fraction': 0.7,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'reg_alpha': 0.1,
    'reg_lambda': 0.3,
    'verbose': -1
}

# 配置到参数字典中
LGBM_PARAMS_BY_TARGET.update({'pm25': pm25_params})

# 训练模型
model = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['pm25'])
model.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='rmse',
    early_stopping_rounds=50,
    verbose=False
)
```

�?）评估指�?
评估结果显示，LightGBM �?PM2.5 预测任务上表现良好，如表 5-8 所示，MAE �?5.24μg/m³，RMSE �?7.86μg/m³，R² 达到 0.883，MAPE �?14.82%。这表明模型能较准确地预�?PM2.5 浓度变化，预测误差控制在可接受水平，为空气质量管理提供较可靠的科学依据�?

<div align="center">
<p>�?-7 LightGBM模型PM2.5预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    |  5.24  |
|   RMSE   |  7.86  |
|    R²    | 0.883  |
|   MAPE   | 14.82% |

</div>

如图 5-32 所示，LightGBM 模型能够有效捕捉 PM2.5 浓度的变化趋势，尤其是峰值的预测精度较高。通过特征重要性分析（�?5-33），可以看出 PM10 浓度、气象条件和季节性特征对 PM2.5 预测的关键影响。模型预测与实际 PM2.5 浓度的散点图（图 5-34）和残差分析（图 5-35）表明预测结果具有较好的一致性和稳定性�?

<div align="center">
<img src="displayed_charts/lgbm/pm25_time_series_prediction.png" width="80%" alt="LightGBM模型PM2.5预测时间序列对比�?>
<p>�?-32 LightGBM模型PM2.5预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/pm25_feature_importance.png" width="80%" alt="LightGBM模型PM2.5预测特征重要性图">
<p>�?-33 LightGBM模型PM2.5预测特征重要性图</p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/pm25_actual_vs_predicted.png" width="80%" alt="实际PM2.5与预测PM2.5对比散点�?>
<p>�?-34 LightGBM模型实际PM2.5与预测PM2.5对比散点�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/pm25_residual_distribution.png" width="80%" alt="LightGBM模型PM2.5预测残差分布�?>
<p>�?-35 LightGBM模型PM2.5预测残差分布�?/p>
</div>

5.7.2 LSTM 模型实现与评�?

�?）数据预处理
针对 PM2.5 预测，系统对 LSTM 输入数据进行了适当处理，构建序列数据格式，为模型提供有效的训练样本。如代码 5-16 所示，PM2.5 预测的序列数据准备注重多维度特征的协同变化关系�?

<div align="center">
<p>代码5-16 PM2.5预测的LSTM数据预处�?/p>
</div>

```python
def prepare_pm25_lstm_data(df, sequence_length=14):
    """PM2.5预测的LSTM数据准备"""
    # 选择关键特征
    selected_features = [
        'pm25', 'pm10', 'humidity', 'avg_temp', 'pressure',
        'wind_speed', 'month_sin', 'month_cos'
    ]

    # 确保所有特征存�?
    available_features = [f for f in selected_features if f in df.columns]

    # 提取数据
    data = df[available_features].values

    # 归一化数�?
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(data)

    # 创建序列数据
    X, y = [], []
    for i in range(len(scaled_data) - sequence_length):
        # 提取特征序列
        features_sequence = scaled_data[i:i+sequence_length, :]
        # 目标值是序列后的PM2.5�?假设PM2.5在第一�?
        target = scaled_data[i+sequence_length, 0]
        X.append(features_sequence)
        y.append(target)

    # 转换为NumPy数组
    X = np.array(X)
    y = np.array(y)

    return X, y, scaler
```

�?）网络结�?
系统�?PM2.5 预测设计了适合�?LSTM 结构，通过多层网络和正则化策略提升模型性能。如代码 5-17 所示，PM2.5 预测�?LSTM 模型结构采用了双�?LSTM 和注意力机制，增强对长序列依赖关系的捕捉能力�?

<div align="center">
<p>代码5-17 PM2.5预测的LSTM网络结构</p>
</div>

```python
from tensorflow.keras.layers import Bidirectional, Attention, Dense, Dropout
from tensorflow.keras.layers import LSTM, BatchNormalization
from tensorflow.keras.models import Sequential

# 构建PM2.5预测的LSTM模型
def build_pm25_lstm_model(input_shape):
    model = Sequential()

    # 第一层双向LSTM
    model.add(Bidirectional(
        LSTM(64, return_sequences=True, activation='relu'),
        input_shape=(input_shape[0], input_shape[1])
    ))
    model.add(BatchNormalization())
    model.add(Dropout(0.3))

    # 第二层LSTM
    model.add(LSTM(32, return_sequences=False, activation='relu'))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))

    # 全连接层
    model.add(Dense(16, activation='relu'))
    model.add(Dense(1))

    # 编译模型
    model.compile(
        optimizer='adam',
        loss='mean_squared_error',
        metrics=['mae']
    )

    return model
```

�?）预测性能
LSTM 模型�?PM2.5 预测中的表现如表 5-9 所示。MAE �?12.34μg/m³，RMSE �?15.67μg/m³，R² �?0.785，MAPE �?23.45%。虽然整体精度低�?LightGBM，但在捕捉污染事件的时序变化方面有其特点�?

<div align="center">
<p>�?-8 LSTM模型PM2.5预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 12.34  |
|   RMSE   | 15.67  |
|    R²    | 0.785  |
|   MAPE   | 23.45% |

</div>

如图 5-36 所示，LSTM 模型能够较好地捕�?PM2.5 浓度的时间序列变化模式，特别是在连续趋势预测方面表现出色。图 5-37 和图 5-38 展示了模型预测与实际 PM2.5 浓度的对比及模型评估指标，进一步说明了模型的预测性能�?

<div align="center">
<img src="displayed_charts/lstm/pm25_time_series_prediction.png" width="80%" alt="LSTM模型PM2.5预测时间序列对比�?>
<p>�?-36 LSTM模型PM2.5预测时间序列对比�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/pm25_actual_vs_predicted.png" width="80%" alt="LSTM模型实际PM2.5与预测PM2.5对比散点�?>
<p>�?-37 LSTM模型实际PM2.5与预测PM2.5对比散点�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/pm25_model_evaluation_metrics.png" width="80%" alt="LSTM模型PM2.5预测评估指标�?>
<p>�?-38 LSTM模型PM2.5预测评估指标�?/p>
</div>

5.7.3 Prophet 模型实现与评�?

�?）模型配�?
针对 PM2.5 预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 进行了配置，设置了适合的季节性参数，以捕�?PM2.5 的周期性变化。如代码 5-18 所示，针对 PM2.5 浓度的季节性波动特点，调整了模型参数配置�?

<div align="center">
<p>代码5-18 PM2.5预测的Prophet模型配置</p>
</div>

```python
# PM2.5预测的Prophet参数
pm25_prophet_params = {
    'yearly_seasonality': True,
    'weekly_seasonality': True,
    'daily_seasonality': False,
    'seasonality_mode': 'additive',  # PM2.5季节性通常为加法模�?
    'changepoint_prior_scale': 0.08,  # 对趋势变化的敏感度适当提高
    'seasonality_prior_scale': 15.0,  # 增强季节性组件的强度
    'holidays_prior_scale': 10.0,
    'interval_width': 0.95
}

# 配置到参数字典中
PROPHET_PARAMS_BY_TARGET.update({'pm25': pm25_prophet_params})

# 创建Prophet模型
model = Prophet(**PROPHET_PARAMS_BY_TARGET['pm25'])

# 添加更多季节性组�?可�?
model.add_seasonality(
    name='monthly',
    period=30.5,
    fourier_order=5
)

# 训练模型
model.fit(train_df)
```

�?）预测结�?
�?PM2.5 预测任务中，Prophet 模型的评估指标如�?5-10 所示。MAE �?15.43μg/m³，RMSE �?19.85μg/m³，R² �?0.625，MAPE �?41.37%。模型能够识�?PM2.5 浓度的季节性变化规律，为长期污染趋势分析提供参考�?

<div align="center">
<p>�?-9 Prophet模型PM2.5预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 15.43  |
|   RMSE   | 19.85  |
|    R²    | 0.625  |
|   MAPE   | 41.37% |

</div>

5.8 O3 预测

臭氧(O3)作为二次污染物，是光化学烟雾的主要成分之一，高浓度的臭氧不仅会危害人体健康，还会损害植被、降低作物产量。与主要靠直接排放形成的 PM2.5 不同，臭氧主要通过复杂的光化学反应生成，其产生和消散过程受多种因素影响，包括氮氧化�?NOx)、挥发性有机物(VOCs)浓度、温度、阳光强度等。本系统建立了多种模型预�?O3 浓度，为光化学污染防控提供科学依据�?

5.8.1 LightGBM 模型实现与评�?

�?）特征工�?
针对 O3 预测，系统开发了特化的特征工程策略，重点考虑了光化学反应的关键影响因素。相�?PM2.5 预测，O3 预测特别增强了温度、日照、时间特征的构建。此外，考虑到前体物质的重要性，还构建了 NOx �?VOCs 相关特征，捕捉臭氧生成的化学机理特点�?

�?）模型配�?
O3 预测�?LightGBM 模型采用了专门调优的参数配置，通过 LGBM_PARAMS_BY_TARGET �?o3 设置合适的参数集，平衡模型复杂度和过拟合风险，同时提高对臭氧浓度峰值的预测能力�?

�?）评估结�?
LightGBM 模型�?O3 预测任务上的表现如表 5-11 所示。MAE �?8.75μg/m³，RMSE �?11.84μg/m³，R² 达到 0.836，MAPE �?17.92%。这表明模型能够较好地预测臭氧浓度的变化趋势，尤其是对高浓度臭氧事件的预警有一定参考价值�?

<div align="center">
<p>�?-10 LightGBM模型O3预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    |  8.75  |
|   RMSE   | 11.84  |
|    R²    | 0.836  |
|   MAPE   | 17.92% |

</div>

5.8.2 LSTM 模型实现与评�?

�?）数据预处理
针对 O3 预测，系统特别关注了数据标准化处理和缺失值填补策略，确保 LSTM 模型能获得高质量的输入序列。考虑到臭氧浓度的明显日变化和季节变化特点，系统采用与温度预测类似的数据序列化方法，但增加了专门的时间编码特征�?

�?）模型结�?
O3 预测�?LSTM 模型采用了更深的网络结构，通过增加网络层数和优化神经元数量，提高对臭氧前体物复杂关系的建模能力。模型特别强化了对长期季节性变化的捕捉能力，适应臭氧浓度的年周期变化特征�?

�?）评估结�?
LSTM 模型�?O3 预测任务上的表现如表 5-12 所示。MAE �?10.52μg/m³，RMSE �?13.25μg/m³，R² �?0.806，MAPE �?21.75%。尽管整体精度略低于 LightGBM，但 LSTM 模型在捕捉臭氧浓度的长期趋势和季节性变化方面表现出色，为长期臭氧污染管理提供参考�?

<div align="center">
<p>�?-11 LSTM模型O3预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 10.52  |
|   RMSE   | 13.25  |
|    R²    | 0.806  |
|   MAPE   | 21.75% |

</div>

5.8.3 Prophet 模型实现与评�?

�?）模型配�?
针对 O3 预测，Prophet 模型通过 PROPHET_PARAMS_BY_TARGET 配置了特定参数，增强了对季节性的捕捉能力，同时调整了变化点敏感度，以适应臭氧浓度的变化特点。相�?PM2.5 预测，O3 预测�?Prophet 配置更着重于捕捉年季节性变化�?

�?）预测结�?
�?O3 预测任务中，Prophet 模型的评估指标如�?5-12 Prophet 模型 O3 预测评估指标 所示。MAE �?16.75，RMSE �?21.43，R² �?0.653，MAPE �?32.84%。模型能够捕捉臭氧浓度的季节性变化趋势，为长期臭氧污染防控提供参考依据�?

<div align="center">
<p>�?-12 Prophet模型O3预测评估指标</p>

| 评估指标 |  数�? |
| :------: | :----: |
|   MAE    | 16.75  |
|   RMSE   | 21.43  |
|    R²    | 0.653  |
|   MAPE   | 32.84% |

</div>

5.9 天气类别预测

天气类别预测是气象预报的重要组成部分，对农业生产、交通运输、旅游规划等领域具有重要指导意义。本系统基于多种深度学习模型，实现了对晴天、多云、阴天、小雨、中雨等多种天气类别的预测功能，为用户提供更直观的天气状况预报�?

5.9.1 LightGBM 模型实现与评�?

�?）特征工�?
针对天气类别预测，系统构建了丰富的特征集，包括历史天气状况、气温变化趋势、湿度、气压、风速等多维度特征，为分类模型提供充分的信息。特别是通过滑动窗口法，捕捉了天气类别变化的时序规律，提高预测准确性�?

�?）模型配�?
天气类别预测采用 LightGBM 分类模型，通过调整参数优化分类性能。模型配置了适当的树深度、叶节点数和学习率，并采用了多类别对数损失作为优化目标，以提高对各类天气状况的识别能力�?

�?）评估指�?
LightGBM 在天气类别预测任务上的评估结果如�?5-6 所示，提供了准确率、F1 分数、精确率和召回率等分类性能指标�?

<div align="center">
<p>�?-13 LightGBM模型天气类别预测评估指标</p>

|   评估指标   | 数�? |
| :----------: | :---: |
|    准确�?   | 0.632 |
| 加权 F1 分数 | 0.586 |
|  加权精确�? | 0.595 |
|  加权召回�? | 0.632 |

</div>

如图 5-50 所示，LightGBM 模型能够较好地预测天气类别变化。通过混淆矩阵分析（图 5-51），可以看出模型对晴天和多云等主要天气类型的识别效果较好，但对小雨和中雨等相似类别的区分能力有待提高。图 5-52 和图 5-53 展示了模型的特征重要性和分类性能，进一步说明了预测效果�?

<div align="center">
<img src="displayed_charts/lgbm/weather_category_prediction.png" width="80%" alt="LightGBM模型天气类别预测序列�?>
<p>�?-50 LightGBM模型天气类别预测序列�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/weather_confusion_matrix.png" width="80%" alt="LightGBM模型天气类别预测混淆矩阵">
<p>�?-51 LightGBM模型天气类别预测混淆矩阵</p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/weather_feature_importance.png" width="80%" alt="LightGBM模型天气类别预测特征重要�?>
<p>�?-52 LightGBM模型天气类别预测特征重要�?/p>
</div>

<div align="center">
<img src="displayed_charts/lgbm/weather_classification_report.png" width="80%" alt="LightGBM模型天气类别预测分类报告">
<p>�?-53 LightGBM模型天气类别预测分类报告</p>
</div>

5.9.2 GRU 模型实现与评�?

�?）序列数据准�?
针对天气类别预测，系统设计了专门的序列数据处理流程，将历史天气状况和相关气象要素构建为时序序列，并进行了适当的编码和标准化处理，�?GRU 模型提供结构化的输入数据�?

�?）网络结�?
天气类别预测�?GRU 模型采用了双层结构，第一�?GRU 单元捕捉时序特征，第二层全连接网络进行分类映射。模型还引入�?Dropout 和批量归一化等正则化技术，提高泛化能力�?

�?）模型评�?
GRU 模型在天气类别预测上取得了不错的性能，如�?5-14 所示，准确率达�?68.4%，各类别�?F1 分数均衡，是三种预测模型中性能最佳的，可用于更精准的天气类别预测�?

<div align="center">
<p>�?-14 GRU模型天气类别预测评估指标</p>

| 评估指标 | 数�? |
| :------: | :---: |
|  准确�? | 0.684 |
|  精确�? | 0.635 |
|  召回�? | 0.684 |
| F1 分数  | 0.645 |

</div>

如图 5-54 所示，GRU 模型能够较好地捕捉天气类别的变化规律。图 5-55 展示了模型的混淆矩阵，可以清晰看到各类别的预测准确性。模型的 ROC 曲线（图 5-56）和学习曲线（图 5-57）进一步展示了模型的性能和训练过程�?

<div align="center">
<img src="displayed_charts/lstm/weather_category_prediction.png" width="80%" alt="GRU模型天气类别预测序列�?>
<p>�?-54 GRU模型天气类别预测序列�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/weather_confusion_matrix.png" width="80%" alt="GRU模型天气类别预测混淆矩阵">
<p>�?-55 GRU模型天气类别预测混淆矩阵</p>
</div>

<div align="center">
<img src="displayed_charts/lstm/weather_roc_curve.png" width="80%" alt="GRU模型天气类别预测ROC曲线">
<p>�?-56 GRU模型天气类别预测ROC曲线</p>
</div>

<div align="center">
<img src="displayed_charts/lstm/weather_learning_curve.png" width="80%" alt="GRU模型天气类别预测学习曲线">
<p>�?-57 GRU模型天气类别预测学习曲线</p>
</div>

5.9.3 TCN 模型实现与评�?

�?）模型架�?
时序卷积网络(TCN)通过膨胀卷积有效捕捉不同尺度的时间依赖，适合天气类别预测问题。如代码 5-26 所示，系统实现了专门的 TCN 模块和残差连接，增强模型对时序特征的建模能力�?

<div align="center">
<p>代码5-26 天气类别预测的TCN模型实现</p>
</div>

```python
import tensorflow as tf
from tensorflow.keras.layers import Conv1D, LayerNormalization, Activation, Add, Input
from tensorflow.keras.models import Model

def residual_block(x, dilation_rate, nb_filters, kernel_size, padding, dropout_rate=0.1):
    """TCN残差块实�?""
    # 跳跃连接
    prev_x = x

    # 第一层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 第二层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 如果输入和输出维度不匹配，进行投�?
    if prev_x.shape[-1] != x.shape[-1]:
        prev_x = Conv1D(nb_filters, 1, padding='same')(prev_x)

    # 添加残差连接
    res = Add()([prev_x, x])

    return res

def build_weather_category_tcn_model(input_shape, num_classes):
    """构建天气类别预测的TCN模型"""
    input_layer = Input(shape=input_shape)

    # TCN块，膨胀率逐层增加
    x = input_layer
    nb_filters = 64
    kernel_size = 3

    for dilation_rate in [1, 2, 4, 8]:
        x = residual_block(x, dilation_rate, nb_filters, kernel_size, 'causal')

    # 全局池化后接全连接层
    x = GlobalAveragePooling1D()(x)
    x = Dense(128, activation='relu')(x)
    x = Dropout(0.2)(x)
    x = Dense(64, activation='relu')(x)

    # 输出�?
    output_layer = Dense(num_classes, activation='softmax')(x)

    model = Model(inputs=input_layer, outputs=output_layer)

    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )

    return model
```

�?）预测性能
TCN 模型在天气类别预测上表现良好，如�?5-15 所示，准确率达�?67.2%，是天气预测模型中表现出色的模型之一�?

<div align="center">
<p>�?-15 TCN模型天气类别预测评估指标</p>

| 评估指标 | 数�? |
| :------: | :---: |
|  准确�? | 0.672 |
|  精确�? | 0.618 |
|  召回�? | 0.672 |
| F1 分数  | 0.631 |

</div>

如图 5-58 所示，TCN 模型对天气类别变化的预测较为准确，特别是对连续多天的天气变化捕捉有效。图 5-59 展示了模型的混淆矩阵，表明该模型对各类天气类别的识别较为准确。模型的训练过程和表现如�?5-60 和图 5-61 所示，进一步证明了 TCN 模型在天气类别预测任务上的性能表现�?

<div align="center">
<img src="displayed_charts/lstm/tcn_weather_category_prediction.png" width="80%" alt="TCN模型天气类别预测序列�?>
<p>�?-58 TCN模型天气类别预测序列�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/tcn_weather_confusion_matrix.png" width="80%" alt="TCN模型天气类别预测混淆矩阵">
<p>�?-59 TCN模型天气类别预测混淆矩阵</p>
</div>

<div align="center">
<img src="displayed_charts/lstm/tcn_weather_accuracy.png" width="80%" alt="TCN模型天气类别预测准确率曲�?>
<p>�?-60 TCN模型天气类别预测准确率曲�?/p>
</div>

<div align="center">
<img src="displayed_charts/lstm/tcn_weather_loss.png" width="80%" alt="TCN模型天气类别预测损失曲线">
<p>�?-61 TCN模型天气类别预测损失曲线</p>
</div>
