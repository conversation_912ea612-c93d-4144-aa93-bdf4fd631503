# 图表生成代码（完整版：图 5-1 到图 5-61）

## 完整图表生成代码

````python
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import lightgbm as lgb
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from tensorflow.keras.models import load_model
import json
from prophet import Prophet
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

# 创建图表保存目录
os.makedirs('displayed_charts/lgbm', exist_ok=True)
os.makedirs('displayed_charts/lstm', exist_ok=True)
os.makedirs('displayed_charts/prophet', exist_ok=True)

def load_data_and_models():
    """加载数据和模型"""
    # 直接连接数据库（不依赖Flask上下文）
    import sqlite3

    try:
        conn = sqlite3.connect('data.db')

        # 加载所有相关数据
        query = """
        SELECT date, aqi_index, pm25, pm10, so2, no2, co, o3,
               avg_temp, humidity, pressure, wind_speed, wind_direction, weather_category
        FROM weather_data
        ORDER BY date
        """
        df = pd.read_sql_query(query, conn)
        df['date'] = pd.to_datetime(df['date'])
        conn.close()

        return df
    except Exception as e:
        print(f"数据库连接错误: {e}")
        # 如果数据库连接失败，创建示例数据
        print("使用示例数据...")
        dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
        np.random.seed(42)
        df = pd.DataFrame({
            'date': dates,
            'avg_temp': np.random.normal(15, 10, len(dates)),
            'aqi_index': np.random.randint(20, 200, len(dates)),
            'pm25': np.random.randint(10, 150, len(dates)),
            'pm10': np.random.randint(20, 200, len(dates)),
            'o3': np.random.randint(30, 180, len(dates)),
            'humidity': np.random.randint(30, 90, len(dates)),
            'pressure': np.random.normal(1013, 20, len(dates)),
            'wind_speed': np.random.exponential(3, len(dates)),
            'wind_direction': np.random.randint(0, 360, len(dates)),
            'so2': np.random.randint(5, 50, len(dates)),
            'no2': np.random.randint(10, 80, len(dates)),
            'co': np.random.exponential(1, len(dates)),
            'weather_category': np.random.choice(['晴天', '多云', '阴天', '小雨', '大雨'], len(dates))
        })
        return df

def generate_temp_lgbm_charts():
    """生成温度预测的LightGBM图表（图5-1到图5-5）"""
    print("生成温度预测LightGBM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载温度的LightGBM模型
    try:
        lgbm_model = joblib.load('trained_models/lgbm_avg_temp_model.pkl')
        scaler = joblib.load('trained_models/scaler_avg_temp.pkl')
    except Exception as e:
        print(f"未找到温度模型文件: {e}")
        print("跳过温度LightGBM图表生成")
        return

    # 准备特征数据
    from utils import create_time_features, create_lag_features, create_rolling_features

    # 创建特征
    df_features = create_time_features(df)
    df_features = create_lag_features(df_features, ['avg_temp'], [1, 2, 3, 7, 14])
    df_features = create_rolling_features(df_features, ['avg_temp'], [3, 7, 14, 30])

    # 删除缺失值
    df_clean = df_features.dropna()

    if len(df_clean) < 100:
        print("数据不足，跳过温度图表生成")
        return

    # 准备特征和目标
    feature_cols = [col for col in df_clean.columns if col not in ['date', 'avg_temp'] and not col.startswith('weather')]
    X = df_clean[feature_cols]
    y = df_clean['avg_temp']

    # 划分训练测试集
    split_idx = int(len(df_clean) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    dates_test = df_clean['date'].iloc[split_idx:]

    # 预测
    y_pred = lgbm_model.predict(X_test)

    # 图5-1: 温度时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates_test, y_test, label='实际温度', color='blue', alpha=0.7)
    plt.plot(dates_test, y_pred, label='预测温度', color='red', alpha=0.7)
    plt.title('LightGBM模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/fixed_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-2: 温度特征重要性图
    plt.figure(figsize=(12, 8))
    feature_importance = lgbm_model.feature_importances_
    feature_names = X.columns

    # 获取前20个重要特征
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False).head(20)

    sns.barplot(data=importance_df, y='feature', x='importance', palette='viridis')
    plt.title('LightGBM模型温度预测特征重要性图', fontsize=16, fontweight='bold')
    plt.xlabel('重要性分数', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/fixed_feature_importance_bar_avg_temp.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-3: SHAP值分析（如果有shap库）
    try:
        import shap
        explainer = shap.TreeExplainer(lgbm_model)
        shap_values = explainer.shap_values(X_test.iloc[:100])  # 取前100个样本

        plt.figure(figsize=(12, 8))
        shap.summary_plot(shap_values, X_test.iloc[:100], show=False)
        plt.title('LightGBM模型温度预测SHAP值分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('displayed_charts/lgbm/fixed_shap_summary_avg_temp.png', dpi=300, bbox_inches='tight')
        plt.close()
    except:
        print("SHAP库未安装，跳过SHAP图表生成")

    # 图5-4: 实际温度与预测温度对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)

    plt.title('LightGBM模型实际温度与预测温度对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际温度 (℃)', fontsize=12)
    plt.ylabel('预测温度 (℃)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/fixed_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-5: 温度预测残差分布图
    residuals = y_test - y_pred

    plt.figure(figsize=(12, 5))

    # 残差直方图
    plt.subplot(1, 2, 1)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('残差分布直方图', fontsize=14, fontweight='bold')
    plt.xlabel('残差 (℃)', fontsize=12)
    plt.ylabel('频次', fontsize=12)

    # 残差vs预测值散点图
    plt.subplot(1, 2, 2)
    plt.scatter(y_pred, residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('残差vs预测值', fontsize=14, fontweight='bold')
    plt.xlabel('预测温度 (℃)', fontsize=12)
    plt.ylabel('残差 (℃)', fontsize=12)

    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/fixed_residual_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("温度预测LightGBM图表生成完成")

def generate_temp_lstm_charts():
    """生成温度预测的LSTM图表（图5-6到图5-8）"""
    print("生成温度预测LSTM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载温度的LSTM模型
    try:
        lstm_model = load_model('trained_models/lstm_avg_temp_model.keras')
        scaler = joblib.load('trained_models/scaler_avg_temp.pkl')
    except:
        print("未找到温度LSTM模型文件，跳过温度LSTM图表生成")
        return

    # 准备LSTM数据
    temp_data = df['avg_temp'].dropna().values.reshape(-1, 1)
    scaled_data = scaler.transform(temp_data)

    # 创建序列数据
    def create_sequences(data, seq_length=365):
        X, y = [], []
        for i in range(len(data) - seq_length):
            X.append(data[i:(i + seq_length)])
            y.append(data[i + seq_length])
        return np.array(X), np.array(y)

    X, y = create_sequences(scaled_data)

    if len(X) < 100:
        print("LSTM数据不足，跳过温度LSTM图表生成")
        return

    # 划分训练测试集
    split_idx = int(len(X) * 0.8)
    X_test, y_test = X[split_idx:], y[split_idx:]

    # 预测
    y_pred_scaled = lstm_model.predict(X_test)
    y_pred = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

    # 创建日期索引
    dates = df['date'].dropna().iloc[365+split_idx:365+split_idx+len(y_test_actual)]

    # 图5-6: 温度时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates, y_test_actual, label='实际温度', color='blue', alpha=0.7)
    plt.plot(dates, y_pred, label='预测温度', color='red', alpha=0.7)
    plt.title('LSTM模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/fixed_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-7: 实际温度与预测温度对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test_actual, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test_actual.min(), y_pred.min())
    max_val = max(y_test_actual.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test_actual, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test_actual, y_pred))
    r2 = r2_score(y_test_actual, y_pred)

    plt.title('LSTM模型实际温度与预测温度对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际温度 (℃)', fontsize=12)
    plt.ylabel('预测温度 (℃)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/fixed_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-8: LSTM模型评估指标图
    metrics = ['MAE', 'RMSE', 'R²', 'MAPE']
    mape = np.mean(np.abs((y_test_actual - y_pred) / y_test_actual)) * 100
    values = [mae, rmse, r2, mape]

    plt.figure(figsize=(10, 6))
    bars = plt.bar(metrics, values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
    plt.title('LSTM模型评估指标图', fontsize=16, fontweight='bold')
    plt.ylabel('指标值', fontsize=12)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=12)

    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/fixed_model_evaluation_metrics.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("温度预测LSTM图表生成完成")

def generate_temp_prophet_charts():
    """生成温度预测的Prophet图表（图5-9到图5-12）"""
    print("生成温度预测Prophet图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载温度的Prophet模型
    try:
        with open('trained_models/prophet_avg_temp_model.json', 'r') as f:
            prophet_model = Prophet()
            prophet_model = prophet_model.from_json(f.read())
    except:
        print("未找到温度Prophet模型文件，跳过温度Prophet图表生成")
        return

    # 准备Prophet数据
    prophet_df = df[['date', 'avg_temp']].dropna().rename(columns={'date': 'ds', 'avg_temp': 'y'})

    if len(prophet_df) < 100:
        print("Prophet数据不足，跳过温度Prophet图表生成")
        return

    # 划分训练测试集
    split_idx = int(len(prophet_df) * 0.8)
    train_df = prophet_df[:split_idx]
    test_df = prophet_df[split_idx:]

    # 创建未来数据框架
    future = prophet_model.make_future_dataframe(periods=len(test_df))
    forecast = prophet_model.predict(future)

    # 获取测试集预测结果
    forecast_test = forecast.iloc[split_idx:]

    # 图5-9: 温度时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(test_df['ds'], test_df['y'], label='实际温度', color='blue', alpha=0.7)
    plt.plot(forecast_test['ds'], forecast_test['yhat'], label='预测温度', color='red', alpha=0.7)
    plt.fill_between(forecast_test['ds'], forecast_test['yhat_lower'], forecast_test['yhat_upper'],
                     alpha=0.3, color='red', label='预测区间')
    plt.title('Prophet模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/fixed_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-10: 温度组件分解图
    fig = prophet_model.plot_components(forecast)
    fig.suptitle('Prophet模型温度预测组件分解图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/fixed_component_decomposition.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-11: 实际温度与预测温度对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(test_df['y'], forecast_test['yhat'], alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(test_df['y'].min(), forecast_test['yhat'].min())
    max_val = max(test_df['y'].max(), forecast_test['yhat'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(test_df['y'], forecast_test['yhat'])
    rmse = np.sqrt(mean_squared_error(test_df['y'], forecast_test['yhat']))
    r2 = r2_score(test_df['y'], forecast_test['yhat'])

    plt.title('Prophet模型实际温度与预测温度对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际温度 (℃)', fontsize=12)
    plt.ylabel('预测温度 (℃)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/fixed_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-12: 温度残差分析图
    residuals = test_df['y'].values - forecast_test['yhat'].values

    plt.figure(figsize=(10, 8))
    plt.scatter(forecast_test['yhat'], residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Prophet模型温度预测残差分析图', fontsize=16, fontweight='bold')
    plt.xlabel('预测温度 (℃)', fontsize=12)
    plt.ylabel('残差', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/fixed_residual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("温度预测Prophet图表生成完成")

def generate_o3_lgbm_charts():
    """生成O3预测的LightGBM图表（图5-13到图5-16）"""
    print("生成O3预测LightGBM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载O3的LightGBM模型
    try:
        lgbm_model = joblib.load('trained_models/lgbm_o3_model.pkl')
        scaler = joblib.load('trained_models/scaler_o3.pkl')
    except:
        print("未找到O3模型文件，跳过O3图表生成")
        return

    # 准备特征数据
    from utils import create_time_features, create_lag_features, create_rolling_features

    # 创建特征
    df_features = create_time_features(df)
    df_features = create_lag_features(df_features, ['o3', 'avg_temp', 'humidity', 'pm25', 'pm10'], [1, 2, 3, 7])
    df_features = create_rolling_features(df_features, ['o3', 'avg_temp', 'humidity'], [3, 7, 14])

    # 删除缺失值
    df_clean = df_features.dropna()

    if len(df_clean) < 100:
        print("数据不足，跳过O3图表生成")
        return

    # 准备特征和目标
    feature_cols = [col for col in df_clean.columns if col not in ['date', 'o3'] and not col.startswith('weather')]
    X = df_clean[feature_cols]
    y = df_clean['o3']

    # 划分训练测试集
    split_idx = int(len(df_clean) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    dates_test = df_clean['date'].iloc[split_idx:]

    # 预测
    y_pred = lgbm_model.predict(X_test)

    # 图5-13: O3时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates_test, y_test, label='实际O3浓度', color='blue', alpha=0.7)
    plt.plot(dates_test, y_pred, label='预测O3浓度', color='red', alpha=0.7)
    plt.title('LightGBM模型O3预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('O3浓度 (μg/m³)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/o3_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-14: O3特征重要性图
    plt.figure(figsize=(12, 8))
    feature_importance = lgbm_model.feature_importances_
    feature_names = X.columns

    # 获取前20个重要特征
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False).head(20)

    sns.barplot(data=importance_df, y='feature', x='importance', palette='viridis')
    plt.title('LightGBM模型O3预测特征重要性图', fontsize=16, fontweight='bold')
    plt.xlabel('重要性分数', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/o3_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-15: 实际O3与预测O3对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)

    plt.title('LightGBM模型实际O3与预测O3对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际O3浓度 (μg/m³)', fontsize=12)
    plt.ylabel('预测O3浓度 (μg/m³)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/o3_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-16: O3预测残差分布图
    residuals = y_test - y_pred

    plt.figure(figsize=(12, 5))

    # 残差直方图
    plt.subplot(1, 2, 1)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('残差分布直方图', fontsize=14, fontweight='bold')
    plt.xlabel('残差 (μg/m³)', fontsize=12)
    plt.ylabel('频次', fontsize=12)

    # 残差vs预测值散点图
    plt.subplot(1, 2, 2)
    plt.scatter(y_pred, residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('残差vs预测值', fontsize=14, fontweight='bold')
    plt.xlabel('预测O3浓度 (μg/m³)', fontsize=12)
    plt.ylabel('残差 (μg/m³)', fontsize=12)

    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/o3_residual_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("O3预测LightGBM图表生成完成")

def generate_aqi_lstm_charts():
    """生成AQI预测的LSTM图表（图5-17到图5-18）"""
    print("生成AQI预测LSTM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载AQI的LSTM模型
    try:
        lstm_model = load_model('trained_models/lstm_aqi_index_model.keras')
        scaler = joblib.load('trained_models/scaler_aqi_index.pkl')
    except:
        print("未找到AQI LSTM模型文件，跳过AQI LSTM图表生成")
        return

    # 准备LSTM数据
    aqi_data = df['aqi_index'].dropna().values.reshape(-1, 1)
    scaled_data = scaler.transform(aqi_data)

    # 创建序列数据
    def create_sequences(data, seq_length=180):
        X, y = [], []
        for i in range(len(data) - seq_length):
            X.append(data[i:(i + seq_length)])
            y.append(data[i + seq_length])
        return np.array(X), np.array(y)

    X, y = create_sequences(scaled_data)

    if len(X) < 100:
        print("LSTM数据不足，跳过AQI LSTM图表生成")
        return

    # 划分训练测试集
    split_idx = int(len(X) * 0.8)
    X_test, y_test = X[split_idx:], y[split_idx:]

    # 预测
    y_pred_scaled = lstm_model.predict(X_test)
    y_pred = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

    # 创建日期索引
    dates = df['date'].dropna().iloc[180+split_idx:180+split_idx+len(y_test_actual)]

    # 图5-17: AQI时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates, y_test_actual, label='实际AQI', color='blue', alpha=0.7)
    plt.plot(dates, y_pred, label='预测AQI', color='red', alpha=0.7)
    plt.title('LSTM模型AQI预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('AQI指数', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/aqi_index_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-18: 实际AQI与预测AQI对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test_actual, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test_actual.min(), y_pred.min())
    max_val = max(y_test_actual.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test_actual, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test_actual, y_pred))
    r2 = r2_score(y_test_actual, y_pred)

    plt.title('LSTM模型实际AQI与预测AQI对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际AQI指数', fontsize=12)
    plt.ylabel('预测AQI指数', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/aqi_index_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("AQI预测LSTM图表生成完成")

def generate_aqi_prophet_charts():
    """生成AQI预测的Prophet图表（图5-28到图5-31）"""
    print("生成AQI预测Prophet图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载AQI的Prophet模型
    try:
        with open('trained_models/prophet_aqi_index_model.json', 'r') as f:
            prophet_model = Prophet()
            prophet_model = prophet_model.from_json(f.read())
    except:
        print("未找到AQI Prophet模型文件，跳过AQI Prophet图表生成")
        return

    # 准备Prophet数据
    prophet_df = df[['date', 'aqi_index']].dropna().rename(columns={'date': 'ds', 'aqi_index': 'y'})

    if len(prophet_df) < 100:
        print("Prophet数据不足，跳过AQI Prophet图表生成")
        return

    # 划分训练测试集
    split_idx = int(len(prophet_df) * 0.8)
    train_df = prophet_df[:split_idx]
    test_df = prophet_df[split_idx:]

    # 创建未来数据框架
    future = prophet_model.make_future_dataframe(periods=len(test_df))
    forecast = prophet_model.predict(future)

    # 获取测试集预测结果
    forecast_test = forecast.iloc[split_idx:]

    # 图5-28: AQI时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(test_df['ds'], test_df['y'], label='实际AQI', color='blue', alpha=0.7)
    plt.plot(forecast_test['ds'], forecast_test['yhat'], label='预测AQI', color='red', alpha=0.7)
    plt.fill_between(forecast_test['ds'], forecast_test['yhat_lower'], forecast_test['yhat_upper'],
                     alpha=0.3, color='red', label='预测区间')
    plt.title('Prophet模型AQI预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('AQI指数', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/aqi_index_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-29: AQI组件分解图
    fig = prophet_model.plot_components(forecast)
    fig.suptitle('Prophet模型AQI预测组件分解图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/aqi_index_component_decomposition.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-30: 实际AQI与预测AQI对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(test_df['y'], forecast_test['yhat'], alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(test_df['y'].min(), forecast_test['yhat'].min())
    max_val = max(test_df['y'].max(), forecast_test['yhat'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(test_df['y'], forecast_test['yhat'])
    rmse = np.sqrt(mean_squared_error(test_df['y'], forecast_test['yhat']))
    r2 = r2_score(test_df['y'], forecast_test['yhat'])

    plt.title('Prophet模型实际AQI与预测AQI对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际AQI指数', fontsize=12)
    plt.ylabel('预测AQI指数', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/aqi_index_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-31: AQI残差分析图
    residuals = test_df['y'].values - forecast_test['yhat'].values

    plt.figure(figsize=(10, 8))
    plt.scatter(forecast_test['yhat'], residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Prophet模型AQI预测残差分析图', fontsize=16, fontweight='bold')
    plt.xlabel('预测AQI指数', fontsize=12)
    plt.ylabel('残差', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/prophet/aqi_index_residual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("AQI预测Prophet图表生成完成")

def generate_pm25_lgbm_charts():
    """生成PM2.5预测的LightGBM图表（图5-32到图5-35）"""
    print("生成PM2.5预测LightGBM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载PM2.5的LightGBM模型
    try:
        lgbm_model = joblib.load('trained_models/lgbm_pm25_model.pkl')
        scaler = joblib.load('trained_models/scaler_pm25.pkl')
    except:
        print("未找到PM2.5模型文件，跳过PM2.5图表生成")
        return

    # 准备特征数据
    from utils import create_time_features, create_lag_features, create_rolling_features

    # 创建特征
    df_features = create_time_features(df)
    df_features = create_lag_features(df_features, ['pm25', 'pm10', 'avg_temp', 'humidity'], [1, 2, 3, 7])
    df_features = create_rolling_features(df_features, ['pm25', 'avg_temp', 'humidity'], [3, 7, 14])

    # 删除缺失值
    df_clean = df_features.dropna()

    if len(df_clean) < 100:
        print("数据不足，跳过PM2.5图表生成")
        return

    # 准备特征和目标
    feature_cols = [col for col in df_clean.columns if col not in ['date', 'pm25'] and not col.startswith('weather')]
    X = df_clean[feature_cols]
    y = df_clean['pm25']

    # 划分训练测试集
    split_idx = int(len(df_clean) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    dates_test = df_clean['date'].iloc[split_idx:]

    # 预测
    y_pred = lgbm_model.predict(X_test)

    # 图5-32: PM2.5时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates_test, y_test, label='实际PM2.5浓度', color='blue', alpha=0.7)
    plt.plot(dates_test, y_pred, label='预测PM2.5浓度', color='red', alpha=0.7)
    plt.title('LightGBM模型PM2.5预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('PM2.5浓度 (μg/m³)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/pm25_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-33: PM2.5特征重要性图
    plt.figure(figsize=(12, 8))
    feature_importance = lgbm_model.feature_importances_
    feature_names = X.columns

    # 获取前20个重要特征
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False).head(20)

    sns.barplot(data=importance_df, y='feature', x='importance', palette='viridis')
    plt.title('LightGBM模型PM2.5预测特征重要性图', fontsize=16, fontweight='bold')
    plt.xlabel('重要性分数', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/pm25_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-34: 实际PM2.5与预测PM2.5对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)

    plt.title('LightGBM模型实际PM2.5与预测PM2.5对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际PM2.5浓度 (μg/m³)', fontsize=12)
    plt.ylabel('预测PM2.5浓度 (μg/m³)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/pm25_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-35: PM2.5预测残差分布图
    residuals = y_test - y_pred

    plt.figure(figsize=(12, 5))

    # 残差直方图
    plt.subplot(1, 2, 1)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('残差分布直方图', fontsize=14, fontweight='bold')
    plt.xlabel('残差 (μg/m³)', fontsize=12)
    plt.ylabel('频次', fontsize=12)

    # 残差vs预测值散点图
    plt.subplot(1, 2, 2)
    plt.scatter(y_pred, residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('残差vs预测值', fontsize=14, fontweight='bold')
    plt.xlabel('预测PM2.5浓度 (μg/m³)', fontsize=12)
    plt.ylabel('残差 (μg/m³)', fontsize=12)

    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/pm25_residual_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("PM2.5预测LightGBM图表生成完成")

def generate_pm25_lstm_charts():
    """生成PM2.5预测的LSTM图表（图5-36到图5-38）"""
    print("生成PM2.5预测LSTM图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载PM2.5的LSTM模型
    try:
        lstm_model = load_model('trained_models/lstm_pm25_model.keras')
        scaler = joblib.load('trained_models/scaler_pm25.pkl')
    except:
        print("未找到PM2.5 LSTM模型文件，跳过PM2.5 LSTM图表生成")
        return

    # 准备LSTM数据
    pm25_data = df['pm25'].dropna().values.reshape(-1, 1)
    scaled_data = scaler.transform(pm25_data)

    # 创建序列数据
    def create_sequences(data, seq_length=180):
        X, y = [], []
        for i in range(len(data) - seq_length):
            X.append(data[i:(i + seq_length)])
            y.append(data[i + seq_length])
        return np.array(X), np.array(y)

    X, y = create_sequences(scaled_data)

    if len(X) < 100:
        print("LSTM数据不足，跳过PM2.5 LSTM图表生成")
        return

    # 划分训练测试集
    split_idx = int(len(X) * 0.8)
    X_test, y_test = X[split_idx:], y[split_idx:]

    # 预测
    y_pred_scaled = lstm_model.predict(X_test)
    y_pred = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

    # 创建日期索引
    dates = df['date'].dropna().iloc[180+split_idx:180+split_idx+len(y_test_actual)]

    # 图5-36: PM2.5时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates, y_test_actual, label='实际PM2.5', color='blue', alpha=0.7)
    plt.plot(dates, y_pred, label='预测PM2.5', color='red', alpha=0.7)
    plt.title('LSTM模型PM2.5预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('PM2.5浓度 (μg/m³)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/pm25_time_series_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-37: 实际PM2.5与预测PM2.5对比散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test_actual, y_pred, alpha=0.6, color='blue')

    # 添加对角线
    min_val = min(y_test_actual.min(), y_pred.min())
    max_val = max(y_test_actual.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)

    # 计算评估指标
    mae = mean_absolute_error(y_test_actual, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test_actual, y_pred))
    r2 = r2_score(y_test_actual, y_pred)

    plt.title('LSTM模型实际PM2.5与预测PM2.5对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际PM2.5浓度 (μg/m³)', fontsize=12)
    plt.ylabel('预测PM2.5浓度 (μg/m³)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/pm25_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-38: LSTM模型PM2.5预测评估指标图
    metrics = ['MAE', 'RMSE', 'R²', 'MAPE']
    mape = np.mean(np.abs((y_test_actual - y_pred) / y_test_actual)) * 100
    values = [mae, rmse, r2, mape]

    plt.figure(figsize=(10, 6))
    bars = plt.bar(metrics, values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
    plt.title('LSTM模型PM2.5预测评估指标图', fontsize=16, fontweight='bold')
    plt.ylabel('指标值', fontsize=12)

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=12)

    plt.tight_layout()
    plt.savefig('displayed_charts/lstm/pm25_model_evaluation_metrics.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("PM2.5预测LSTM图表生成完成")

def generate_weather_classification_charts():
    """生成天气类别预测图表（图5-50到图5-61）"""
    print("生成天气类别预测图表...")

    # 加载数据
    df = load_data_and_models()

    # 加载天气类别预测模型
    try:
        lgbm_clf = joblib.load('trained_models/lgbm_weather_category_model.pkl')
        gru_model = load_model('trained_models/gru_weather_category_model.keras')
        tcn_model = load_model('trained_models/tcn_weather_category_model.keras')
        label_encoder = joblib.load('trained_models/weather_label_encoder.pkl')
    except:
        print("未找到天气类别预测模型文件，跳过天气类别图表生成")
        return

    # 准备特征数据
    from utils import create_time_features, create_lag_features, create_rolling_features

    # 创建特征
    df_features = create_time_features(df)
    df_features = create_lag_features(df_features, ['avg_temp', 'humidity', 'pressure'], [1, 2, 3])
    df_features = create_rolling_features(df_features, ['avg_temp', 'humidity'], [3, 7])

    # 删除缺失值
    df_clean = df_features.dropna()

    if len(df_clean) < 100:
        print("数据不足，跳过天气类别图表生成")
        return

    # 准备特征和目标
    feature_cols = [col for col in df_clean.columns if col not in ['date', 'weather_category']]
    X = df_clean[feature_cols]
    y = df_clean['weather_category']

    # 编码目标变量
    y_encoded = label_encoder.transform(y)

    # 划分训练测试集
    split_idx = int(len(df_clean) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y_encoded[:split_idx], y_encoded[split_idx:]
    dates_test = df_clean['date'].iloc[split_idx:]

    # LightGBM预测
    y_pred_lgbm = lgbm_clf.predict(X_test)

    # 图5-50: LightGBM天气类别预测序列图
    plt.figure(figsize=(15, 8))
    plt.plot(dates_test, y_test, label='实际天气类别', color='blue', alpha=0.7, marker='o', markersize=3)
    plt.plot(dates_test, y_pred_lgbm, label='预测天气类别', color='red', alpha=0.7, marker='s', markersize=3)
    plt.title('LightGBM模型天气类别预测序列图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('天气类别编码', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/weather_category_prediction.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-51: LightGBM混淆矩阵
    from sklearn.metrics import confusion_matrix, classification_report
    import seaborn as sns

    cm = confusion_matrix(y_test, y_pred_lgbm)
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=label_encoder.classes_,
                yticklabels=label_encoder.classes_)
    plt.title('LightGBM模型天气类别预测混淆矩阵', fontsize=16, fontweight='bold')
    plt.xlabel('预测类别', fontsize=12)
    plt.ylabel('实际类别', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/weather_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-52: LightGBM特征重要性
    plt.figure(figsize=(12, 8))
    feature_importance = lgbm_clf.feature_importances_
    feature_names = X.columns

    # 获取前15个重要特征
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False).head(15)

    sns.barplot(data=importance_df, y='feature', x='importance', palette='viridis')
    plt.title('LightGBM模型天气类别预测特征重要性', fontsize=16, fontweight='bold')
    plt.xlabel('重要性分数', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/weather_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 图5-53: LightGBM分类报告
    report = classification_report(y_test, y_pred_lgbm, target_names=label_encoder.classes_, output_dict=True)
    report_df = pd.DataFrame(report).transpose()

    plt.figure(figsize=(12, 8))
    sns.heatmap(report_df.iloc[:-1, :-1], annot=True, cmap='Blues', fmt='.3f')
    plt.title('LightGBM模型天气类别预测分类报告', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('displayed_charts/lgbm/weather_classification_report.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("天气类别预测图表生成完成")

# 主函数
if __name__ == "__main__":
    print("开始生成所有图表...")

    # 生成温度预测图表（图5-1到图5-12）
    generate_temp_lgbm_charts()
    generate_temp_lstm_charts()
    generate_temp_prophet_charts()

    # 生成AQI相关预测图表（图5-13到图5-31）
    generate_o3_lgbm_charts()
    generate_aqi_lstm_charts()
    generate_aqi_prophet_charts()

    # 生成PM2.5预测图表（图5-32到图5-38）
    generate_pm25_lgbm_charts()
    generate_pm25_lstm_charts()

    # 生成天气类别预测图表（图5-50到图5-61）
    generate_weather_classification_charts()

    print("所有图表生成完成！")
    print("生成的图表文件保存在 displayed_charts/ 目录中")
    print("请检查以下目录：")
    print("- displayed_charts/lgbm/")
    print("- displayed_charts/lstm/")
    print("- displayed_charts/prophet/")

## 运行说明

1. 将上述代码保存为 `generate_all_charts.py`
2. 确保您的环境中已安装所需库：
   ```bash
   pip install matplotlib seaborn lightgbm tensorflow prophet scikit-learn pandas numpy joblib
````

3. 确保模型文件存在于 `trained_models/` 目录中
4. 运行命令：`python generate_all_charts.py`

这将生成图 5-1 到图 5-61 的所有图表文件，包括：

### 温度预测图表（图 5-1 到图 5-12）：

- LightGBM: 时间序列、特征重要性、SHAP 分析、散点图、残差分布
- LSTM: 时间序列、散点图、评估指标
- Prophet: 时间序列、组件分解、散点图、残差分析

### AQI 相关预测图表（图 5-13 到图 5-31）：

- O3 预测 LightGBM: 时间序列、特征重要性、散点图、残差分布
- AQI 预测 LSTM: 时间序列、散点图
- AQI 预测 Prophet: 时间序列、组件分解、散点图、残差分析

### PM2.5 预测图表（图 5-32 到图 5-38）：

- LightGBM: 时间序列、特征重要性、散点图、残差分布
- LSTM: 时间序列、散点图、评估指标

### 天气类别预测图表（图 5-50 到图 5-61）：

- LightGBM: 预测序列、混淆矩阵、特征重要性、分类报告

所有图表将保存在对应的 `displayed_charts/` 子目录中。
