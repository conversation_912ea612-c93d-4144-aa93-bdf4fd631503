# 5.1 数据爬虫实现

本系统的数据采集模块采用网络爬虫技术从特定网站自动获取气象和空气质量历史数据，为后续分析和预测提供原始数据支持。数据爬虫主要由针对天气数据的`weather_spider.py`和针对空气质量数据的`aqi_spider.py`两个核心脚本实现。

## 5.1.1 网络爬虫架构设计

系统爬虫模块采用基于 Python 的 requests 库与 BeautifulSoup4 库相结合的爬取架构，实现从 HTTP 请求到结构化数据的全流程处理。如图 5.1-1 所示，爬虫架构遵循"URL 构造 → 网页获取 → 内容解析 → 数据结构化 → 数据入库"的经典数据采集流程。

爬虫首先根据预定义的城市编码和时间范围参数，自动构建目标数据页面 URL。例如，眉山市 2023 年 10 月的天气数据 URL 为"http://www.tianqihoubao.com/lishi/meishan/month/202310.html"。随后，模拟浏览器行为发送HTTP GET 请求获取页面内容，并通过 BeautifulSoup 库定位到包含目标数据的 HTML 元素，提取所需信息后存入 SQLite 数据库。

图 5.1-1 网络爬虫架构流程图

## 5.1.2 数据源适配与解析

本系统选用天气后报网(tianqihoubao.com)作为主要数据源，该网站提供了全国各地区的历史天气和空气质量数据记录。针对不同类型数据，系统实现了专门的解析逻辑：

1. **天气数据解析**

对于天气数据，主要从表格中提取日期、天气状况、温度范围和风力信息。其中温度数据通常以"25℃/15℃"形式存在，需要特殊处理以提取最高温、最低温并计算平均温度。在`weather_spider.py`中，系统使用 BeautifulSoup 的 CSS 选择器定位表格行，并从中提取各字段内容。如图 5.1-2 所示为天气数据源页面示例及解析提取过程。

2. **空气质量数据解析**

对于空气质量数据，系统主要提取 AQI 指数、空气质量等级以及 PM2.5、PM10、SO2、NO2、CO、O3 等污染物浓度。在`aqi_spider.py`中，针对数值转换可能出现的问题，系统实现了安全转换机制，确保数据的有效性。如图 5.1-3 所示为 AQI 数据源页面示例及数据提取效果。

3. **日期格式标准化**

两个爬虫模块均实现了日期格式标准化功能，将网页中多种格式的日期字符串（如"2023 年 10 月 1 日"、"2023-10-01"）统一转换为"YYYY-MM-DD"标准格式，保证数据时间标识的一致性。系统采用多级解析策略，优先尝试标准格式，失败后尝试中文日期格式，最后使用正则表达式匹配年月日。

图 5.1-2 天气数据源页面示例
图 5.1-3 AQI 数据源页面示例

## 5.1.3 爬取策略与稳定性保障

为确保数据采集的可靠性和稳定性，同时避免对目标网站造成过大负担，系统实施了一系列优化策略：

1. **参数化配置与批量爬取**

系统通过`CITY_MAP`、`YEARS`和`MONTHS`等配置参数，实现对多城市、多时间段数据的批量化采集。这种参数化设计使得系统可以轻松扩展到其他城市或更新时间范围的数据爬取任务。

2. **反爬虫对策**

为避免被目标网站识别为爬虫行为，系统在请求头中设置了标准的 User-Agent 信息，并在连续请求之间添加了随机延时（1-3 秒），有效降低被封禁风险。此外，系统还实现了 IP 代理切换机制（可选功能），进一步提高了爬取的隐蔽性。

3. **增量更新机制**

系统实现了智能的增量更新机制，通过查询数据库中已有记录，避免重复爬取相同数据。在爬取前，系统会先检查数据库中指定城市和月份的记录数量，如果数量足够且不在强制重新爬取列表中，则跳过该月份的爬取。这种机制显著提高了爬虫的运行效率，也减轻了对目标网站的请求压力。

4. **容错与重试机制**

为应对网络波动和临时错误，系统实现了自动重试机制，单个 URL 最多尝试 3 次请求，显著提高了爬取成功率。此外，系统还处理了各种异常情况，包括网络超时、解析错误和数据库异常等，确保爬虫能够长时间稳定运行。

5. **调试支持**

在开启 DEBUG_MODE 的情况下，系统会将爬取到的原始 HTML 内容保存到本地文件，便于问题排查和解析逻辑调试。如图 5.1-4 所示为爬虫任务执行日志与调试文件示例。

通过上述策略的综合应用，本系统的爬虫模块能够稳定、高效地采集所需的气象和空气质量数据，为后续的数据分析和预测模型提供可靠的数据基础。

图 5.1-4 爬虫任务执行日志与调试文件示例
