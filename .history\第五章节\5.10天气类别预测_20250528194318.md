5.10 天气类别预测

天气类别预测是气象预报的重要组成部分，不同于温度、空气质量等数值预测，天气类别预测属于典型的多分类问题，需要预测"晴、多云、阴、雨、雪"等离散的天气状态。准确的天气类别预测对人们的日常生活、出行计划、户外活动安排等具有直接价值。本系统在实现了各种数值型气象要素预测的基础上，进一步构建了针对天气类别的专门预测模型，实现了对未来天气状况的分类预测。

5.10.1 天气类别预测特性与挑战

天气类别预测区别于数值预测任务，具有其独特的特点与挑战。系统在设计天气类别预测模型时，特别考虑了以下关键特性：

首先，天气类别是高度综合的气象状态描述，涉及多种气象要素的综合表现。与单一气象要素（如温度、湿度）不同，天气类别是对大气状况的整体描述，通常由多种气象要素共同决定。例如，"雨"的判定不仅取决于降水量，还与相对湿度、云量、气压等多个因素相关。分析表明，眉山市的天气类别与气象要素的关联性表现为：降水量与"雨"类别的相关系数为 0.83；云量与"多云/阴"类别的相关系数为 0.74；相对湿度与"晴"类别的负相关系数为-0.68。这种多因素综合决定的特性要求预测模型能够有效整合多种气象要素信息，理解它们之间的复杂交互关系。

其次，天气类别之间存在内在的演变规律和转换模式。天气系统演变具有一定的连续性和规律性，特定的天气类别之间更容易发生转换，而某些转换则相对罕见。统计分析显示，眉山市"晴"到"多云"的转换概率约为 32.5%，而"晴"直接转为"大雨"的概率仅为 0.7%。同样，天气状况持续的时间也呈现出规律性，"晴"天气平均持续期为 2.3 天，"阴"天气平均持续期为 1.7 天，而"大雨"通常持续不超过 1 天。这种状态转换和持续性特征为序列模型提供了有价值的时间依赖信息。

第三，天气类别分布存在明显的季节性和不平衡性。不同季节的天气类别分布差异明显，如眉山市夏季（6-8 月）"雨"类天气出现频率为 38.2%，而冬季（12-2 月）仅为 12.5%；相反，"晴"类天气在冬季出现频率为 45.7%，而夏季仅为 25.3%。此外，在整体数据分布上，"晴"、"多云"、"阴"三类占主导地位，合计约占全年的 78.6%，而"大雪"、"暴雨"等极端天气类别出现频率不足 1%。这种类别不平衡性对模型训练构成挑战，容易导致模型偏向预测主导类别，而对稀有但往往更为重要的极端天气类别预测不足。

此外，天气类别判定标准具有一定的主观性和地域差异性。不同气象部门对天气类别的定义和判定标准可能存在细微差异，同样的气象条件在不同地区可能对应不同的天气描述。例如，南方和北方对"小雨"和"中雨"的降水量划分标准不完全相同。此外，天气类别判定通常涉及人为观测和经验判断，引入了一定的主观性。系统分析发现，相同气象观测数据被不同观测员判定为不同天气类别的情况占比约为 8.3%，主要集中在相邻类别之间（如"多云"与"阴"之间的区分）。这种模糊性和主观性增加了预测的难度，也影响了模型评估的准确性。

最后，天气类别预测还面临类别细分粒度的平衡问题。天气类别可以划分为粗粒度（如"晴、阴、雨"）或细粒度（如将"雨"进一步细分为"小雨、中雨、大雨、暴雨"等）。粒度越细，对用户的实用价值越高，但预测难度也随之增加，准确率往往下降。系统测试表明，当使用粗粒度 5 类划分时（"晴、多云、阴、雨、雪"），最佳模型的准确率可达 78.3%；而使用细粒度 11 类划分时（进一步细分雨雪等级），准确率降至 61.5%。因此，需要在预测精度和信息粒度之间寻找合适的平衡点。

图 1 眉山市 2020-2024 年天气类别分布统计图

5.10.2 天气类别预测的特有特征与模型调整

针对天气类别预测的特点和挑战，系统设计了专门的特征工程方法和模型优化策略，以提高分类预测准确性。

（1）针对天气类别的特征优化

在天气类别预测的特征工程中，系统构建了四类关键特征集：多源气象要素特征、天气系统特征、状态转换特征和时间特征。

多源气象要素特征是天气类别预测的基础。系统综合利用多种气象监测数据，构建了丰富的气象要素特征集：首先是基础气象观测特征，包括温度（平均、最高、最低）、相对湿度、气压、风向、风速、降水量、日照时数、云量等；其次是推导气象指标，如露点温度、体感温度、蒸发量、大气稳定度指数等；第三是极端值和变化率特征，包括 24 小时气温变化、气压变化率、湿度极值等。特别地，系统发现降水量、相对湿度和云量是判定天气类别的三个最关键要素，三者的特征重要性分值分别为 0.23、0.19 和 0.17。此外，系统还创新性地构建了多要素组合特征，如温湿组合指数、气压-风速联合特征等，这些复合特征能更好地表达气象要素间的交互作用，进一步提升了预测性能。

天气系统特征反映了大尺度气象环境对局地天气的影响。系统构建了一系列描述天气系统特征的指标：首先是天气系统类型标记，包括冷锋、暖锋、高压脊、低压槽等主要天气系统的判定特征；其次是系统演变特征，如气压系统移速、强度变化等；第三是环流形势特征，描述大尺度气流场的布局和变化。分析表明，冷锋通过眉山市区域的概率与降水天气的关联度高达 0.72，成为预测"雨"类天气的重要指标。系统还针对各种天气系统总结了特征模式库，如暖锋过境的典型气象要素变化序列、冷空气入侵的特征信号等，这些模式特征帮助模型识别天气系统发展的关键阶段。

状态转换特征捕捉了天气类别的时序演变规律。天气状态的转换具有显著的时间依赖性和模式性，系统基于此构建了多层次的状态转换特征：首先是历史天气类别序列，包括过去 1 天、3 天、7 天的天气类别及其编码表示；其次是类别持续特征，如当前天气状态的持续天数，特定天气类别的累积出现天数等；第三是转换概率特征，基于历史统计的天气类别转换矩阵，计算当前状态转向各可能状态的历史概率。特别地，系统发现不同季节的转换模式有显著差异，因此构建了季节性条件转换概率特征，如夏季"多云"转为"雨"的概率（42.3%）远高于冬季（15.7%）。这些状态转换特征极大地增强了模型捕捉天气演变规律的能力，使预测准确率提升了约 7.5 个百分点。

时间特征反映了天气类别的季节性和周期性变化。系统构建了多尺度的时间特征：基本时间标记包括月份、季节、一年中的第几天等；周期性编码特征使用三角函数变换，更好地表达时间的循环特性；此外，系统还添加了特殊节气标记和季节交替期标记。分析表明，天气类别的季节性特征极为显著，如眉山市春季（3-5 月）"阴雨"天气占比达 52.4%，而秋季（9-11 月）仅为 31.6%。系统基于这些规律性，构建了季节性天气类别概率分布特征，即每个日期历史上各类天气出现的概率统计，这一特征为模型提供了强有力的先验知识，特别是在数据稀疏的情况下提供了有价值的基准预测。

图 2 天气类别预测特征重要性排序图

（2）模型参数定制与调优

针对天气类别预测任务，系统对各预测模型进行了专门的参数优化和结构调整，以提高分类性能。

LightGBM 作为基线模型，在天气类别预测中表现出色。系统针对分类任务特点优化了模型配置：首先选择了 multi_logloss 作为优化目标，适用于多分类问题；为应对类别不平衡问题，系统采用了 class_weight 参数，为稀有天气类别（如"暴雨"、"大雪"等）设置了更高的权重，提高了对这些重要但罕见天气的预测敏感度。在树结构参数方面，系统设置了 num_leaves 为 31，max_depth 为 8，在模型复杂度和泛化能力间取得平衡。为增强模型的稳定性，采用了 5 折交叉验证的训练方式，并使用 feature_fraction(0.8)和 bagging_fraction(0.8)参数增强随机性，防止过拟合。特别地，系统实现了季节性模型集成策略，为四个季节分别训练专用模型，然后根据预测日期的季节选择相应模型进行预测，这一策略使整体准确率提升了 3.2 个百分点，特别是改善了季节性天气类别的预测准确性。

GRU（门控循环单元）模型是系统针对天气类别时序预测优化的深度学习模型。考虑到天气状态转换的序列特性，GRU 模型在捕捉时间依赖关系方面表现出色。系统配置了双向 GRU 结构，能同时考虑过去和未来的上下文信息，提高序列理解能力。为处理不同时间尺度的依赖关系，系统实现了注意力机制，使模型能自动关注序列中的关键时间点。在输入特征方面，系统为天气类别数据设计了特殊的嵌入层(Embedding Layer)，将类别变量转换为密集向量表示，更好地捕捉类别间的语义相似性。例如，在嵌入空间中，"小雨"和"中雨"的向量表示相似度高于"小雨"和"晴"的相似度，这种语义关系有助于模型学习类别间的内在联系。训练过程中，系统采用了标签平滑(Label Smoothing)技术，设置平滑参数为 0.1，缓解了模型对训练数据的过度自信，提高了泛化能力。此外，还实现了基于类别权重的损失函数调整，解决类别不平衡问题。实验表明，优化后的 GRU 模型在天气类别预测上的加权 F1 分数达到 0.684，显著优于基线模型的 0.623。

TCN（时间卷积网络）是系统特别引入的适合天气序列预测的深度学习架构。与 RNN 类模型相比，TCN 通过因果卷积和膨胀卷积高效处理长序列数据，同时保持并行计算优势。系统配置的 TCN 模型包括 3 个残差块，每个残差块包含 2 个一维卷积层，卷积核大小为 3，膨胀率分别为 1、2、4，实现了覆盖 15 天历史数据的感受野。为提高对罕见天气类别的预测能力，系统实现了焦点损失(Focal Loss)，通过调整 γ 参数(设为 2.0)增加难分类样本的权重。此外，系统还采用了混合精度训练技术，在保持预测精度的同时提高了训练效率约 40%。与传统时序模型相比，优化后的 TCN 在处理长期依赖关系和捕捉天气周期性变化方面表现出色，特别是在预测季节性天气类型转换时准确率提升明显。

图 3 天气类别预测模型训练过程可视化

随机森林模型作为传统但稳健的集成学习方法，在天气类别预测中也有应用。系统优化的随机森林模型配置了 500 棵决策树，最大深度为 12，最小叶节点样本数为 5。为增强模型多样性，系统设置了 max_features 参数为'sqrt'，每棵树随机选择特征子集，提高了集成效果。此外，系统还实施了分层抽样策略，确保每棵树的训练集都包含足够的少数类样本。相比单一决策树，随机森林显著提高了预测稳定性和准确性，在处理噪声数据和离群值方面表现出色。虽然在整体性能上略逊于 GRU 和 TCN 模型，但随机森林的训练速度快、参数调整简单、解释性强，在实际部署中仍有重要价值。

此外，系统还实现了基于 Transformer 架构的天气序列预测模型。Transformer 通过自注意力机制有效处理序列数据，无需 RNN 的递归结构，具有更好的并行性和捕捉长距离依赖的能力。系统配置的 Transformer 模型包含 2 个编码器层，每层 8 个注意力头，隐藏层维度为 256。为适应天气数据的特点，系统实现了时间位置编码，以保留序列中的时间信息。此外，系统还应用了层归一化(Layer Normalization)和残差连接(Residual Connection)，有效防止了深度网络中的梯度问题。在小样本数据集下，Transformer 模型的表现不如 GRU 稳定，但随着训练数据量增加，其性能逐渐超越传统时序模型，特别是在捕捉复杂非线性模式方面表现出优势。

5.10.3 天气类别预测结果分析与比较

基于眉山市 2020-2024 年的气象观测数据，系统对各模型的天气类别预测性能进行了全面评估和比较。评估采用了多种分类评价指标，并特别关注不同类别天气的预测准确性。

从整体分类性能看，GRU 模型表现最为优异，在 11 分类任务上的准确率达到 68.4%，加权 F1 分数为 0.684，宏平均 F1 分数为 0.552。TCN 模型紧随其后，准确率为 66.7%，加权 F1 分数为 0.671。LightGBM 模型表现也相当出色，准确率为 65.3%，加权 F1 分数为 0.657。随机森林和 Transformer 模型的准确率分别为 63.8%和 62.1%。考虑到天气类别预测的复杂性和 11 个类别的细粒度划分，所有模型均取得了令人满意的性能，特别是 GRU 模型的 68.4%准确率已接近人类专家水平（参考水平约为 75-80%）。

按天气类别分析，各模型在不同类别上的预测能力存在明显差异。主要天气类别（"晴"、"多云"、"阴"）的 F1 分数普遍较高，GRU 模型对这三类的 F1 分数分别达到 0.80、0.77 和 0.72。对于中等频率类别（如"小雨"、"中雨"），预测性能有所下降，GRU 模型的 F1 分数分别为 0.69 和 0.58。对于稀有天气类别（如"大雨"、"暴雨"、"大雪"），预测难度最大，即使表现最佳的 GRU 模型，F1 分数也仅为 0.45、0.33 和 0.21。这一结果反映了类别不平衡带来的预测挑战，尽管系统采用了类别权重和焦点损失等技术，稀有类别的预测性能仍有提升空间。值得注意的是，在实际应用中，对这些稀有但通常代表极端天气的准确预测尤为重要。

从预测时效性看，系统评估了不同预测时长下的模型表现。在 1 天预测窗口内，GRU 模型的准确率为 72.3%；延长至 3 天时，准确率降至 65.7%；而 7 天预测的准确率进一步降至 58.2%。这种随预测距离增加而性能下降的趋势符合气象预报的一般规律，反映了天气系统的混沌特性和长期预测的根本挑战。值得注意的是，在较长预测窗口下，所有模型的性能差距缩小，这可能表明长期预测更多依赖于气候学统计规律而非短期动力学特征。

图 4 不同模型天气类别预测效果对比图

在季节性能比较方面，模型在不同季节的预测准确性存在差异。所有模型在冬季(12-2 月)表现最佳，GRU 模型准确率达到 73.5%，这主要得益于冬季天气模式相对稳定，"晴"和"多云"天气占主导地位；春季(3-5 月)和秋季(9-11 月)次之，准确率分别为 67.8%和 69.2%；夏季(6-8 月)的预测准确率最低，为 65.1%，这与夏季对流天气发展迅速、变化复杂有关。针对季节性差异，系统实现的季节专用模型策略取得了良好效果，特别提升了夏季预测能力，使 GRU 模型在夏季的准确率提高了 2.7 个百分点。

转换准确性分析特别评估了模型对天气状态转换的预测能力。结果显示，GRU 模型在捕捉天气状态转换方面表现最佳，状态转换预测准确率达到 63.7%，明显优于 LightGBM 的 57.2%和随机森林的 55.8%。这验证了循环神经网络在处理序列转换问题上的优势。具体分析表明，天气状态的稳定性转换（如"晴"到"晴"、"阴"到"阴"）的预测准确率最高，达 80%以上；而剧烈转换（如"晴"到"大雨"、"雨"到"晴"）的准确预测较为困难，准确率通常不足 50%。此外，不同转换类型的预测难度也存在差异，例如"多云"到"阴"的转换预测准确率（72.3%）明显高于"阴"到"小雨"（59.1%），这反映了不同天气过程的内在可预测性差异。

图 5 天气类别转换准确性分析

误差分析揭示了模型预测错误的主要模式和来源。通过混淆矩阵分析发现，预测错误主要集中在相邻天气类别之间，如将"多云"误判为"晴"或"阴"，将"小雨"误判为"中雨"等。这些错误在一定程度上反映了天气类别划分本身的模糊性和主观性。特别是，所有模型都存在对极端天气类别的"低估"倾向，如将"大雨"预测为"中雨"、将"暴雨"预测为"大雨"的情况较为常见。这种保守预测模式可能源于类别不平衡和模型风险厌恶特性，是未来需要重点改进的方向。此外，误差还与特定气象条件相关，如在快速变化的天气系统过境期间，预测准确率明显降低，反映了动态天气过程建模的挑战。

实用性评估表明，天气类别预测模型具有显著的应用价值。从预警角度看，GRU 模型对重要天气转换（如转为"雨雪"类）的提前预警能力达到 65.3%，为公众出行和活动安排提供了有效参考；从使用便捷性看，类别预测比数值预测更直观，更适合公众理解和使用；从综合性看，天气类别预测集成了多种气象要素信息，提供了天气状况的整体描述。实际应用评估显示，系统部署后用户满意度达到 82.7%，特别是在户外活动计划和旅行安排方面获得了积极反馈。

针对天气类别预测的不确定性，系统还实现了概率输出功能，不仅预测最可能的天气类别，还提供各类别的概率分布。例如，预测结果可能为"明天多云的概率为 65%，阴的概率为 25%，小雨的概率为 10%"。这种概率预测方式更全面地传达了预测的不确定性，为用户决策提供了更丰富的信息。评估表明，概率校准性良好，预测概率与实际发生频率的相关系数达 0.92，显示出良好的可靠性。

总体而言，系统在天气类别预测任务上取得了满意的性能，特别是 GRU 模型展现出的优异表现为实际应用奠定了基础。未来工作将重点提升对极端天气类别和快速转换状态的预测能力，并探索融合数值天气预报和机器学习的混合方法，进一步提高预测准确性和时空覆盖范围。
