# 5.2 爬虫数据的清洗

原始爬取的气象和空气质量数据往往存在格式不一致、缺失值、异常值等问题，需要进行系统性清洗处理，才能为后续的分析与预测模型提供高质量的数据输入。本系统的数据清洗逻辑主要分布在爬虫脚本（`weather_spider.py`和`aqi_spider.py`）的解析部分及`utils.py`中的数据处理函数中。

## 5.2.1 缺失值处理

在气象与空气质量监测数据中，缺失值是常见的数据质量问题，可能由设备故障、传输错误或源数据本身不完整导致。本系统针对不同类型数据采用了差异化的缺失值处理策略。

1. **天气数据缺失值处理**

对于温度数据，`utils.py`中的`parse_temperature`函数实现了智能的缺失值推导逻辑：

```python
def parse_temperature(temp_range_str):
    """解析温度字符串，返回平均温度、最高温度和最低温度"""
    high_temp, low_temp, avg_temp = np.nan, np.nan, np.nan
    try:
        if isinstance(temp_range_str, str):
            if "/" in temp_range_str:
                parts = temp_range_str.split("/")
                high_str = parts[0].replace("℃", "").strip()
                low_str = parts[1].replace("℃", "").strip()

                try:
                    high_temp = float(high_str) if high_str else np.nan
                except ValueError:
                    pass

                try:
                    low_temp = float(low_str) if low_str else np.nan
                except ValueError:
                    pass

                # 智能填充策略：只有一个温度值缺失时
                if np.isnan(high_temp) and not np.isnan(low_temp):
                    high_temp = low_temp
                if not np.isnan(high_temp) and np.isnan(low_temp):
                    low_temp = high_temp

            # 计算平均温度
            if not np.isnan(high_temp) and not np.isnan(low_temp):
                avg_temp = (high_temp + low_temp) / 2.0
    except Exception as e:
        logger.error(f"解析温度字符串'{temp_range_str}'时出错: {e}")

    return high_temp, low_temp, avg_temp
```

该函数实现了三个关键缺失值处理策略：

- 当最高温度缺失但最低温度存在时，用最低温度填充最高温度
- 当最低温度缺失但最高温度存在时，用最高温度填充最低温度
- 只有在最高温度和最低温度都存在的情况下，才计算平均温度

2. **空气质量数据缺失值处理**

对于 AQI 指数和污染物浓度等数值型数据，系统在`aqi_spider.py`中通过`safe_float`和`safe_int`函数处理可能的缺失值：

```python
def safe_float(value_str, default=None):
    """安全地将字符串转换为浮点数"""
    if not value_str or value_str == "-":
        return default
    try:
        value_str = value_str.replace(",", "")  # 移除可能的千位分隔符
        return float(value_str)
    except (ValueError, TypeError):
        print(f"警告: 无法将 '{value_str}' 转换为浮点数")
        return default
```

如图 5.2-1 所示，系统对比了 PM2.5 浓度缺失值处理前后的数据分布，处理后的数据完整性显著提高，为后续模型训练奠定了基础。

图 5.2-1 PM2.5 浓度缺失值处理前后对比

## 5.2.2 异常值检测与修正

异常值不仅会影响数据分析的准确性，还可能导致预测模型性能下降。本系统采用多层次策略对异常值进行检测与修正。

1. **格式层面的异常检测**

在数据爬取阶段，系统通过严格的格式验证过滤明显的格式错误：

```python
# 日期格式验证
try:
    date_str_formatted = date_str.replace("年", "-").replace("月", "-").replace("日", "")
    datetime.strptime(date_str_formatted, "%Y-%m-%d")  # 验证格式
    return date_str_formatted
except ValueError:
    pass  # 格式错误，尝试下一个解析策略
```

2. **数值范围检测**

在`utils.py`中，系统对关键气象指标设定了合理的物理范围限制，超出范围的值被标记为异常：

```python
# 温度异常值检测示例代码
def is_temperature_anomaly(temp_value):
    """检测温度值是否异常（超出合理范围）"""
    if temp_value is None or np.isnan(temp_value):
        return False
    # 中国气温范围约为-50℃到50℃
    return temp_value < -50 or temp_value > 50
```

3. **统计学异常检测**

系统在数据加载到 Pandas DataFrame 后，使用箱线图（IQR 法则）进行异常值的统计检测：

```python
def detect_outliers_iqr(df, column, multiplier=1.5):
    """使用IQR方法检测异常值"""
    Q1 = df[column].quantile(0.25)
    Q3 = df[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - multiplier * IQR
    upper_bound = Q3 + multiplier * IQR
    outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]
    return outliers
```

如图 5.2-2 所示，系统对平均温度数据进行了异常值检测，并标记出超出正常范围的数据点。

图 5.2-2 平均温度异常值检测示例

对于检测出的异常值，系统根据数据类型和异常程度采用不同的处理策略：

- 对于轻微异常，使用中位数或线性插值替换
- 对于严重异常，如果样本量足够，考虑直接删除
- 对于时序数据，使用前后时间点的均值填充

## 5.2.3 数据标准化

数据标准化是确保数据一致性和可比性的关键步骤，涉及格式统一、类型转换和量纲调整等方面。

1. **日期格式标准化**

系统通过`parse_date_weather`和`parse_date_aqi`函数将各种格式的日期统一转换为 ISO 标准的"YYYY-MM-DD"格式：

```python
def parse_date_aqi(date_str):
    """将日期字符串转换为 'YYYY-MM-DD' 格式，使用多种解析策略"""
    # 策略1: 尝试已知日期格式解析
    try:
        # 如果已经是标准格式
        datetime.strptime(date_str, "%Y-%m-%d")
        return date_str
    except ValueError:
        pass

    # 策略2: 尝试中文日期格式
    try:
        date_str_formatted = date_str.replace("年", "-").replace("月", "-").replace("日", "")
        datetime.strptime(date_str_formatted, "%Y-%m-%d")
        return date_str_formatted
    except ValueError:
        pass

    # 策略3: 使用正则表达式提取年月日
    try:
        pattern = r"(\d{4}).*?(\d{1,2}).*?(\d{1,2})"
        match = re.search(pattern, date_str)
        if match:
            year, month, day = match.groups()
            month = month.zfill(2)  # 确保月是两位数
            day = day.zfill(2)      # 确保日是两位数
            formatted = f"{year}-{month}-{day}"
            datetime.strptime(formatted, "%Y-%m-%d")  # 验证格式
            return formatted
    except (ValueError, AttributeError):
        pass

    # 如果都失败，输出警告
    print(f"警告：无法解析的日期格式: {date_str}")
    return None
```

2. **天气状况标准化**

为了处理天气状况描述的多样性（如"晴转多云"、"小雨/中雨"等），系统在`utils.py`中实现了`map_weather_condition`函数，将复杂的天气描述映射为标准化的分类：

```python
def map_weather_condition(original_condition):
    """将复杂的天气状况描述转换为简单分类"""
    # 处理空值或非字符串
    if original_condition is None or not isinstance(original_condition, str):
        return DEFAULT_CATEGORY

    # 直接匹配映射表
    if original_condition in WEATHER_CONDITION_MAP:
        return WEATHER_CONDITION_MAP[original_condition]

    # 对于形如"晴/多云"的组合天气，取第一部分
    if "/" in original_condition:
        first_part = original_condition.split("/")[0].strip()
        if first_part in WEATHER_CONDITION_MAP:
            return WEATHER_CONDITION_MAP[first_part]
        else:
            return DEFAULT_CATEGORY
    else:
        # 完全找不到匹配
        return DEFAULT_CATEGORY
```

如图 5.2-3 所示，系统定义了全面的天气状况映射表，确保所有气象描述都能映射到一致的分类体系中。

图 5.2-3 天气状况描述映射表示例

3. **数值类型和单位统一**

系统对所有数值型数据进行了类型检查和单位统一：

- 温度统一使用摄氏度（℃）
- 污染物浓度根据类型统一单位（PM2.5、PM10 单位为 μg/m³，CO 单位为 mg/m³ 等）
- 所有数值型数据统一转换为 Python 的 float 或 int 类型

## 5.2.4 数据库存储实现

清洗和标准化后的数据最终存储到 SQLite 数据库中，实现持久化管理。系统通过`database.py`中定义的函数进行数据库连接管理。

1. **数据库表结构设计**

系统在`weather_spider.py`和`aqi_spider.py`中的`check_tables_exist`函数中定义了核心数据表结构：

```python
def check_tables_exist(conn):
    """检查数据库中是否存在必要的表"""
    cursor = conn.cursor()

    # 检查weather_data表是否存在
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS weather_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            city TEXT NOT NULL,
            date TEXT NOT NULL,
            weather_condition TEXT,
            temperature_range TEXT,
            wind_info TEXT,
            UNIQUE(city, date)
        )
        """
    )

    # 检查aqi_data表是否存在
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS aqi_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            city TEXT NOT NULL,
            date TEXT NOT NULL,
            quality_level TEXT,
            aqi_index INTEGER,
            pm25 REAL,
            pm10 REAL,
            so2 REAL,
            no2 REAL,
            co REAL,
            o3 REAL,
            UNIQUE(city, date)
        )
        """
    )
    conn.commit()
```

如图 5.2-4 所示，系统设计了两个核心数据表：`weather_data`和`aqi_data`，分别存储天气数据和空气质量数据。两个表都通过`city`和`date`字段设置唯一性约束，确保数据的一致性和完整性。

图 5.2-4 数据库表结构设计示意图

2. **数据库访问模式**

系统通过`database.py`中的`get_db`和`get_user_db`函数统一管理数据库连接：

```python
def get_db():
    """获取当前请求上下文的主数据库连接 (data.db)"""
    db = getattr(g, "_database", None)
    if db is None:
        try:
            db = g._database = sqlite3.connect(DATABASE)
            db.row_factory = sqlite3.Row  # 让查询结果可以通过列名访问
        except sqlite3.Error as e:
            logger.error(f"无法连接到数据库 {DATABASE}: {e}", exc_info=True)
            return None
    return db
```

3. **事务处理与并发控制**

系统使用 SQLite 的事务机制确保数据写入的原子性和一致性：

```python
try:
    # 开始事务
    conn.execute("BEGIN TRANSACTION")

    # 数据插入操作
    cursor.execute(
        "INSERT OR IGNORE INTO weather_data (city, date, weather_condition, temperature_range, wind_info) VALUES (?, ?, ?, ?, ?)",
        (city, date, weather_condition, temperature_range, wind_info)
    )

    # 提交事务
    conn.commit()

except sqlite3.Error as e:
    # 回滚事务
    conn.rollback()
    print(f"数据库错误: {e}")
```

通过上述数据清洗和存储机制，系统确保了气象和空气质量数据的高质量、一致性和可靠性，为后续的数据分析和预测模型提供了坚实的数据基础。
