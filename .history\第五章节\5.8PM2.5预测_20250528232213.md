5.8 PM2.5 预测

PM2.5（细颗粒物）作为最主要的空气污染物之一，对人体健康和环境质量具有重大影响。准确预测 PM2.5 浓度变化对环境监管和公众健康防护具有重要意义。本系统针对眉山市 PM2.5 浓度数据，构建了专用预测模型，实现了对 PM2.5 浓度的高精度预测，为空气质量管理提供了科学依据。

5.8.1 PM2.5 预测特性与挑战

PM2.5 预测与常规气象要素预测和 AQI 综合指数预测均有显著差异，具有其独特的特点和挑战。系统在构建 PM2.5 预测模型时，重点考虑了以下几个关键特性：

首先，PM2.5 浓度具有显著的时空分布不均特性。眉山市 PM2.5 浓度在空间和时间维度上均表现出较大的变异性。空间上，不同区域由于地形、产业布局和人口密度的差异，PM2.5 浓度可能相差数倍；时间上，受气象条件和排放强度影响，PM2.5 浓度可在短时间内剧烈波动。数据分析显示，眉山市中心城区的 PM2.5 年均浓度约为 35.3μg/m³，而工业区域可达 47.8μg/m³，差异明显。在时间尺度上，日内最大值与最小值的比例通常在 2-4 倍之间，且存在明显的日变化和季节变化规律。这种高度的时空变异性使得 PM2.5 预测需要考虑更多的局地特征和时间模式。

其次，PM2.5 浓度受多种复杂因素的综合影响。与 AQI 不同，PM2.5 是单一污染物指标，但其来源和影响因素却十分复杂。主要影响因素包括：人为排放源（工业生产、燃煤取暖、机动车尾气等）、气象条件（风速、湿度、大气稳定度等）、地理环境（地形、植被覆盖等）以及区域传输贡献。相关性分析显示，眉山市 PM2.5 浓度与相对湿度的正相关系数为 0.53，与风速的负相关系数为-0.57，与温度的相关关系则呈现季节性变化。这些复杂的影响机制使得 PM2.5 预测需要综合考虑多种因素的交互作用。

第三，PM2.5 浓度数据表现出明显的非线性和非平稳特征。PM2.5 浓度随环境条件变化呈现出高度非线性响应，且长期趋势受环保政策影响显著。分析显示，眉山市 PM2.5 年均浓度从 2020 年的 39.6μg/m³ 下降至 2023 年的 31.2μg/m³，降幅达 21.2%。同时，PM2.5 浓度与气象要素的关系也表现为复杂的非线性模式，如在高湿度条件下，PM2.5 与湿度的正相关性会显著增强，反映了湿度对颗粒物吸湿增长的促进作用。这种非线性和非平稳特性对预测模型的适应能力提出了更高要求。

此外，PM2.5 预测面临数据质量和异常值处理的挑战。监测数据中存在的噪声、异常值和缺失值会严重影响预测精度。统计分析发现，眉山市 PM2.5 监测数据中约有 2.3%的数据点为异常值（与相邻时间点偏差超过 200%），这些异常值通常与监测设备故障或校准过程有关。同时，数据完整率方面也存在挑战，特别是在极端天气条件下，数据缺失率可能达到 5%以上。这些数据质量问题要求模型具有强大的鲁棒性和有效的异常处理能力。

图 1 眉山市 2020-2024 年 PM2.5 浓度变化趋势图

最后，PM2.5 预测还需要关注极端污染事件的预警能力。针对重污染天气的预测尤其具有挑战性，但同时也最为重要。数据显示，眉山市 2020-2024 年间共发生 PM2.5 日均浓度超过 75μg/m³ 的重污染天气 52 天，其中 21 天与区域性污染传输直接相关。这类重污染事件对模型的预测极限形成了挑战，也是评估模型实用价值的重要标准。

5.8.2 PM2.5 预测的特有特征与模型调整

针对 PM2.5 预测的特点和挑战，系统设计了专门的特征工程策略和模型优化方案，以提升预测准确性。

（1）针对 PM2.5 的特征优化

在 PM2.5 预测的特征工程中，系统构建了五类特征集合：PM2.5 本体特征、气象影响特征、区域传输特征、时间模式特征以及排放活动特征。

PM2.5 本体特征是预测模型的核心输入。系统构建了多尺度的 PM2.5 历史特征，包括多个时间窗口的滞后值（前 1 小时、3 小时、6 小时、12 小时、1 天、2 天、3 天、7 天）和统计特征。特别地，系统发现前 24 小时的 PM2.5 加权移动平均与未来浓度的相关性最高，相关系数达 0.83，因此构建了特殊的指数衰减加权平均特征，赋予近期数据更高权重。此外，系统还构建了 PM2.5 浓度变化率特征，捕捉浓度上升或下降的趋势和速率，这对预测拐点和趋势变化尤为重要。

气象影响特征反映了大气环境对 PM2.5 的作用机制。系统分析表明，气象条件是影响 PM2.5 浓度的关键因素，因此构建了全面的气象特征集：风特征包括风速、风向、风速的平方项（反映风对污染物的稀释作用符合二次关系）；温度特征包括气温、地表温度及其差值（反映大气稳定度）；湿度特征包括相对湿度、绝对湿度及其与温度的交互项；气压特征包括气压值及其变化率，气压变化通常伴随天气系统转变。特别地，系统还构建了大气扩散条件指数，综合考虑风速、大气稳定度和混合层高度，量化大气对污染物的扩散能力。分析显示，该指数与 PM2.5 浓度的负相关性达-0.72，是预测模型中的关键特征。

区域传输特征考虑了 PM2.5 的区域流动特性。PM2.5 具有显著的区域传输特征，上风向城市的污染物可能对目标区域产生重要影响。系统构建了基于风场的传输路径特征：首先计算 24 小时后向轨迹，确定潜在源区域；然后引入周边城市（成都、德阳、乐山等）的 PM2.5 监测数据作为预测特征；特别地，系统开发了风向加权的区域 PM2.5 贡献模型，根据风向和风速对不同城市的贡献进行动态加权。统计显示，在西北风条件下，成都市 PM2.5 浓度每上升 10μg/m³，眉山市 PM2.5 浓度在 6-12 小时后平均上升 4.7μg/m³，验证了区域传输特征的预测价值。

时间模式特征捕捉了 PM2.5 的时间变化规律。系统构建的时间特征包括三个层次：首先是时点特征，如小时、星期几、月份等基础时间标记；其次是周期性编码特征，使用三角函数对时间进行周期性编码，如 sin(2π·hour/24)、cos(2π·hour/24)等；第三是特殊时间段标记，如夜间时段、交通高峰时段等。分析表明，眉山市 PM2.5 浓度具有明显的日内变化规律，通常在早晨 7-9 点和晚上 19-21 点出现两个峰值，这与交通出行高峰和大气稳定度变化有关。系统通过这些时间特征成功捕捉了这种规律性变化模式。

排放活动特征反映了人为排放对 PM2.5 的贡献。系统创新性地引入了多种反映人为活动强度的特征：工作日/周末标记反映了工作模式对排放的影响；节假日标记捕捉了特殊时期的排放变化；季节性活动标记如冬季采暖期标记、秸秆焚烧高发期标记等则反映了季节性排放活动。特别地，系统还尝试引入了活动强度代理特征，如交通流量指数、供暖需求指数（基于供暖度日）等，进一步量化人为排放强度。分析显示，在其他条件相似的情况下，工作日的 PM2.5 平均比周末高 3.6μg/m³，而春节假期期间则比平常低约 8.5μg/m³。

图 2 PM2.5 预测特征重要性排序图

（2）模型参数定制与调优

针对 PM2.5 预测任务的特点，系统对各预测模型进行了针对性优化，以最大限度提升预测性能。

LightGBM 作为系统中 PM2.5 预测的核心模型，其参数配置经过了全面优化。考虑到 PM2.5 浓度数据的高度噪声和异常值，系统选择了 huber 损失函数作为优化目标，这种损失函数结合了 MSE 和 MAE 的优点，对中等大小的误差使用平方惩罚，对大误差使用线性惩罚，提高了模型的鲁棒性。为适应 PM2.5 数据的复杂模式，系统设置了较大的 n_estimators 参数(1500)，通过较小的 learning_rate(0.01)保持训练稳定性并防止过拟合。特别地，系统还采用了正则化参数调整，设置了适当的 lambda_l1(0.5)和 lambda_l2(0.5)参数，增强模型的泛化能力。在树结构参数方面，系统将 max_depth 限制在 8，num_leaves 设为 40，以平衡模型的复杂度和泛化性能。此外，针对 PM2.5 数据的不平衡分布（高浓度样本较少），系统实现了基于浓度分层的采样策略，提高了模型对高浓度情况的预测能力。

LSTM 模型在 PM2.5 预测中针对时序依赖进行了特殊优化。系统设置了较短的 look_back 参数(168 小时，即 7 天)，这是基于对 PM2.5 时间依赖性的详细分析得出的最佳长度，能够覆盖主要的时间依赖同时避免引入过多噪声。为提高模型对复杂时序模式的捕捉能力，系统设计了双层 LSTM 结构，第一层包含 96 个神经元，第二层包含 48 个神经元，形成层次化特征提取。为防止过拟合，应用了适度的 dropout(0.3)策略。在激活函数选择上，系统使用了 LeakyReLU 代替传统的 tanh 激活函数，以缓解梯度消失问题并提高训练效率。此外，系统还实现了注意力机制(Attention Mechanism)，使模型能够动态关注序列中的重要部分，这一改进使预测准确度提高了约 7.3%。在训练策略上，采用了 batch size 为 128 的设置，并结合了余弦退火学习率调度器，初始学习率为 0.001，随训练进程动态调整，有效提高了模型收敛速度和稳定性。

TCN（时间卷积网络）模型是系统针对 PM2.5 预测特别引入的深度学习架构。相比常规 RNN 模型，TCN 通过因果卷积和膨胀卷积能够更高效地处理长序列数据，同时保持并行计算优势。系统配置的 TCN 模型包括 4 个残差块，每个残差块包含 2 个一维卷积层，卷积核大小为 3，膨胀率分别为 1、2、4、8，实现了感受野为 255 个时间步的覆盖，能够有效捕捉近 8 天的时间依赖模式。同时，系统在每个卷积层后应用了权重标准化和空间 dropout(0.2)，增强模型的泛化能力。该模型在 PM2.5 预测中表现出色，特别是在捕捉短期波动和季节性模式方面优势明显，预测 RMSE 比 LSTM 模型低约 5.2%。

图 3 PM2.5 预测模型训练过程可视化

此外，系统还采用了样本加权策略针对性优化高浓度 PM2.5 的预测能力。实际应用中，高浓度 PM2.5 的准确预测对环境管理和公众健康更为关键，但由于高值样本在训练数据中比例较低，模型往往对这些情况预测不足。系统通过样本加权技术，对浓度超过 75μg/m³ 的样本赋予更高权重(2 倍)，对超过 150μg/m³ 的样本赋予更高权重(3 倍)，显著提升了模型在高浓度条件下的预测性能，重污染天气的预测准确率从原来的 63.7%提升至 78.2%。

5.8.3 PM2.5 预测结果分析与比较

基于眉山市 2020-2024 年的高时间分辨率监测数据，系统对各模型的 PM2.5 预测性能进行了全面评估和比较。评估过程不仅关注整体预测精度，还特别分析了不同浓度区间、不同季节和极端事件下的预测能力，全面呈现了各模型的性能特点。

从整体预测精度看，LightGBM 模型表现最佳，在日均值预测任务上，MAE 为 3.12μg/m³，RMSE 为 6.42μg/m³，R² 达到 0.898，MAPE 为 10.43%；在小时值预测任务上，MAE 为 4.86μg/m³，RMSE 为 8.73μg/m³，R² 为 0.837，MAPE 为 15.62%。TCN 模型紧随其后，日均值预测的 MAE 为 3.97μg/m³，RMSE 为 7.15μg/m³，R² 为 0.868；小时值预测的 MAE 为 5.28μg/m³，RMSE 为 9.24μg/m³，R² 为 0.802。LSTM 模型表现相对较弱，日均值预测的 MAE 为 4.53μg/m³，R² 为 0.832；小时值预测的 MAE 为 6.11μg/m³，R² 为 0.754。这一结果表明，对于复杂的 PM2.5 预测任务，基于梯度提升决策树的 LightGBM 模型具有明显的优势，而深度学习模型中 TCN 的性能优于 LSTM。

分浓度区间分析显示，模型在不同 PM2.5 浓度水平下的预测准确性存在显著差异。在低浓度区间（0-35μg/m³），所有模型表现都相对较好，LightGBM 的 MAE 仅为 2.45μg/m³，预测误差率约 8.7%；在中等浓度区间（35-75μg/m³），预测难度增加，LightGBM 的 MAE 增至 4.18μg/m³，误差率约 7.6%；在高浓度区间（>75μg/m³），预测难度最大，LightGBM 的 MAE 达到 7.63μg/m³，但相对误差率反而降至 7.2%，表明模型对高值的预测也具有良好的相对准确性。相比之下，LSTM 和 TCN 模型在高浓度区间的预测性能下降更为显著，MAE 分别达到 12.87μg/m³ 和 9.35μg/m³。这一现象可能与 LightGBM 模型的样本加权策略有关，该策略有效提升了高浓度条件下的预测能力。

从时效性角度分析，系统比较了不同预测时长下的模型表现。在短期预测（1-12 小时内）中，LightGBM 和 TCN 模型表现接近，MAE 分别为 3.82μg/m³ 和 4.12μg/m³；在中期预测（1-3 天）中，LightGBM 模型优势明显，MAE 为 5.27μg/m³，而 TCN 和 LSTM 的 MAE 分别为 7.13μg/m³ 和 8.56μg/m³；在长期预测（7 天以上）中，所有模型精度均显著下降，但 LightGBM 依然表现最佳，MAE 为 9.83μg/m³。这一结果表明，PM2.5 预测的难度随预测时长延长而显著增加，三天以内的预测具有较好的实用价值，而更长期预测则主要用于把握整体趋势。

图 4 不同模型 PM2.5 预测效果对比图

季节性能比较揭示了模型在不同季节的预测能力差异。分析显示，夏季（6-8 月）的预测精度最高，LightGBM 模型的 MAE 仅为 2.37μg/m³，这主要得益于夏季 PM2.5 浓度较低且相对稳定；春季（3-5 月）和秋季（9-11 月）次之，MAE 分别为 3.26μg/m³ 和 3.05μg/m³；冬季（12-2 月）则是最具挑战性的季节，MAE 升至 4.81μg/m³，这与冬季采暖排放增加、气象条件复杂多变有关。值得注意的是，各模型在不同季节的相对表现也存在差异，LightGBM 在所有季节均表现出色，而 LSTM 模型在夏季的相对表现优于其他季节，TCN 模型则在季节交替期（如春秋季）表现更为稳定。

针对极端污染事件的预测能力是评估模型实用价值的重要指标。系统选取了 2023 年发生的 3 次典型 PM2.5 重污染过程（日均浓度>75μg/m³ 且持续 ≥2 天）进行专项评估。结果显示，LightGBM 模型能够提前 1-2 天预测重污染过程的发生，平均提前预警时间为 32 小时，浓度峰值预测误差约为 15.3%；TCN 模型的预警能力次之，平均提前预警时间为 24 小时，峰值预测误差约为 21.7%；LSTM 模型表现最弱，预警时间平均仅为 18 小时，且峰值预测普遍偏低约 28.4%。此外，系统还评估了重污染结束时间的预测准确性，LightGBM 模型的平均预测误差为 12 小时，明显优于 TCN 的 19 小时和 LSTM 的 23 小时。这一结果表明，LightGBM 模型在重污染预警方面具有显著优势，能够为环境管理和公众健康提供宝贵的预警时间。

图 5 PM2.5 极端污染事件预测效果分析

误差分析揭示了模型预测误差的主要来源和分布特征。通过残差分析发现，LightGBM 模型的预测误差主要集中在三类情况：一是污染快速累积初期，模型预测值通常低于实际值，表现出一定的滞后性；二是污染快速消散阶段，模型预测同样存在滞后，预测值通常高于实际值；三是特殊气象条件下（如强对流天气过程），预测误差明显增大。这些特点表明，模型对 PM2.5 浓度的突变点把握还有提升空间。另外，误差分布分析显示，LightGBM 模型的误差呈近似正态分布，95%的预测误差落在 ±12μg/m³ 范围内，表明模型预测结果的可靠性较高。

实用性评估表明，基于 LightGBM 的 PM2.5 预测模型具有极高的应用价值。从计算效率看，该模型训练一次仅需约 3 分钟，预测一次仅需约 0.1 秒，完全满足实时预测需要；从可解释性看，模型能够输出直观的特征重要性分析结果，帮助理解影响 PM2.5 的主要因素；从适应性看，模型支持增量更新，能够根据最新数据持续优化。在实际应用中，系统采用每日更新的策略，使用最新的监测数据重新训练模型，确保预测性能的持续稳定。

总体而言，系统在 PM2.5 预测任务上取得了优异的性能，特别是 LightGBM 模型显示出的高精度预测能力为空气质量管理和公众健康防护提供了有力支持。未来工作将重点优化对污染快速变化期的预测能力，并探索结合更多数据源（如卫星遥感观测、化学传输模型输出等）以进一步提升预测准确性和时空覆盖范围。
