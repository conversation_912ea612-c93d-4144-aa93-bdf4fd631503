5.9 O3 预测

臭氧(O3)是近年来我国城市空气质量的主要污染物之一，是形成光化学烟雾的主要成分，对人体健康和生态环境具有明显危害。不同于 PM2.5 等一次污染物，O3 是由氮氧化物(NOx)和挥发性有机物(VOCs)在阳光照射下通过复杂的光化学反应形成的二次污染物，其生成机制和影响因素更为复杂。本系统针对眉山市 O3 污染特点，构建了专门的 O3 预测模型，实现了对臭氧浓度的科学预测。

5.9.1 O3 预测特性与挑战

O3 预测区别于其他污染物预测，具有独特的特点和难点。系统在设计 O3 预测模型时，特别关注了以下几个关键特性：

首先，O3 具有显著的季节性和日变化规律。与 PM2.5 和其他污染物不同，O3 浓度在夏季达到峰值，冬季则明显降低。数据分析显示，眉山市 O3 最高浓度主要出现在 5-9 月，其中 6-8 月浓度最高，日最大 8 小时平均浓度(MDA8)在夏季可达 160-180μg/m³，而冬季仅为 60-80μg/m³。在日变化方面，O3 浓度通常在午后 13-15 时达到峰值，深夜和清晨最低，日内最大值与最小值之比可达 10 倍以上。这种强烈的日变化特性要求预测模型具有较高的时间分辨率，能够准确捕捉白天和夜间的浓度差异。

其次，O3 形成受光照、温度等气象条件的决定性影响。作为典型的光化学污染物，O3 的生成强烈依赖于阳光(特别是紫外辐射)和温度。相关性分析显示，眉山市 O3 浓度与日照强度的相关系数达 0.75，与气温的相关系数为 0.69，这远高于其与前体物浓度的相关性。特别是在夏季高温晴朗天气下，光化学反应加速，极易形成 O3 污染。统计数据表明，当日最高温度超过 30℃ 且日照时间超过 6 小时时，O3 超标概率高达 68%，而在其他条件下仅为 12%。这种对气象条件的高度敏感性使得 O3 预测必须充分考虑气象因素的影响。

第三，O3 与其前体物之间存在复杂的非线性关系。O3 的生成取决于 NOx 和 VOCs 的浓度比例，而非简单的正相关关系。在 VOCs 控制区域，减少 NOx 反而会导致 O3 浓度上升；而在 NOx 控制区域，情况则相反。分析表明，眉山市区域总体处于 VOCs 控制区域，这导致了复杂的"周末效应"——虽然周末 NOx 排放减少，但 O3 浓度反而高于工作日。数据显示，2023 年眉山市周末 O3 MDA8 平均值比工作日高 7.3%，这一特性对预测模型提出了更高要求，简单的线性模型难以准确捕捉这种非线性关系。

此外，O3 具有显著的区域传输特性。由于 O3 及其前体物的大气寿命较长，区域传输对局地 O3 浓度有重要影响。统计分析发现，在特定气象条件下，成都市 O3 污染事件通常会在 6-12 小时后影响到眉山市，平均传输贡献率约为 25-30%。区域传输的复杂性增加了预测的难度，要求模型能够考虑上风向城市的污染状况和空气质量变化。

最后，O3 预测面临数据可获取性的挑战。虽然 O3 监测数据相对完整，但其关键前体物（特别是 VOCs 组分）的监测数据通常缺乏或分辨率不足，这限制了基于前体物的预测能力。数据评估显示，眉山市仅有两个站点具备部分 VOCs 监测能力，且监测频率仅为每天一次，无法满足高时间分辨率预测需求。这种数据限制要求模型能够通过其他可获取数据（如气象、常规污染物等）间接推断 O3 形成的有利条件。

图 1 眉山市 2020-2024 年 O3 浓度季节变化趋势图

5.9.2 O3 预测的特有特征与模型调整

针对 O3 预测的特点和挑战，系统设计了专门的特征工程方法和模型参数优化策略，以提高预测准确性。

（1）针对 O3 的特征优化

在 O3 预测的特征工程中，系统重点构建了四类关键特征：光化学条件特征、前体物特征、气象条件特征和时空模式特征。

光化学条件特征是 O3 预测的核心特征集。由于 O3 是光化学反应产物，其生成强烈依赖于光照条件。系统构建了多维光照相关特征：首先是日照时长和强度特征，包括日照时数、太阳辐射强度（实测或推算值）、透明度指数等；其次是紫外辐射特征，O3 生成对 UV-B 辐射特别敏感，系统基于天气状况和季节推算了紫外线强度指数；第三是光化学反应潜力指数，这是系统创新开发的综合指标，结合温度、辐射强度和大气稳定度，量化一天中的光化学反应潜力。分析表明，该指数与日最大 O3 浓度的相关系数高达 0.81，显著高于单一气象要素的相关性。此外，系统还考虑了云量和能见度特征，这些因素通过影响到达地表的辐射量间接影响 O3 形成。

前体物特征反映了 O3 形成的化学基础。虽然 VOCs 组分数据有限，系统仍然构建了可行的前体物特征集：首先利用了可获取的 NOx 数据，包括 NO 和 NO2 浓度及其比值，NO2/NO 比值对光化学活性有重要指示意义；其次引入了 CO 浓度作为 VOCs 的代理指标，CO 与部分 VOCs 具有相似来源，可在一定程度上反映 VOCs 排放强度；第三是构建了 NOx 与 CO 的比值特征，这一比值可粗略反映大气光化学敏感性区域（NOx 控制或 VOCs 控制）；此外，系统尝试引入了 O3 前体物指数，这是基于可用监测数据和排放清单推算的指标，间接反映光化学臭氧生成潜势。值得注意的是，系统发现 O3 与 NO2 呈现出"滞后-负相关"的特征，即当日高 NO2 常对应次日低 O3，这一特征被模型有效利用。

气象条件特征是影响 O3 生成和传输的关键因素。系统构建了全面的气象特征集：首先是温度相关特征，包括日均温、日最高温、日温差和累积温度指数（反映持续高温效应）；其次是风场特征，包括风速、风向和风速变异系数，分析显示，低风速对应高 O3 概率，风速小于 2m/s 时 O3 超标率增加 46%；第三是大气稳定度特征，包括温度递减率、混合层高度和通风系数等，这些特征反映大气垂直混合能力，与污染物积累紧密相关；此外，还考虑了相对湿度、气压和降水特征，分析表明 O3 与相对湿度呈负相关，湿度每增加 10%，O3 平均降低约 4.8μg/m³。系统特别构建了持续高温特征，如连续高温天数和高温积累指数，用于识别容易形成 O3 污染的天气系统。

时空模式特征捕捉 O3 的时间演变和空间分布规律。在时间维度，系统构建了多尺度特征：基础时间特征包括小时、日期、月份、季节等；周期性编码特征使用三角函数进行编码，更好地表达周期性；滞后特征捕捉 O3 的时序依赖，包括前 1 天、2 天、3 天、7 天的 O3 浓度及其最大值；特别地，系统发现前 3 天最高 O3 浓度的均值与当天最高 O3 浓度具有较强相关性（相关系数 0.76），因此构建了历史 O3 极值特征。在空间维度，系统引入了周边站点和城市的 O3 观测数据，构建了空间相关性特征，以捕捉区域传输模式。此外，还开发了 O3 传输潜势指数，该指数基于风场和上风向城市的 O3 水平，评估区域传输对本地 O3 的潜在贡献。

图 2 O3 预测特征重要性排序图

（2）模型参数定制与调优

针对 O3 预测任务的特征，系统对预测模型进行了专门的参数优化和结构调整。

LightGBM 作为系统中 O3 预测的基础模型，其参数配置经过了系统化优化。考虑到 O3 预测的重点是准确捕捉峰值，系统选择了均方误差（MSE）作为优化目标，以增强对高值的预测能力。为适应 O3 数据的复杂非线性模式，系统设置了较大的 n_estimators 参数(1000)，以提供足够的模型复杂度，同时通过较小的 learning_rate(0.02)防止过拟合。特别针对 O3 数据的非对称性，系统实施了样本加权策略，对高浓度样本（MDA8>160μg/m³）赋予更高权重(1.5 倍)，以提高对超标事件的预测准确率。在特征选择方面，系统通过 feature_fraction 参数(0.85)控制随机特征选择比例，增强模型的泛化能力。为进一步提高对极端情况的预测能力，系统还采用了交叉验证调整的方法，针对臭氧高发季节（5-9 月）的数据单独优化模型参数，如增大 max_depth 至 10，提升模型的表达能力。这种季节性参数调整策略显著提高了高臭氧季节的预测准确性，模型在夏季的 MAE 比全年通用参数模型降低了约 12.3%。

GRU（门控循环单元）模型是系统针对 O3 时序预测特别优化的深度学习架构。与 LSTM 相比，GRU 结构更为简洁，但保留了处理长期依赖的能力，更适合 O3 这类具有明显日变化和季节性的时序数据。系统配置的 GRU 模型具有如下特点：时序输入长度设为 168 小时（7 天），足以涵盖周变化模式；网络结构为双层 GRU，第一层 100 个单元，第二层 50 个单元，在两层之间添加批量归一化层以加速训练和提高泛化能力；为处理 O3 浓度的日变化模式，系统创新性地引入了时间嵌入层(Time Embedding)，将小时信息转换为密集向量表示，增强模型对时间的感知能力；此外，系统还实现了注意力机制，使模型能够自动关注历史序列中与当前预测最相关的时间点。在激活函数的选择上，输出层使用 ReLU 激活函数，确保预测结果非负。训练过程采用了早停策略和学习率衰减调度器，初始学习率 0.001，每 10 轮无改善则减半。实验表明，与标准 LSTM 相比，优化后的 GRU 模型在 O3 预测上 MAE 降低了约 8.7%，计算效率提升约 25%。

系统还针对 O3 预测开发了基于 XGBoost 的集成模型。XGBoost 在处理 O3 这类存在复杂非线性关系的预测任务时表现出色。系统优化的 XGBoost 模型具有以下特点：采用树的最大深度为 9，最小子节点样本数为 20，以平衡模型复杂度和泛化能力；使用子采样率 0.8 和特征采样率 0.85，增强模型的鲁棒性；正则化参数 lambda 设为 1.2，alpha 设为 0.5，有效控制过拟合风险。特别地，系统实施了分层建模策略，针对不同 O3 浓度区间训练专门的子模型：低浓度模型（<100μg/m³）、中浓度模型（100-160μg/m³）和高浓度模型（>160μg/m³），然后通过元学习器整合这些子模型的预测结果。这种分层建模方法使预测性能提升显著，特别是对 O3 超标事件的捕捉能力提高了约 15.8%。

图 3 O3 预测模型训练过程可视化

针对 O3 预测的特殊需求，系统还创新性地实现了时空融合模型。该模型结合了传统机器学习方法与深度学习技术，同时考虑时间依赖和空间相关性。具体而言，系统首先使用 CNN(卷积神经网络)处理空间特征，捕捉区域污染传输模式；然后使用 GRU 处理时间序列特征；最后通过注意力机制融合这两部分信息，生成最终预测。这种时空融合架构在捕捉 O3 的区域传输特性方面表现出色，特别是在受上风向城市影响显著的污染事件中，预测准确率比单纯的时序模型提高了约 11.5%。

5.9.3 O3 预测结果分析与比较

基于眉山市 2020-2024 年的高时间分辨率监测数据，系统对各模型的 O3 预测能力进行了全面评估和比较。评估重点关注日最大 8 小时平均浓度(MDA8)的预测性能，这是 O3 评价和管理的核心指标。

从整体预测精度看，LightGBM 模型表现最优，MDA8 预测的 MAE 为 13.24μg/m³，RMSE 为 17.82μg/m³，R² 达到 0.83，MAPE 为 16.37%。XGBoost 模型次之，MAE 为 14.61μg/m³，RMSE 为 19.05μg/m³，R² 为 0.81。GRU 模型表现略逊，MAE 为 16.28μg/m³，RMSE 为 21.76μg/m³，R² 为 0.77。时空融合模型在区域传输明显的情况下表现优异，但在整体数据集上与 LightGBM 表现相当，MAE 为 13.56μg/m³，R² 为 0.82。这一结果表明，针对 O3 这类受多因素影响的污染物，基于梯度提升树的模型通常具有更好的整体预测能力。

按浓度区间分析，模型在不同 O3 浓度级别下的预测性能存在明显差异。在低浓度区间（MDA8<100μg/m³），所有模型表现良好，LightGBM 的 MAE 仅为 8.23μg/m³，相对误差约为 11.3%；在中等浓度区间（100-160μg/m³），预测难度增加，LightGBM 的 MAE 上升至 14.77μg/m³，误差率约为 11.5%；在高浓度区间（>160μg/m³，即可能超标的情况），预测最为困难，LightGBM 的 MAE 达到 19.82μg/m³，相对误差约为 10.8%。值得注意的是，采用分层建模策略的 XGBoost 在高浓度区间表现突出，MAE 为 18.35μg/m³，优于其他模型，这证明了针对不同浓度区间的专门优化能有效提升预测能力。

从时效性角度分析，系统评估了不同预测时长的模型表现。在 1 天预测窗口内，LightGBM 模型表现最佳，MAE 为 12.85μg/m³；当预测延长至 3 天时，所有模型精度均有所下降，LightGBM 的 MAE 增至 17.23μg/m³；延长至 7 天时，预测误差进一步增大，LightGBM 的 MAE 达到 22.57μg/m³。这一结果与 O3 形成的光化学机制相符，由于 O3 生成强烈依赖于难以长期准确预报的气象条件，长期 O3 预测面临本质挑战。但即使在 7 天预测中，模型仍能较好地捕捉总体趋势，R² 保持在 0.62 左右，为中长期空气质量规划提供了有价值的参考。

图 4 不同模型 O3 预测效果对比图

季节性能比较显示，模型在不同季节的 O3 预测能力存在明显差异。在 O3 高发季节（5-9 月），由于数据丰富且模式相对稳定，模型表现较好，LightGBM 的 MAE 为 15.28μg/m³；在过渡季节（4 月和 10 月），O3 生成条件变化快，预测难度增加，MAE 上升至 17.46μg/m³；在低 O3 季节（11-3 月），虽然浓度水平低，但相对误差反而较高，MAE 为 10.53μg/m³，相对误差达 18.7%。分析表明，这种季节性差异主要源于光化学反应条件的季节变化以及不同季节排放模式的差异。针对这一特点，系统实现的季节性参数调整策略取得了良好效果，专门针对高臭氧季节优化的模型 MAE 降低至 13.75μg/m³，比通用模型提升了 10.0%。

O3 超标预测能力是评估模型实用价值的核心指标。系统评估了各模型对 O3 超标事件（MDA8>160μg/m³）的预警能力。结果显示，LightGBM 模型表现最佳，F1 分数达 0.74，超标捕获率(Recall)为 0.72，精确率(Precision)为 0.76。XGBoost 模型的分层策略在超标预测方面表现突出，F1 分数达 0.76，虽然整体 MAE 略高于 LightGBM。GRU 和时空融合模型在超标预测上表现相当，F1 分数分别为 0.67 和 0.69。进一步分析发现，对于超标程度较高的事件（MDA8>200μg/m³），所有模型的捕获率均有提升，LightGBM 的捕获率高达 0.85，这表明模型对严重超标事件的识别能力更强。

图 5 O3 超标事件预测评估分析

模型解释性分析揭示了影响 O3 预测的关键因素。通过 SHAP(SHapley Additive exPlanations)方法分析 LightGBM 模型，系统识别了影响 O3 预测的主要特征：日最高温度、太阳辐射强度和前 1-3 天 O3 浓度是影响最大的三个因素，三者共同解释了约 42%的预测变异；NOx 浓度特别是 NO2 浓度对 O3 预测也具有重要影响，但这种影响呈现复杂的非线性模式，低 NO2 和高 NO2 条件下 O3 表现出不同的响应模式；大气稳定度相关特征（如风速、混合层高度）显著影响 O3 的积累和扩散，共同解释了约 15%的预测变异。这些发现与 O3 的光化学形成机制高度一致，证实了模型成功捕捉了 O3 形成的关键影响因素。

误差来源分析帮助理解模型预测误差的主要原因。通过残差分析发现，误差主要来源于三个方面：一是气象预报误差传递，尤其是辐射强度和云量预报的不准确性显著影响 O3 预测，气象预报误差约贡献了总误差的 35%；二是前体物数据的限制，由于缺乏高时间分辨率的 VOCs 监测数据，模型难以精确捕捉前体物变化引起的 O3 波动，这一因素约贡献了误差的 28%；三是边界条件变化，特别是区域传输条件快速变化时，预测准确性下降，约贡献了误差的 20%。未来改进空间主要在于：增强气象要素预测准确性、融合更多 VOCs 观测或模拟数据、以及强化区域传输模式识别能力。

实用性评估证明，系统开发的 O3 预测模型具有显著的应用价值。在预警能力方面，系统可提前 24-48 小时预测可能的 O3 超标，为管控措施提供充足准备时间；在预测精度方面，模型预测的空气质量级别准确率达 85.3%，完全满足预警需求；在易用性方面，系统提供了可视化的预测结果和不确定性估计，便于决策者理解和应用。实际应用评估显示，预测系统的部署使得 O3 超标预警准确率提升了约 20.5%，为针对性减排和健康防护措施提供了科学依据。

针对实际应用需求，系统还开发了 O3 超标概率预测功能。通过量化预测结果的不确定性，系统不仅提供点预测值，还输出超标概率分布，如"明日 O3 超标概率为 75%"。这种概率预测方法通过集成多模型结果并考虑历史误差分布，有效量化了预测的不确定性，为风险评估和决策提供了更全面的信息支持。评估显示，概率预测的可靠性指数(reliability index)达到 0.82，校准性良好，预测的高概率事件(>80%)实际发生率为 86.5%，低概率事件(<20%)实际发生率为 17.3%。

总体而言，系统在 O3 预测任务上取得了满意的性能，特别是在 O3 超标预警方面展现出了实用价值。未来工作将重点优化光化学过程的模拟能力，增强对前体物-O3 关系的表达，并探索融合化学传输模型和数据驱动模型的混合方法，进一步提升预测的物理基础和准确性。
