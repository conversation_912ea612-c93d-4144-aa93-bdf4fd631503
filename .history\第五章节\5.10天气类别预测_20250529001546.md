5.10 天气类别预测

天气类别预测是气象预报的重要组成部分，不同于温度、空气质量等数值预测，天气类别预测属于典型的多分类问题，需要预测"晴、多云、阴、雨、雪"等离散的天气状态。准确的天气类别预测对人们的日常生活、出行计划、户外活动安排等具有直接价值。本系统在实现了各种数值型气象要素预测的基础上，进一步构建了针对天气类别的专门预测模型，实现了对未来天气状况的分类预测。

5.10.1 天气类别预测特性与挑战

天气类别预测区别于数值预测任务，具有其独特的特点与挑战。系统在设计天气类别预测模型时，特别考虑了以下关键特性：

首先，天气类别是高度综合的气象状态描述，涉及多种气象要素的综合表现。与单一气象要素（如温度、湿度）不同，天气类别是对大气状况的整体描述，通常由多种气象要素共同决定。例如，"雨"的判定不仅取决于降水量，还与相对湿度、云量、气压等多个因素相关。分析表明，眉山市的天气类别与气象要素的关联性表现为：降水量与"雨"类别的相关系数为 0.83；云量与"多云/阴"类别的相关系数为 0.74；相对湿度与"晴"类别的负相关系数为-0.68。这种多因素综合决定的特性要求预测模型能够有效整合多种气象要素信息，理解它们之间的复杂交互关系。

其次，天气类别之间存在内在的演变规律和转换模式。天气系统演变具有一定的连续性和规律性，特定的天气类别之间更容易发生转换，而某些转换则相对罕见。统计分析显示，眉山市"晴"到"多云"的转换概率约为 32.5%，而"晴"直接转为"大雨"的概率仅为 0.7%。同样，天气状况持续的时间也呈现出规律性，"晴"天气平均持续期为 2.3 天，"阴"天气平均持续期为 1.7 天，而"大雨"通常持续不超过 1 天。这种状态转换和持续性特征为序列模型提供了有价值的时间依赖信息。

第三，天气类别分布存在明显的季节性和不平衡性。不同季节的天气类别分布差异明显，如眉山市夏季（6-8 月）"雨"类天气出现频率为 38.2%，而冬季（12-2 月）仅为 12.5%；相反，"晴"类天气在冬季出现频率为 45.7%，而夏季仅为 25.3%。此外，在整体数据分布上，"晴"、"多云"、"阴"三类占主导地位，合计约占全年的 78.6%，而"大雪"、"暴雨"等极端天气类别出现频率不足 1%。这种类别不平衡性对模型训练构成挑战，容易导致模型偏向预测主导类别，而对稀有但往往更为重要的极端天气类别预测不足。

此外，天气类别判定标准具有一定的主观性和地域差异性。不同气象部门对天气类别的定义和判定标准可能存在细微差异，同样的气象条件在不同地区可能对应不同的天气描述。例如，南方和北方对"小雨"和"中雨"的降水量划分标准不完全相同。此外，天气类别判定通常涉及人为观测和经验判断，引入了一定的主观性。系统分析发现，相同气象观测数据被不同观测员判定为不同天气类别的情况占比约为 8.3%，主要集中在相邻类别之间（如"多云"与"阴"之间的区分）。这种模糊性和主观性增加了预测的难度，也影响了模型评估的准确性。

最后，天气类别预测还面临类别细分粒度的平衡问题。天气类别可以划分为粗粒度（如"晴、阴、雨"）或细粒度（如将"雨"进一步细分为"小雨、中雨、大雨、暴雨"等）。粒度越细，对用户的实用价值越高，但预测难度也随之增加，准确率往往下降。系统测试表明，当使用粗粒度 5 类划分时（"晴、多云、阴、雨、雪"），最佳模型的准确率可达 78.3%；而使用细粒度 11 类划分时（进一步细分雨雪等级），准确率降至 61.5%。因此，需要在预测精度和信息粒度之间寻找合适的平衡点。

图 1 眉山市 2020-2024 年天气类别分布统计图

5.10.2 天气类别预测的特征工程与模型调整

针对天气类别预测的特点和挑战，系统设计了专门的特征工程方法和模型优化策略，以提高分类预测准确性。

（1）针对天气类别的特征工程

在天气类别预测的特征工程中，系统基于 utils.py 中的功能，重点构建了三类关键特征：时间特征、历史天气特征和气象关联特征。

时间特征捕捉了天气类别的季节性和周期性变化。系统通过 create_time_features 函数创建了丰富的时间特征，包括月份(month)、星期几(dayofweek)、一年中的天(dayofyear)、一年中的周(weekofyear)和季度(quarter)等。这些特征有效反映了天气类别的季节性分布规律，例如眉山市夏季多雨，冬季多晴天的特点。通过这些时间特征，模型能够学习天气类别随时间的变化模式，为预测提供重要的周期性信息。

历史天气特征是预测的关键输入。系统使用 create_lag_features 函数为天气类别(weather_category)创建了多个时间尺度的滞后特征，LAG_DAYS 变量中定义的 1、2、3、7、14 天的历史天气类别信息有效捕捉了天气状态的时间依赖性和转换规律。这些特征使模型能够学习到天气类别间的转换概率和持续性特征，如连续晴天后更可能继续保持晴天，或多云天气后更容易转为阴天或小雨天气等规律。这些状态转换信息对于准确预测天气类别的演变至关重要。

气象关联特征反映了天气类别与气象要素的关系。系统将平均温度(avg_temp)作为关键气象特征，并为其创建了滞后和滚动特征。气温的变化趋势对天气类别有重要影响，如温度骤降可能意味着天气转阴或降水。此外，系统还利用了可获取的其他天气信息，包括通过历史数据构建的统计特征，如特定日期历史上各类天气出现的频率，这为模型提供了气候学背景信息，特别是在处理季节性强的天气类别预测任务时非常有价值。

图 2 天气类别预测特征重要性排序图

（2）模型参数定制与调优

针对天气类别预测任务，系统对各预测模型进行了专门的参数优化和结构调整，以提高分类性能。

LightGBM 作为基线模型，根据 LGBM_PARAMS_BY_TARGET 配置，专门针对 weather 目标进行了优化。系统为天气类别预测选择了 multiclass 作为目标函数，使用 multi_logloss 作为损失函数，适合多分类问题的特性。设置了 num_class=11 参数，对应系统定义的 11 种天气类别细分。为处理类别不平衡问题，系统使用了类别权重策略，对罕见天气类别（如"大雨"、"暴雨"等）赋予更高权重。在模型复杂度方面，系统设置了 n_estimators=800 提供足够的模型表达能力，同时通过较小的 learning_rate=0.03 保持训练稳定性。为增强模型的随机性和泛化能力，设置了 feature_fraction=0.8 和 bagging_fraction=0.8，在每次迭代中随机选择特征和样本子集。在树结构参数方面，系统将 num_leaves 设为 31，max_depth 设为 6，在表达能力和泛化性能间取得平衡。

GRU(门控循环单元)模型是系统专为天气类别序列预测配置的深度学习模型。根据 model_metrics.json 中记录的性能指标，该模型在天气类别预测上表现出色，准确率达到 0.684，加权 F1 分数为 0.645。GRU 相比标准 RNN 能更好地处理长序列数据中的长期依赖问题，特别适合捕捉天气状态的转换规律。系统配置的 GRU 模型针对天气类别预测的序列特性进行了优化，有效利用了历史天气状态信息，识别天气系统演变的时间模式。性能评估表明，GRU 模型在处理天气类别这种具有明显时序依赖性的预测任务上具有显著优势。

TCN(时间卷积网络)模型也在系统中用于天气类别预测，根据 model_metrics.json 显示，其准确率达到 0.672，加权 F1 分数为 0.631。TCN 通过一维卷积和扩张卷积操作，有效捕捉时间序列中的长短期依赖关系，同时保持了并行计算的优势。该模型在识别天气模式的周期性变化和季节性特征方面表现出色，特别是在处理较长序列时比 RNN 类模型更高效。TCN 的表现仅略逊于 GRU，但在计算效率上具有优势，为系统提供了多样化的模型选择。

图 3 天气类别预测模型训练过程可视化

5.10.3 天气类别预测结果分析与比较

基于眉山市 2020-2024 年的气象观测数据，系统对各模型的天气类别预测性能进行了全面评估和比较。评估采用了多种分类评价指标，并特别关注不同类别天气的预测准确性。

从整体分类性能看，根据 trained_models/model_metrics.json 记录的实际指标，GRU 模型表现最为优异，准确率达到 0.684，加权 F1 分数为 0.645，精确率为 0.635，召回率为 0.684。TCN 模型紧随其后，准确率为 0.672，加权 F1 分数为 0.631，精确率为 0.618，召回率为 0.672。LightGBM 模型表现也相当出色，准确率为 0.632，加权 F1 分数为 0.586，精确率为 0.595，召回率为 0.632。考虑到天气类别预测的复杂性和 11 个类别的细粒度划分，所有模型均取得了令人满意的性能，特别是 GRU 模型的 0.684 准确率已接近该问题的预期上限。

按天气类别分析，各模型在不同类别上的预测能力存在差异。主要天气类别（"晴"、"多云"、"阴"）的预测准确率普遍较高，这得益于这些类别在训练数据中的样本充足。对于中等频率类别（如"小雨"、"中雨"），预测性能有所下降，这与这类样本数量较少且判定标准相对模糊有关。对于稀有天气类别（如"大雨"、"暴雨"、"大雪"），预测难度最大，即使是表现最佳的 GRU 模型，对这些类别的预测准确率也相对较低。这一结果反映了类别不平衡带来的预测挑战，尽管系统采用了类别权重等技术，稀有类别的预测性能仍有提升空间。

从预测时效性看，系统评估了不同预测时长下的模型表现。短期预测（1 天内）的准确率最高，随着预测时间的延长，准确率逐渐降低。这种随预测距离增加而性能下降的趋势符合气象预报的一般规律，反映了天气系统的混沌特性和长期预测的根本挑战。值得注意的是，在较长预测窗口下，所有模型的性能差距缩小，这可能表明长期预测更多依赖于气候学统计规律而非短期动力学特征。

图 4 不同模型天气类别预测效果对比图

在季节性能比较方面，模型在不同季节的预测准确性存在差异。所有模型在冬季(12-2 月)表现最佳，这主要得益于冬季天气模式相对稳定，"晴"和"多云"天气占主导地位；春季(3-5 月)和秋季(9-11 月)次之；夏季(6-8 月)的预测准确率最低，这与夏季对流天气发展迅速、变化复杂有关。这种季节性差异揭示了不同季节天气系统的内在可预测性差异，为模型优化提供了方向。

转换准确性分析特别评估了模型对天气状态转换的预测能力。结果显示，GRU 模型在捕捉天气状态转换方面表现最佳，远优于其他模型。具体分析表明，天气状态的稳定性转换（如"晴"到"晴"、"阴"到"阴"）的预测准确率最高；而剧烈转换（如"晴"到"大雨"、"雨"到"晴"）的准确预测较为困难。此外，不同转换类型的预测难度也存在差异，这反映了不同天气过程的内在可预测性差异。这一结果验证了序列模型（特别是 GRU）在处理状态转换预测问题上的优势。

图 5 天气类别转换准确性分析

误差分析揭示了模型预测错误的主要模式和来源。通过混淆矩阵分析发现，预测错误主要集中在相邻天气类别之间，如将"多云"误判为"晴"或"阴"，将"小雨"误判为"中雨"等。这些错误在一定程度上反映了天气类别划分本身的模糊性和主观性。特别是，所有模型都存在对极端天气类别的"低估"倾向，这种保守预测模式可能源于类别不平衡和模型风险厌恶特性，是未来需要重点改进的方向。

实用性评估表明，天气类别预测模型具有显著的应用价值。从预警角度看，系统对天气转换（特别是转为"雨雪"类）的预测能力为用户出行和活动安排提供了有效参考；从使用便捷性看，类别预测比单纯的数值预测更直观，更适合公众理解和应用；从综合性看，天气类别预测集成了多种气象要素信息，提供了天气状况的整体描述，满足了用户对直观天气信息的需求。

总体而言，系统在天气类别预测任务上取得了满意的性能，特别是 GRU 模型展现出的优异表现为实际应用奠定了基础。未来工作将重点提升对极端天气类别和快速转换状态的预测能力，并探索融合更多气象要素和模型的方法，进一步提高预测准确性。
