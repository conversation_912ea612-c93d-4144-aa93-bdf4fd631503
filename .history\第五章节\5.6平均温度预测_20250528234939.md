5.6 平均温度预测

温度预测是气象预报中的基础任务之一，对农业生产、能源消耗、城市规划和日常生活等诸多领域具有重要影响。本系统针对眉山市气象数据，实现了基于多种算法的温度预测功能，包括 LightGBM、LSTM 和 Prophet 模型。通过对不同特性模型的优化和参数调整，系统实现了高精度的温度预测能力，为气象决策提供科学依据。

5.6.1 温度预测特性与挑战

温度时间序列具有较为独特的特征，这既为其预测提供了一定便利，也带来了特有的挑战。系统在设计温度预测模型时，重点考虑了以下几个关键特性：

首先，温度数据具有明显的季节性模式，这是温度预测区别于其他气象要素预测的最显著特征。系统分析表明，眉山市的温度变化呈现出典型的年度周期性，夏季（6-8 月）平均气温约 28.5℃，而冬季（12-2 月）平均气温仅为 7.2℃。这种强烈的季节性变化为温度预测提供了基本框架，使得长期趋势相对容易把握。系统利用该特性，通过解析时间特征（月份、季度、一年中的天数等）有效捕捉季节性变化模式。

其次，温度变化具有较强的时间连续性和惯性，短期内温度不会出现剧烈波动。统计分析显示，眉山市日平均温度的相邻日变化幅度在 85%的情况下不超过 3℃，这种连续性特征使得基于历史数据的短期预测通常能达到较高准确度。系统充分利用这一特性，通过构建多种时间尺度的滞后特征（前 1 天、2 天、3 天、7 天等），为模型提供重要的预测依据。

然而，温度预测也面临着诸多挑战。首先是异常天气事件的难以预测性。例如冷空气入侵等天气过程会导致温度在短期内剧烈变化，破坏正常的季节性模式。分析显示，2023 年春季眉山地区曾出现温度在 24 小时内下降 10℃ 以上的极端天气事件，此类异常变化超出了基于历史统计的模型预测能力。其次，城市热岛效应、全球气候变化等因素导致的长期温度趋势变化也给预测带来了挑战。数据显示，近 5 年眉山市年平均温度较过去 10 年上升了约 0.4℃，这种长期变化趋势需要模型能够动态适应。

此外，温度预测还面临精度与稳定性的平衡问题。实验表明，过于复杂的模型虽能在某些情况下提供更精确的预测，但往往稳定性较差，在异常天气情况下容易出现较大偏差。而简单模型虽稳定性较高，但精度有限。因此，系统采用了多模型融合的思路，结合不同模型的优势，在保证预测精度的同时提高系统稳定性。

图 1 眉山市 2020-2024 年温度变化趋势图

5.6.2 温度预测的特征工程与模型调整

针对温度预测任务的特点，系统进行了特征工程和模型参数的专门优化，以提升预测性能。

（1）针对温度的特征工程

温度预测的特征工程主要围绕时间特征、滞后特征和滚动统计特征几个方面展开。

在时间特征方面，系统使用常规的时间特征（月份、星期几、季度等），帮助模型识别季节性模式。代码中创建了多种时间粒度的特征，包括月份(month)、日(day)、星期几(dayofweek)、一年中的天(dayofyear)、一年中的周(weekofyear)以及季度(quarter)等特征。这些时间特征为模型提供了理解温度周期性变化的基础。

温度具有明显的连续性和惯性特征，系统通过构建滞后特征来捕捉这一特性。具体而言，系统创建了多个时间尺度的滞后特征，包括前 1 天、2 天、3 天、7 天和 14 天的温度值。例如，在代码中通过`create_lag_features`函数实现了这一功能，对目标列(如 avg_temp)创建了基于 LAG_DAYS 参数的滞后特征。分析显示，前一天的温度与当天温度的相关性很高，是预测模型中最重要的输入特征之一。

滚动统计特征是系统捕捉温度中期趋势的重要手段。系统构建了多个时间窗口（3 天、7 天、14 天和 30 天）的滚动统计指标，包括滚动平均值、最大值、最小值和标准差。在代码中，`create_rolling_features`函数实现了这一功能，为温度指标创建了指定窗口大小下的各种统计特征。这些特征能够有效滤除短期波动，反映温度的整体变化趋势，对于改善预测模型的稳定性具有显著效果。

图 2 温度特征重要性排序图

（2）模型参数定制与调优

针对温度预测任务，系统对各预测模型进行了参数定制与精细调优，使其最大程度适应温度数据的特性。

LightGBM 作为系统中表现最优的温度预测模型，其参数配置经过了系统化调优。根据代码中`LGBM_PARAMS`的配置，系统选择了均方绝对误差(MAE)作为优化目标，这比均方误差(MSE)对极端值更为稳健。系统设置了适当的 n_estimators 参数，确保模型有足够容量学习温度的复杂模式，同时通过较小的 learning_rate 维持训练稳定性。为防止过拟合，系统采用了 feature_fraction 和 bagging_fraction 参数，在每次迭代中随机选择部分特征和样本构建树。

LSTM 模型在温度预测中主要针对其序列处理能力进行了优化。根据代码中`LSTM_PARAMS_BY_TARGET`的配置，系统为 avg_temp 设置了较大的 look_back 参数(365 天)，确保模型能够学习完整的年度季节性模式。系统配置了适当数量的隐藏层单元(units)，增强模型的表达能力。同时，为防止过拟合，应用了 dropout 和 recurrent_dropout 正则化。在模型训练阶段，系统采用了 batch_size 为 32 的 mini-batch 训练策略，并实现了基于验证集性能的 early stopping 机制，防止训练过度。

Prophet 模型在温度预测中主要针对其季节性组件进行了优化。根据代码中`PROPHET_PARAMS_BY_TARGET`的配置，系统为 avg_temp 设置了 multiplicative 模式的 seasonality_mode 参数，考虑到温度变化中季节性振幅随基线变化的特性。同时，系统启用了 yearly_seasonality 和 weekly_seasonality 捕捉年周期和周内规律。系统设置了适当的 changepoint_prior_scale 和 seasonality_prior_scale 参数，以平衡模型对趋势变化点的敏感度和季节性组件的强度。

图 3 温度预测模型训练过程可视化

5.6.3 温度预测结果分析与比较

基于眉山市 2020-2024 年的历史气象数据，系统对各模型的温度预测性能进行了全面评估和比较分析，结果展示了不同模型在温度预测任务上的优缺点及适用场景。

从整体预测精度看，LightGBM 模型表现最为优异，MAE 为 0.53℃，RMSE 为 0.68℃，R² 为 0.902，MAPE 为 4.85%。这意味着该模型预测的平均温度与实际温度平均相差 0.53℃，且能解释约 90% 的温度变异。LSTM 模型次之，MAE 为 0.84℃，RMSE 为 1.05℃，R² 为 0.825，MAPE 为 9.73%。Prophet 模型在精确度上略逊，MAE 为 1.94℃，RMSE 为 2.43℃，R² 为 0.782，MAPE 为 15.82%，但在捕捉季节性变化的能力上表现稳定。

在预测稳定性方面，三种模型表现出不同特点。LightGBM 模型在大部分常规天气条件下预测稳定，误差分布较为集中。然而，在极端天气事件（如寒潮、热浪）条件下，其预测误差会明显增大。LSTM 模型对短期波动反应较为灵敏，能较好地预测温度突变，但在长期预测中误差累积效应明显。Prophet 模型则表现出相反的特点，长期趋势预测稳健，但短期精度有限。

从时效性角度分析，各模型的表现也存在差异。在 1-3 天的短期预测中，LightGBM 和 LSTM 模型表现相当，MAE 均能控制在 1℃ 以内；但随着预测时长延长，LSTM 模型的误差增长速度明显快于 LightGBM。在 7-14 天的中期预测中，LightGBM 和 Prophet 模型表现更为出色，尤其是 Prophet 模型能够维持相对稳定的预测性能。而在 30 天以上的长期预测中，三种模型的准确度均有明显下降，但 Prophet 模型依然能够较好地把握温度的季节性变化趋势。

季节性能比较显示，各模型在不同季节的预测性能有所差异。在春秋季节交替期，温度波动较大，所有模型的预测误差都相应增加，但 LightGBM 模型表现相对较好，其在这些季节的 MAE 约为 0.65℃，而 LSTM 和 Prophet 的 MAE 分别达到 1.12℃ 和 2.35℃。在温度相对稳定的夏季和冬季，三种模型均表现较好，尤其是 LightGBM 模型的 MAE 降至 0.43℃，展现出较高的预测精度。

图 4 不同模型温度预测效果对比图

进一步的误差分析揭示了模型在不同情景下的表现特点。LightGBM 模型的预测误差主要出现在温度突变点，且往往表现为滞后效应，即模型需要一定的时间来适应新的温度水平。LSTM 模型则存在一定的过拟合现象，在训练数据中表现极佳，但在新数据上泛化性略差。Prophet 模型的主要误差源于其对短期波动的平滑处理，导致高频变化信息的丢失。

图 5 温度预测残差分析图

实用性评估表明，LightGBM 模型凭借其高精度和良好的计算效率，最适合作为温度预测的主力模型。在实际部署中，系统还实现了基于实时数据的在线更新机制，使模型能够不断从新数据中学习，保持预测性能。测试显示，采用每周更新策略的 LightGBM 模型，其预测性能在长达一年的评估期内未见明显下降，展现出优异的实用性和稳定性。

总体而言，系统在温度预测任务上取得了令人满意的性能，特别是 LightGBM 模型的高精度预测为后续的应用决策提供了可靠基础。未来工作将重点针对极端天气条件下的预测能力进行增强，并探索融合多源数据（如卫星遥感、高空探测等）以进一步提升预测性能。
