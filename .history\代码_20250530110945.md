# 代码清单（基于程序真实情况）

## 代码 5-1: 温度数据预处理核心代码

```python
def preprocess_temperature_data(df):
    """对温度数据进行预处理，包括缺失值处理和标准化"""
    # 处理缺失值
    df['avg_temp'] = df['avg_temp'].interpolate(method='linear')
    df['avg_temp'] = df['avg_temp'].fillna(method='ffill').fillna(method='bfill')

    # 归一化温度数据
    scaler = MinMaxScaler(feature_range=(0, 1))
    df['avg_temp_scaled'] = scaler.fit_transform(df[['avg_temp']])

    return df, scaler
```

## 代码 5-2: 温度预测特征工程实现

```python
def create_time_features(df):
    """创建时间相关特征"""
    df['year'] = df['date'].dt.year
    df['month'] = df['date'].dt.month
    df['day'] = df['date'].dt.day
    df['dayofweek'] = df['date'].dt.dayofweek
    df['dayofyear'] = df['date'].dt.dayofyear
    df['quarter'] = df['date'].dt.quarter
    df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)

    # 季节性编码
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    df['day_sin'] = np.sin(2 * np.pi * df['day'] / 31)
    df['day_cos'] = np.cos(2 * np.pi * df['day'] / 31)

    return df

def create_lag_features(df, columns, lag_days):
    """创建滞后特征"""
    for col in columns:
        if col in df.columns:
            for lag in lag_days:
                df[f'{col}_lag_{lag}'] = df[col].shift(lag)
    return df

def create_rolling_features(df, columns, windows):
    """创建滚动统计特征"""
    for col in columns:
        if col in df.columns:
            for window in windows:
                df[f'{col}_rolling_mean_{window}'] = df[col].rolling(window=window).mean()
                df[f'{col}_rolling_std_{window}'] = df[col].rolling(window=window).std()
    return df
```

## 代码 5-3: LightGBM 模型参数配置

```python
# 针对不同目标变量的LightGBM回归参数
LGBM_PARAMS_BY_TARGET = {
    "avg_temp": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1200,
        "learning_rate": 0.02,
        "feature_fraction": 0.85,
        "bagging_fraction": 0.85,
        "bagging_freq": 7,
        "lambda_l1": 0.05,
        "lambda_l2": 0.05,
        "num_leaves": 40,
        "max_depth": 8,
        "verbose": -1,
        "n_jobs": -1,
        "seed": 42,
        "boosting_type": "gbdt",
    }
}

# 训练LightGBM模型
model_lgbm = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['avg_temp'])
model_lgbm.fit(
    X_train, y_train,
    eval_set=[(X_test, y_test)],
    eval_metric='mae',
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
)
```

## 8. 数据序列化处理（整合.md）

```python
def create_dataset_lstm_gru(data, look_back=30):
    """创建LSTM/GRU所需的序列数据"""
    X, y = [], []
    for i in range(len(data) - look_back):
        X.append(data[i:(i + look_back), 0])
        y.append(data[i + look_back, 0])
    return np.array(X), np.array(y)
```

## 9. LSTM 模型架构设计（整合.md）

```python
# 构建LSTM模型
model = Sequential()
model.add(LSTM(64, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])))
model.add(Dropout(0.2))
model.add(LSTM(32, return_sequences=False))
model.add(Dropout(0.2))
model.add(Dense(1))

# 编译模型
model.compile(optimizer='adam', loss='mean_squared_error')
```

## 10. Prophet 模型数据准备（整合.md）

```python
# 准备Prophet所需格式的数据
prophet_df = df[['date', 'avg_temp']].rename(columns={'date': 'ds', 'avg_temp': 'y'})

# 划分训练集和测试集
train_size = int(len(prophet_df) * 0.8)
train_df = prophet_df[:train_size]
test_df = prophet_df[train_size:]
```

## 11. AQI 预测特征构建（整合.md）

```python
def create_aqi_features(df):
    """创建AQI预测的特征集"""
    # 污染物相关特征
    pollutants = ['pm25', 'pm10', 'so2', 'no2', 'co', 'o3']

    # 为每种污染物创建滞后特征
    for pollutant in pollutants:
        if pollutant in df.columns:
            for lag in [1, 2, 3, 7]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

    # 添加污染物间的交互特征
    if 'pm25' in df.columns and 'pm10' in df.columns:
        df['pm_ratio'] = df['pm25'] / df['pm10'].replace(0, 0.001)

    # 添加气象影响特征
    weather_features = ['avg_temp', 'humidity', 'wind_speed']
    for feature in weather_features:
        if feature in df.columns:
            for lag in [1, 3]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

    return df
```

## 12. AQI 预测 LightGBM 参数配置（整合.md）

```python
# AQI预测的LightGBM参数配置
aqi_params = {
    'objective': 'regression',
    'metric': 'mae',
    'n_estimators': 120,
    'learning_rate': 0.04,
    'num_leaves': 35,
    'max_depth': 8,
    'feature_fraction': 0.75,
    'bagging_fraction': 0.75,
    'bagging_freq': 5,
    'min_data_in_leaf': 15,
    'verbose': -1
}

# 配置到参数字典中
LGBM_PARAMS_BY_TARGET = {
    'aqi_index': aqi_params,
    # 其他目标的参数...
}

# 训练模型
model = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['aqi_index'])
model.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='mae',
    early_stopping_rounds=50,
    verbose=False
)
```

## 13. LSTM 多变量序列数据准备（整合.md）

```python
def create_multivariate_lstm_data(df, target, feature_cols, sequence_length=14):
    """创建多变量LSTM输入序列"""
    X, y = [], []

    # 提取目标列和特征列
    data = df[feature_cols + [target]].values

    # 创建序列样本
    for i in range(len(data) - sequence_length):
        X.append(data[i:i+sequence_length, :-1])  # 所有特征
        y.append(data[i+sequence_length, -1])    # 目标值

    return np.array(X), np.array(y)
```

## 14. AQI 预测 LSTM 模型结构（整合.md）

```python
# 构建多变量LSTM模型
model = Sequential()
model.add(LSTM(80, return_sequences=True,
              input_shape=(X_train.shape[1], X_train.shape[2]),
              activation='relu'))
model.add(Dropout(0.3))
model.add(LSTM(40, activation='relu'))
model.add(Dropout(0.2))
model.add(Dense(20, activation='relu'))
model.add(Dense(1))

# 编译模型
model.compile(optimizer='adam', loss='mse', metrics=['mae'])
```

## 15. AQI 预测 Prophet 模型配置（整合.md）

```python
# AQI预测的Prophet参数配置
aqi_prophet_params = {
    'yearly_seasonality': True,        # 年度季节性
    'weekly_seasonality': True,        # 周季节性
    'daily_seasonality': False,        # 不使用日季节性
    'seasonality_mode': 'multiplicative',  # 乘法季节性模式
    'changepoint_prior_scale': 0.05,    # 变化点敏感度
    'seasonality_prior_scale': 10.0,    # 季节性强度
    'holidays_prior_scale': 10.0,       # 节假日影响强度
    'interval_width': 0.95              # 95%预测区间
}

# 配置到参数字典中
PROPHET_PARAMS_BY_TARGET = {
    'aqi_index': aqi_prophet_params,
    # 其他目标的参数...
}

# 创建Prophet模型
model = Prophet(**PROPHET_PARAMS_BY_TARGET['aqi_index'])

# 添加国家法定假日(可选)
from prophet.holidays import get_holiday_names, add_country_holidays
model.add_country_holidays(country_name='China')

# 训练模型
model.fit(train_prophet_df)
```

## 16. PM2.5 预测特征构建（整合.md）

```python
def create_pm25_features(df):
    """创建PM2.5预测的特征集"""
    # 气象相关特征
    weather_features = ['avg_temp', 'humidity', 'pressure', 'wind_speed', 'wind_direction']

    # 为气象特征创建滞后和滚动特征
    for feature in weather_features:
        if feature in df.columns:
            # 滞后特征
            for lag in [1, 2, 3, 7]:
                df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)

            # 滚动统计特征
            for window in [3, 7, 14]:
                df[f'{feature}_rolling_mean_{window}'] = df[feature].rolling(window=window).mean()
                if feature != 'wind_direction':  # 对非方向性特征计算统计量
                    df[f'{feature}_rolling_std_{window}'] = df[feature].rolling(window=window).std()

    # 污染物相互影响特征
    other_pollutants = ['pm10', 'so2', 'no2', 'co', 'o3']
    for pollutant in other_pollutants:
        if pollutant in df.columns:
            # 滞后特征
            for lag in [1, 2, 3]:
                df[f'{pollutant}_lag_{lag}'] = df[pollutant].shift(lag)

            # 与PM2.5的比值特征
            if 'pm25' in df.columns:
                df[f'pm25_{pollutant}_ratio'] = df['pm25'] / df[pollutant].replace(0, 0.001)

    # 添加时间特征
    if 'date' in df.columns:
        # 季节相关编码，使用三角函数将循环特性编码
        df['month_sin'] = np.sin(2 * np.pi * df['date'].dt.month / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['date'].dt.month / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['date'].dt.day / 31)
        df['day_cos'] = np.cos(2 * np.pi * df['date'].dt.day / 31)

    return df
```

## 17. PM2.5 预测 LightGBM 参数配置（整合.md）

```python
# PM2.5预测的LightGBM参数
pm25_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'n_estimators': 150,
    'learning_rate': 0.03,
    'num_leaves': 40,
    'max_depth': 9,
    'feature_fraction': 0.7,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 20,
    'reg_alpha': 0.1,
    'reg_lambda': 0.3,
    'verbose': -1
}

# 配置到参数字典中
LGBM_PARAMS_BY_TARGET.update({'pm25': pm25_params})

# 训练模型
model = lgb.LGBMRegressor(**LGBM_PARAMS_BY_TARGET['pm25'])
model.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='rmse',
    early_stopping_rounds=50,
    verbose=False
)
```

## 18. PM2.5 预测 LSTM 数据预处理（整合.md）

```python
def prepare_pm25_lstm_data(df, sequence_length=14):
    """PM2.5预测的LSTM数据准备"""
    # 选择关键特征
    selected_features = [
        'pm25', 'pm10', 'humidity', 'avg_temp', 'pressure',
        'wind_speed', 'month_sin', 'month_cos'
    ]

    # 确保所有特征存在
    available_features = [f for f in selected_features if f in df.columns]

    # 提取数据
    data = df[available_features].values

    # 归一化数据
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(data)

    # 创建序列数据
    X, y = [], []
    for i in range(len(scaled_data) - sequence_length):
        # 提取特征序列
        features_sequence = scaled_data[i:i+sequence_length, :]
        # 目标值是序列后的PM2.5值(假设PM2.5在第一列)
        target = scaled_data[i+sequence_length, 0]
        X.append(features_sequence)
        y.append(target)

    # 转换为NumPy数组
    X = np.array(X)
    y = np.array(y)

    return X, y, scaler
```

## 19. PM2.5 预测 LSTM 网络结构（整合.md）

```python
from tensorflow.keras.layers import Bidirectional, Attention, Dense, Dropout
from tensorflow.keras.layers import LSTM, BatchNormalization
from tensorflow.keras.models import Sequential

# 构建PM2.5预测的LSTM模型
def build_pm25_lstm_model(input_shape):
    model = Sequential()

    # 第一层双向LSTM
    model.add(Bidirectional(
        LSTM(64, return_sequences=True, activation='relu'),
        input_shape=(input_shape[0], input_shape[1])
    ))
    model.add(BatchNormalization())
    model.add(Dropout(0.3))

    # 第二层LSTM
    model.add(LSTM(32, return_sequences=False, activation='relu'))
    model.add(BatchNormalization())
    model.add(Dropout(0.2))

    # 全连接层
    model.add(Dense(16, activation='relu'))
    model.add(Dense(1))

    # 编译模型
    model.compile(
        optimizer='adam',
        loss='mean_squared_error',
        metrics=['mae']
    )

    return model
```

## 20. TCN 天气分类模型（整合.md）

```python
import tensorflow as tf
from tensorflow.keras.layers import Conv1D, LayerNormalization, Activation, Add, Input
from tensorflow.keras.models import Model

def residual_block(x, dilation_rate, nb_filters, kernel_size, padding, dropout_rate=0.1):
    """TCN残差块实现"""
    # 跳跃连接
    prev_x = x

    # 第一层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 第二层膨胀卷积
    x = Conv1D(filters=nb_filters,
              kernel_size=kernel_size,
              dilation_rate=dilation_rate,
              padding=padding)(x)
    x = LayerNormalization()(x)
    x = Activation('relu')(x)
    x = Dropout(dropout_rate)(x)

    # 如果输入和输出维度不匹配，进行投影
    if prev_x.shape[-1] != x.shape[-1]:
        prev_x = Conv1D(nb_filters, 1, padding='same')(prev_x)

    # 添加残差连接
    res = Add()([prev_x, x])

    return res

def build_weather_category_tcn_model(input_shape, num_classes):
    """构建天气类别预测的TCN模型"""
    input_layer = Input(shape=input_shape)

    # TCN块，膨胀率逐层增加
    x = input_layer
    nb_filters = 64
    kernel_size = 3

    for dilation_rate in [1, 2, 4, 8]:
        x = residual_block(x, dilation_rate, nb_filters, kernel_size, 'causal')

    # 全局池化后接全连接层
    x = GlobalAveragePooling1D()(x)
    x = Dense(128, activation='relu')(x)
    x = Dropout(0.2)(x)
    x = Dense(64, activation='relu')(x)

    # 输出层
    output_layer = Dense(num_classes, activation='softmax')(x)

    model = Model(inputs=input_layer, outputs=output_layer)

    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )

    return model
```
