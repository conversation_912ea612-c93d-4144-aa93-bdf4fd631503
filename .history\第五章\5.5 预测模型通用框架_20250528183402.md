5.5 预测模型通用框架

本系统实现了多种气象和空气质量预测模型，包括平均温度预测、AQI 指数预测、PM2.5 浓度预测、O3 浓度预测以及天气类别预测。这些不同预测目标虽然具体实现各有特点，但在数据处理流程、模型构建方法和评估标准方面共享一套通用框架，有效提高了系统的一致性和可维护性。本节概述这一通用框架，为后续各预测模型的实现提供基础。

5.5.1 数据预处理与特征工程

系统从 data.db 数据库加载经过清洗的气象和空气质量数据，对所有预测模型应用统一的数据预处理流程。首先处理缺失值，对于数值型变量如温度、AQI 指数等，使用中位数填充；对于分类变量如空气质量等级，则采用前后值填充或最频繁值填充。对异常值，系统使用统计方法识别并处理，确保数据质量。

特征工程是提升预测性能的关键环节。系统通过 utils.py 中实现的三个核心特征创建函数构建丰富的特征集：时间特征提取函数 create_time_features 从日期字段中提取月份、日期、星期几、季度等时间维度信息，有效捕捉季节性和周期性模式；滞后特征函数 create_lag_features 创建目标变量的历史值特征，如前 1 天、3 天、7 天的数值，引入时序依赖关系；滚动特征函数 create_rolling_features 计算过去 3 天、7 天、14 天、30 天的移动平均值、标准差、最大值和最小值，提供历史趋势信息。

【图 5.5-1：特征工程流程示意图】
此图展示了系统特征工程的完整流程，包括三个主要环节：时间特征提取、滞后特征创建和滚动特征生成。图中通过流程图形式展示了原始数据经过特征提取后的转换过程，左侧为输入的原始时间序列数据，中间为特征转换处理过程，右侧为生成的多维特征矩阵。图中还直观展示了系统如何从日期中提取年、月、日、星期等时间维度，以及如何基于历史数据创建滞后和滚动统计特征。

针对不同预测目标，系统自动选择相关性最强的特征子集。例如，对温度预测主要利用历史温度数据及时间特征；对空气质量指标则额外考虑气象条件特征，如温度与 PM2.5 之间的关联。特征选择过程既提高了模型效率，又降低了过拟合风险。

数据划分采用时间序列交叉验证方法，按照时间顺序将数据集分为训练集(80%)和测试集(20%)，避免未来数据泄露。对于深度学习模型如 LSTM、GRU 和 TCN，系统还创建特定的序列数据格式，通过指定的回看窗口(lookback)构建输入序列，为这些模型提供时序连贯的训练数据。

5.5.2 多模型架构设计

系统根据预测任务特性和数据特点，为每种预测目标实现了多种类型的模型。主要包括五类：基于梯度提升的 LightGBM 模型、基于深度学习的 LSTM 模型、基于门控循环单元的 GRU 模型、基于时间卷积网络的 TCN 模型和基于分解的 Prophet 模型。这种多模型架构既能满足不同场景需求，又可通过比较和融合提高预测准确性。

LightGBM 模型是系统的核心，适用于所有预测目标。对于数值预测任务(平均温度、AQI 指数等)，配置为回归模型；对于天气类别预测，则配置为多分类模型。LightGBM 参数针对不同目标进行了专门优化，如温度预测使用较大的树深度(8)和叶节点数(40)，而 PM2.5 预测则使用较小的树深度(6)和叶节点数(31)，反映了不同数据特性的需求。所有模型都采用早停策略避免过拟合，并通过交叉验证确保模型稳定性。

LSTM 模型专门用于捕捉时间序列的长期依赖关系，对具有明显时序特性的目标如温度和空气质量指标特别有效。系统实现的 LSTM 模型针对不同目标使用不同的网络配置：温度预测使用 128 个网络单元和较长的回看窗口(365 天)以捕捉年度季节性特征；AQI 指数和 PM2.5 预测分别使用 96 个和 64 个网络单元，回看窗口为 180 天；O3 预测使用 96 个网络单元和 365 天回看窗口，以捕捉臭氧的强季节性特征。所有 LSTM 模型均采用 dropout 技术(比率为 0.2-0.3)减少过拟合，并使用早停机制优化训练过程。

GRU(门控循环单元)模型是系统中专门用于天气类别预测的深度学习模型，其设计简化了 LSTM 的结构，但保留了建模长期依赖关系的能力。系统实现的 GRU 网络包含单层 GRU(128 个单元)和 Softmax 分类层，能够处理多类别天气预测问题。与 LSTM 相比，GRU 参数更少，训练速度更快，特别适合样本量有限的天气类别预测任务。系统采用 180 天的回看窗口，使模型能够捕捉天气状况的季节性变化模式。

TCN(时间卷积网络)模型是系统中最新引入的深度学习架构，专为序列建模设计，通过膨胀卷积(Dilated Convolution)技术有效扩大感受野，能够同时捕捉短期和长期依赖关系。系统实现的 TCN 模型位于 dl_model_fix.py 文件中，通过 create_tcn_model 函数实现，包含多个残差块，每个残差块中包含两层一维卷积层、ReLU 激活函数和 SpatialDropout1D 层。系统中的 TCN 模型使用[1, 2, 4, 8, 16]的膨胀率序列扩大感受野，主要用于天气类别预测，其并行计算特性使训练速度优于 LSTM 和 GRU。

Prophet 模型是 Facebook 开发的专用于时间序列预测的工具，特别擅长处理具有明显季节性和趋势性的数据。系统利用 Prophet 内置的季节性分解功能，自动识别数据中的年度、周度和日度模式，适用于温度等强季节性指标的预测。

【图 5.5-2：不同预测模型适用场景对比图】
此图采用矩阵形式展示了五种模型(LightGBM、LSTM、GRU、TCN、Prophet)在不同预测目标和数据特性下的适用性。横轴表示不同预测目标(温度、AQI、PM2.5、O3、天气类别)，纵轴表示不同模型特性(计算效率、长期依赖建模能力、季节性捕捉能力、特征利用能力、样本需求量)。图中使用热力图颜色深浅表示适用程度，并清晰展示了各模型的最佳应用场景，如 LightGBM 适合特征丰富的结构化预测任务，LSTM 和 GRU 适合捕捉长期依赖关系，TCN 适合并行处理高效率任务，Prophet 适合强季节性数据。

每种预测目标的模型选择遵循模型特性与数据特征的匹配原则。系统会同时训练多个模型，保存各自的性能指标，既方便用户比较不同模型的预测效果，也为后续的模型融合奠定基础。

5.5.3 训练流程与参数优化

模型训练由 train_models.py 脚本集中管理，执行包括数据准备、特征工程、模型训练和评估的完整流程。针对不同预测目标，系统维护专门的参数配置，确保每个模型都能发挥最佳性能。

对于 LightGBM 模型，系统在 LGBM_PARAMS_BY_TARGET 字典中为每个预测目标定义了专门的参数集。这些参数经过多轮实验优化，包括学习率(0.02-0.03)、特征抽样比例(0.7-0.85)、正则化参数等。具体配置上，对于平均温度预测，learning_rate 设为 0.02，feature_fraction 设为 0.85，bagging_fraction 设为 0.85，正则化参数 lambda_l1 和 lambda_l2 均为 0.05，num_leaves 设为 40，max_depth 设为 8；对于 AQI 预测，learning_rate 设为 0.03，feature_fraction 设为 0.8，bagging_fraction 设为 0.8，lambda_l1 和 lambda_l2 均为 0.1，num_leaves 设为 35，max_depth 设为 7。训练过程中使用 early_stopping 回调机制，当验证集性能连续 50 轮不再提升时自动停止训练，避免过拟合并节省计算资源。

LSTM 模型的训练参数同样针对不同目标进行了优化，存储在 LSTM_PARAMS_BY_TARGET 字典中。关键参数包括：温度预测使用 128 个网络单元、0.2 的 dropout 比例和 365 天的回看窗口；AQI 指数预测使用 96 个网络单元、0.25 的 dropout 比例和 180 天的回看窗口；PM2.5 预测使用 64 个网络单元、0.3 的 dropout 比例和 180 天的回看窗口；O3 预测使用 96 个网络单元、0.25 的 dropout 比例和 365 天的回看窗口以捕捉其强季节性；天气类别预测使用 128 个网络单元、0.3 的 dropout 比例和 180 天的回看窗口。LSTM 模型使用的早停策略 patience 参数也根据目标不同而变化，温度和 O3 预测为 15，其他目标为 12。训练采用 Adam 优化器和均方误差损失函数(回归任务)或稀疏分类交叉熵损失函数(分类任务)。

GRU 和 TCN 模型使用的早停策略均采用 patience=12 的设置，在验证集损失或准确率连续 12 个周期没有改善时停止训练。TCN 模型的关键参数包括 64 个滤波器、kernel_size 为 3、膨胀率序列[1, 2, 4, 8, 16]和 dropout 比例为 0.2。这些参数组合经过实验验证，能够在模型复杂度和预测精度之间取得良好平衡。

Prophet 模型训练相对简单，主要配置包括季节性组件(yearly_seasonality、weekly_seasonality)和季节性模式(seasonality_mode)。系统根据数据特性自动确定合适的配置，例如温度预测使用 multiplicative 的 seasonality_mode，而 AQI 指数、PM2.5 和 O3 预测使用 additive 模式，反映了不同指标的季节性特征差异。

【图 5.5-3：模型训练流程与参数优化过程图】
此图展示了系统的模型训练完整流程，包含数据准备、模型训练和参数优化三个主要阶段。图表采用流程图形式，展示了从原始数据到最终模型的完整路径。左侧显示输入数据的预处理和特征工程步骤，中间部分展示不同类型模型的训练过程和特定参数配置，右侧展示模型评估和选择机制。图中还包含了各模型的关键参数设置和优化策略，如 LightGBM 的树深度和叶节点优化、LSTM/GRU 的网络结构调整、TCN 的膨胀率配置等，并展示了早停策略和交叉验证的应用。

训练完成后，系统将模型文件、参数配置和性能指标保存到 trained_models 目录，便于后续调用和比较。LightGBM 和 Scaler 对象通过 joblib 序列化保存，深度学习模型则保存为 .h5 或 .keras 格式，确保模型的可复用性。

5.5.4 模型评估与可视化

系统采用多种评估指标全面评估模型性能。对于回归任务(温度、AQI 等)，主要使用平均绝对误差(MAE)、均方根误差(RMSE)、决定系数(R²)和平均绝对百分比误差(MAPE)；对于分类任务(天气类别)，则使用准确率(Accuracy)、精确率(Precision)、召回率(Recall)和 F1 分数。

根据 model_metrics.json 记录的评估结果，各模型在不同预测任务上表现各异。在温度预测方面，LightGBM 模型表现最佳，MAE 为 0.53℃，RMSE 为 0.68℃，R² 为 0.902，MAPE 为 4.85%；而 LSTM 模型次之，MAE 为 0.84℃，RMSE 为 1.05℃，R² 为 0.825，MAPE 为 9.73%；Prophet 模型表现相对较弱，MAE 为 1.94℃，RMSE 为 2.43℃，R² 为 0.782，MAPE 为 15.82%。

在 AQI 预测方面，LightGBM 依然表现最佳，MAE 为 6.75，RMSE 为 10.23，R² 为 0.874，MAPE 为 12.43%；LSTM 模型次之，MAE 为 13.85，RMSE 为 17.32，R² 为 0.753，MAPE 为 19.84%；Prophet 模型表现最弱，MAE 为 17.85，RMSE 为 22.54，R² 为 0.584，MAPE 为 31.26%。

在 PM2.5 预测任务上，LightGBM 模型 MAE 为 5.24μg/m³，RMSE 为 7.86μg/m³，R² 为 0.883，MAPE 为 14.82%；LSTM 模型 MAE 为 12.34μg/m³，RMSE 为 15.67μg/m³，R² 为 0.785，MAPE 为 23.45%；Prophet 模型 MAE 为 15.43μg/m³，RMSE 为 19.85μg/m³，R² 为 0.625，MAPE 为 41.37%。

在 O3 预测任务中，LightGBM 模型 MAE 为 8.75μg/m³，RMSE 为 11.84μg/m³，R² 为 0.836，MAPE 为 17.92%；LSTM 模型 MAE 为 10.52μg/m³，RMSE 为 13.25μg/m³，R² 为 0.806，MAPE 为 21.75%；Prophet 模型 MAE 为 16.75μg/m³，RMSE 为 21.43μg/m³，R² 为 0.653，MAPE 为 32.84%。

在天气类别预测中，GRU 模型表现最佳，准确率为 68.4%，加权 F1 分数为 0.645；TCN 模型次之，准确率为 67.2%，F1 分数为 0.631；LightGBM 模型表现相对较弱，准确率为 63.2%，F1 分数为 0.586。

评估过程中，回归模型的预测结果会从归一化空间转换回原始尺度，确保评估指标的可解释性。系统将评估结果保存为 JSON 格式，同时记录在日志文件中，便于跟踪模型性能变化。

【图 5.5-4：模型评估可视化结果示例图】
此图展示了系统生成的典型模型评估可视化结果，对应 static/analysis_results/眉山/models/avg_temp/lgbm 目录下的评估图表。它包含多个子图：预测值与实际值对比散点图(actual_vs_predicted.png)，显示了模型预测的准确性；预测误差分布直方图(residual_distribution.png)，展示了误差的分布特性；残差与预测值关系散点图(residual_vs_predicted.png)，用于检测系统性预测偏差；模型评估指标条形图(model_evaluation_metrics.png)，包括 MAE、RMSE、R² 等关键指标；以及时序预测图(time_series_prediction.png)，展示了模型在时间序列上的预测表现。

结果可视化是理解模型性能的重要手段。系统通过 generate_model_evaluation.py 脚本自动生成各种评估图表，包括预测值与实际值对比图、残差分布图、残差 vs 预测散点图和评估指标条形图等。这些图表直观展示了模型的预测能力和误差特征，保存在 evaluation_plots 目录以及各模型的专用目录下供用户查看。

系统还支持模型比较功能，将同一预测目标的不同模型结果在一张图表中对比展示，帮助用户直观理解各模型的优劣。对于时间序列预测，系统生成时序预测线图，展示模型对未来趋势的把握能力。

5.5.5 模型部署与预测应用

经过训练和评估的模型通过标准化的接口集成到系统中，为 Web 应用提供预测服务。系统设计了统一的预测接口，能够根据用户需求灵活调用不同模型，实现对气象和空气质量指标的预测分析。

预测流程与训练流程保持一致，首先对输入数据应用相同的预处理和特征工程步骤，然后加载保存的模型和缩放器进行预测，最后将预测结果转换回原始尺度并返回给用户。对于缺失的特征值，系统使用训练阶段保存的特征统计量进行填充，确保预测过程的稳定性。

【图 5.5-5：模型部署与预测应用流程图】
此图展示了训练完成的模型如何部署到生产环境并应用于实时预测的完整流程。图表采用流程图形式，左侧展示模型存储和管理机制，中间部分展示预测接口的实现和数据处理流程，右侧展示预测结果的输出和应用。图中详细说明了模型加载、特征处理、预测执行和结果转换的各个步骤，并展示了系统如何处理实时输入数据和返回预测结果。

系统通过创建训练模型的备份目录（如 trained_models_backup_20250512_022054）来保存模型历史版本，这种方式便于在需要时恢复到之前的模型状态。模型训练完成后保存在 trained_models 目录中，可供预测应用调用。

通过上述通用框架，系统实现了对多种气象和空气质量指标的高效、准确预测。这一框架不仅确保了各预测模型的一致性和可维护性，也为系统的扩展和优化提供了坚实基础。后续章节将详细介绍各具体预测模型在此框架基础上的特定实现和应用。
