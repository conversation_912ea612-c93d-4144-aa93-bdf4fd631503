本 科 生 毕 业 设 计 说 明 书

基于机器学习的气象分析与预测系统的设计

钟宇骏

学 院： 新工科产业学院  
专 业： 数据科学与大数据技术
班 级： 数据 214  
学 号： 201912904449  
指导教师： 何素贞  
职称（或学位）： 副教授

2025 年 4 月
原创性声明

本人郑重声明：所呈交的毕业设计，是本人在导师的指导下，独立进行研究工作所取得的成果。除文中已经注明引用的内容外，本设计不含任何其他个人或集体已经发表或撰写过的作品成果。对本设计的研究做出重要贡献的个人和集体，均已在文中以明确方式标明。本人完全意识到本声明的法律结果由本人承担。
学生签名： 2025 年 4 月 10 日

指导声明

本人指导的 钟宇骏 同学的毕业设计题目大小、难度适当，且符合该同学所学专业的培养目标的要求。本人在指导过程中，通过网上文献搜索及文献比对等方式，对其毕业设计内容进行了检查，未发现抄袭现象，特此声明。
指导教师签名： 2025 年 4 月 10 日

基于机器学习的气象分析与预测系统的设计
钟宇骏
（新工科产业学院 指导教师：何素贞）

摘要：为解决传统气象预测存在的精度欠佳和时效性不足问题，开发了基于机器学习的气象数据分析与预测系统。系统有三个核心模块：一是数据采集模块，通过网络爬虫从公开网站采集温度、PM2.5 等气象与空气质量数据；二是多算法预测模块，整合了 LightGBM、LSTM、GRU、TCN 和 Prophet 等五种模型和它们的融合算法，针对不同预测目标选择最优算法进行预测，测试显示最优秀的模型 LightGBM 在眉山市数据集上温度预测 MAE 为 0.13℃，R² 为 0.999，PM2.5 预测 MAE 为 4.1μg/m³，AQI 预测 MAE 为 4.207，GRU 在天气分类任务上准确率达 64.3%，实验表明，通过加权融合策略可提高系统整体预测精度，融合模型的 R² 达 0.8879，较最佳单模型提升约 11%。；三是可视化分析模块，基于 Flask 框架开发 Web 应用，实现历史数据查询和多模型预测结果的图表展示。系统经测试满足设计基本需求，能安全、稳定和可靠地运行。
关键词：气象预测；空气质量预测；机器学习

Design of Meteorological Data Analysis and Prediction System Based on Machine Learning
Yujun Zhong
(New Engineering Industry College, Advisor: He Suizhen)

Abstract: To address the issues of suboptimal accuracy and insufficient timeliness in traditional meteorological forecasting, a machine learning-based system for meteorological data analysis and prediction was developed. The system comprises three core modules: First, a data collection module that gathers meteorological and air quality data, such as temperature and PM2.5, from public websites using web crawlers. Second, a multi-algorithm prediction module that integrates five models—LightGBM, LSTM, GRU, TCN, and Prophet—along with their fusion algorithms, selecting the optimal algorithm for prediction based on different forecasting targets. Tests showed that the best-performing model, LightGBM, achieved a Mean Absolute Error (MAE) of 0.13°C and an R² of 0.999 for temperature prediction on the Meishan City dataset, an MAE of 4.1 μg/m³ for PM2.5 prediction, and an MAE of 4.207 for AQI prediction. GRU achieved an accuracy of 64.3% in weather classification tasks. Experiments demonstrated that a weighted fusion strategy can enhance the overall prediction accuracy of the system, with the fused model achieving an R² of 0.8879, an improvement of approximately 11% compared to the best single model. Third, a visualization analysis module, developed as a web application using the Flask framework, enables historical data querying and the graphical display of multi-model prediction results. The system, after testing, meets the fundamental design requirements and operates securely, stably, and reliably.
Keywords: Meteorological Prediction; Air Quality Forecasting; Machine Learning

目 录

1 绪论 1
1.1 选题背景及意义 1
1.2 国内外研究现状 1
1.3 课题研究目标与内容 2
2 相关技术介绍 2
2.1 数据挖掘和智能分析流程 2
2.2 相关机器学习理论介绍 3
2.3 Flask 框架 9
3 系统需求分析 10
3.1 可行性分析 10
3.2 功能需求分析 10
4 系统功能设计 10
4.1 数据爬虫与处理功能设计 11
4.2 多算法预测模型功能设计 12
4.3 可视化模块功能设计 13
4.4 数据库设计 13
5 系统功能实现 16
5.1 数据爬虫实现 16
5.2 爬虫数据的清洗 16
5.3 用户登录界面展示 16
5.4 查询功能 17
5.5 预测模型 20
5.6 深度分析模块实现 35
6 系统测试 44
6.1 软件测试意义 44
6.2 测试项目 44
7 总结与展望 45
7.1 总结 45
7.2 展望 47
致谢 47
参考文献 48

1 绪论
1.1 选题背景及意义
1.1.1 选题背景
预测气象要素和空气质量是关系到国计民生的重要课题。传统气象预测主要依赖于数值天气预报(NWP)模式[1]，利用数值运算求解大气运动方程组来描述未来大气运动状态，局部不能精准预报，而统计预测方法虽然在短期天气预报中取得了一定的效果，但由于基于线性假设条件，无法有效描述非平稳、非线性的气象时间序列，因此预报精度也存在局限性。
机器学习为解决气象预测问题提供了新的思路。基于梯度提升树的 LightGBM 模型可以更加快速高效地处理多种不同特征的类别和复杂模式；基于深度学习的方法，如由记忆单元构成的深度学习模型(如 LSTM、GRU 等)可以有效地解决循环神经网络(RNN)造成的梯度消失问题，能较好地实现时序预测；Prophet 算法在季节和节假日效应建模方面有较好的性能，能有效地处理时间序列分解预测问题。每种方法都有自己的优势和不足，通常都需要将不同模型融合才能在气象预测中取得更好的效果。 LightGBM 算法擅长处理结构化特征和短期模式，而 LSTM 网络则适用于分析和理解长期时间依赖性，Prophet 算法对于趋势和周期分解预测具有更好的鲁棒性。因此，在实际的气象预测中，综合考虑多种模型的对比和融合往往能够取得更加准确的预测效果。
1.1.2 选题意义
系统旨在研究基于机器学习的多模型预测方法。研究的意义主要体现在以下三个方面：
（1）理论方法创新：推动时序预测模型集成策略研究
传统的气象预测主要依赖于单一模型，无法同时兼顾不同的预测任务对精度的要求。系统基于 LightGBM、LSTM、GRU 和 Prophet 等算法构建模型库并创新性地提出对比选择最优策略，为非线性、非平稳的气象时间序列提供分析方法，对机器学习模型集成理论形成一定的参考价值。
（2）构建气象-空气质量联合预测框架
针对空气质量预测中气象和污染物的耦合特征，首次将多源数据纳入同一分析框架，建立基于气象的空气质量预测模型，该框架突破了传统统计模型无法有效进行多因素的耦合解析的不足，为复合型环境问题的智能化分析提供了技术范式，应用于智慧城市环境管理中。
（3）开发交互式气象服务决策支持系统
基于 Flask 框架的交互式 Web 系统，集成了多模型预测功能和可视化分析工具，基于“数据-模型-应用”链路，用算法优势转化为公共服务能力。
1.2 国内外研究现状
就传统预报方法而言，气象预报长期以来取决于运行良好的数值气象预报模式(NWP)。比较典型的模式，如欧洲中期综合预报中心(ECMWF)[2]。国家环境预报中心(NCEP)[3]研发的 IFS 和 GFS 模式等，都是常用的典型的模式。但由于模式计算成本的巨大、观测数据质量和空间覆盖有限，因此对特定地点的气象现象再现准确性成为制约模式性能的主要因素。
全球有多个使用最新技术的气象数据分析系统，其中，美国国家气象局(NWS)[4]拥有强大的观测网络和多种业务化运行的数值模式。中国自主研发的 GRAPES[5]在分辨率、资料同化技术、物理过程参数化等方面持续改进，预报能力稳步提升，CMA-TYM[6]则有针对台风路径和强度的专用预报模式，CMA-CUACE[7]是有空气质量模式，与气象模式耦合预测。
未来，国内外都将致力于发展更高精度、更长时效、更智能化、更具针对性的天气预测系统，以更好地服务于防灾减灾、经济社会发展和应对气候变化的需求。
1.3 课题研究目标与内容
1.3.1 课题研究目标
为了突破传统预测气象和空气质量分析方法的准确性和时效性，研究提出基于机器学习的集成多模型预测体系，针对单一模型无法有效建模复杂天气类型和空气质量的预测问题，考虑构建包含 LightGBM、LSTM、GRU 和 Prophet 在内的综合模型库；设计自适应更新权重策略使模型进行更新，兼顾短时预测效果和长时序列依赖，提高系统的健壮性；构建基于 Flask 框架的交互式 Web 应用程序。
1.3.2 课题研究内容
先构建多源数据驱动的基础层，用网络爬虫技术从天气后报网站(tianqihoubao.com)采集包含温度、风力风速、PM2.5 和 O3 等指标的多源时序数据集；而后采用线性插值、中位数填充等方法处理缺失值，通过归一化处理和特征工程优化数据质量，生成时序滞后特征以及气象-空气质量间的耦合特征。然后实现多算法预测模型集成创新，在温度预测、天气状况预测、污染物浓度预测等方面充分利用 LightGBM 的结构化数据处理能力，和 LSTM 和 GRU 网络其可记忆长期累积历史的特征，还有时间变化对于 Prophet 模型的影响；设计算法对比模块，通过各项评估指标获得最优算法模块。最后设计可视化预测系统，基于 Flask 框架设计前后端交互系统，完成数据查询，多模型预测，可视化对比系统，将预处理的数据信息储存在 SQLite 数据库中，调用服务器的 RESTful API，完成模型的调取及数据的发送，最终完成空气质量-气象联合预测的可视化展示的 web 系统。
2 相关技术介绍
2.1 数据挖掘和智能分析流程
如图 1 所示，系统通过多源数据采集模块采集到原始数据后，经由数据清洗引擎完成对其的异常值处理、缺失值填补等预处理操作。清洗后的标准化数据会被存入到 SQLite 数据库中，同时要进入到特征工程模块中进行高阶抽象特征的提取。这些结构化特征与前期加载的知识库中的知识进行融合，输入到自适应机器学习框架中完成模型训练。最终构建的多个模型可以较好地实现对天气状况预测、污染物浓度预测等服务，从而形成从数据采集到知识应用的全流程闭环[8]。

图 1 数据挖掘整体流程图
2.2 相关机器学习理论介绍
2.2.1 LightGBM 算法
LightGBM 的核心原理是以梯度提升决策树也就是 GBDT 为基础[9]，依靠迭代训练多棵决策树，每棵树去拟合前序模型的残差，最终依靠加权投票机制来构建强学习器。和传统 GBDT 实现相比，有效地解决了传统算法在处理气象要素高维特征时的计算瓶颈问题。
在算法公式方面，LightGBM 遵循 GBDT 的加法模型框架，假设训练数据集为公式 1。
（1）
其中为包含温度、湿度、风速等 m 维气象要素的特征向量，为预测目标。模型通过 K 轮迭代构建决策树序列，最终预测函数表示为如公式 2 所示：
（2）
其中为第 K 棵树的权重系数。每轮迭代通过最小化损失函数的负梯度方向确定最优分割点，为防止过拟合，算法引入 L1/L2 正则化项及最大深度限制，并通过 GOSS 机制对样本加权，其权重更新为公式 3：
（3）
其中为第 i 个样本的损失梯度绝对值，a 和 b 为预设采样比例参数。
2.2.2 LSTM 模型
长短时记忆网络是一种特殊的递归神经网络，专门用来处理传统 RNN 在处理长序列数据时记忆能力受限以及梯度消失的问题[10]。它的核心思路是依靠一组门控机制去控制信息的流动，让关键的信息可在长时间跨度内得以保留，而不关键的信息则被有选择性地遗忘，以此提升网络对长期依赖关系的建模能力，LSTM 架构图如图 2 所示。

图 2 LSTM 网络结构示意图
如图 2 所呈现的，LSTM 借助三个门控单元，也就是遗忘门、输入门和输出门，来对信息的存储、更新以及输出进行动态管理。
（1）遗忘门
遗忘门决定了细胞状态中哪些部分的信息需要保留，哪些需要丢弃。遗忘门公式如下 4 所示。
（4）
遗忘门控制细胞状态中哪些信息需要被遗忘。是激活函数 Sigmoid，输出在。其中是权重矩阵，形状为，其中是隐藏状态维度，是输入特征维度。是偏置向量，形状为。是遗忘门的输出，形状为，其中是批量大小。
（2）输入门
输入门公式如下 5 和 6 所示：
（5）
（6）
输入门控制当前时间步的输入中哪些信息需要写入记忆单元。候选记忆生成新的候选记忆，用于更新细胞状态。是权重矩阵，形状为。是候选记忆权重矩阵，形状为。和是偏置，形状分别为。
（3）细胞状态更新
细胞状态更新公式如下 7 所示：
（7）
更新记忆单元的状态，将遗忘门和输入门的结果结合起来。是上一时间步的细胞状态，形状为。是当前时间步的细胞状态，形状为。
（4）输出门
输出门控制细胞状态中哪些部分的信息需要输出到隐藏状态，并作为当前时间步的最终结果，具体如公式 8 和 9 所示：
（8）
（9）
输出门控制哪些细胞状态通过激活值输出。隐含状态作为当前时间步的最终输出，同时也会用于下一时间步。是权重矩阵，形状为。是偏置，形状为。
2.2.3 Prophet 模型
Prophet 模型专门针对具有强季节性和节假日效应的时间序列设计[11]。其数学表达式为：
（10）
其中：g(t)为趋势项，采用分段线性函数或逻辑增长曲线建模；s(t)为季节项，通过傅里叶级数拟合周期性模式；h(t)为节假日效应项，以指示函数刻画特殊事件影响；为误差项，服从正态分布。
Prophet 通过 L-BFGS 算法自动优化各分量参数，并支持人工指定变点以捕捉趋势突变。
2.2.4 GRU 模型
设输入序列为，其中为包含温度、湿度、风速等$n$维气象要素的特征向量，GRU 的隐藏状态更新规则如下：
（1）更新门计算：
（11）
其中为 Sigmoid 函数，为权重矩阵，$d$为隐藏层维度，为偏置项。
（2）重置门计算：
（12）
（3）候选隐藏层生成：
（13）
其中为逐元素乘法，。
（4）隐藏状态更新：
（14）
GRU 的优势在于：（1）信息融合：通过将与拼接，实现历史记忆与当前输入的特征融合；（2）门控机制：更新门的取值范围为(0,1)，控制新旧信息的融合比例；（3）梯度传播：隐藏状态的线性更新规则确保梯度可跨时刻传播，缓解 RNN 的梯度消失问题[12]。
2.2.5 TCN 模型
时间卷积网络(Temporal Convolutional Network，TCN)是专为序列建模设计的深度学习架构，相较于循环神经网络[13]，它通过一维卷积操作实现并行计算，同时借助膨胀卷积(Dilated Convolution)技术有效扩大感受野，适用于气象时序数据中复杂的长期依赖关系建模。TCN 网络的核心特征可归纳为三点：因果卷积、膨胀卷积和残差连接。
在数学表达上，给定输入序列 X = {x₁, x₂, ..., xₜ}，其中 xₜ 代表 t 时刻的特征向量（包含温度、湿度、风速等气象要素），TCN 的一维因果卷积操作可表示为：
（15）
其中 F(t)为输出特征值，X(t-d\*i)为历史输入数据，k 为卷积核大小，W(i)为卷积核在第 i 个位置的权重参数，d 为膨胀因子。
膨胀卷积使得第 l 层的感受野呈指数增长，理论感受野大小为：
（16）
其中 RF(l)表示第 l 层神经元的理论感受野大小，k 为每层卷积核的大小，l 为网络层数。
这使得 TCN 能够在参数量不显著增加的情况下，有效捕捉长距离时序依赖。
TCN 工作流程如图 3 所示：

图 3 TCN 工作流程
为缓解深层网络训练困难，TCN 采用残差连接机制，单个残差块结构如下：
（17）
其中 F(X)表示残差块中经过两层卷积、激活函数和 Dropout 处理后的特征映射，X 为残差块输入值，W1×1\*X 表示当输入和输出的通道数（维度）不同时，使用 1×1 卷积改变维度（∗ 表示卷积）。
TCN 在气象预测中的优势体现在三个方面：（1）并行：得益于卷积操作的并行计算速度，相比 LSTM 和 GRU 模型，相同数据规模下训练时间缩短约 40%；（2）多尺度：通过对膨胀率的控制，TCN 可同时捕捉短期（如一天之内的温度波动）和长期（如一年四季的波动）的气象模式；（3）梯度稳定：因为残差连接机制的存在，所以可以很好地避免深层网络中的梯度消失问题，来确保模型能够稳定学习长序列数据中的复杂特征。
在气象分类任务（如天气状况预测）中，TCN 结构可以通过全局平均池化和 Softmax 激活层转换为分类模型：
（18）
其中 W 表示分类层的权重矩阵，b 表示偏置项，Softmax 表示将线性输出转换为概率分布，p 表示最终预测的天气类别概率向量。
2.2.6 模型比较
研究采用多模型并行架构：
表 1 五个模型比较
比较维度 LightGBM LSTM Prophet GRU TCN
理论基础 集成学习的梯度提升决策树 循环神经网络的门控记忆变体 加性回归分解 深度神经网络非线性映射 时间卷积网络
参数调优 基于梯度的自适应调优，参数适中 需调整隐藏层、单元数和序列长度 自动优化趋势/季节分量，支持变点干预 需网格搜索隐藏层数、神经元数量等超参 需调整膨胀率、卷积核大小等超参
非线性处理 决策树自然捕捉特征非线性关系 多层次门控结构捕捉复杂时序模式 逻辑增长曲线拟合趋势突变 通过门控机制自动捕捉复杂模式 通过膨胀卷积层有效捕捉长期依赖关系
长序列依赖 通过滞后特征显式建模时序关联 细胞状态保持长期信息流动 年周期通过傅里叶项拟合 通过简化的门控机制实现记忆传递 通过残差连接机制稳定学习长序列数据中的复杂特征
多变量支持 原生支持多特征输入和交互 多变量输入序列自然处理 需手动构建协变量矩阵 自动融合多维度输入特征 通过膨胀卷积层有效捕捉多尺度气象模式
计算资源 低（秒级训练，无 GPU 需求） 高（需 GPU 加速训练） 中等（秒级，CPU 足够） 高（需 GPU 加速训练） 中等（秒级，CPU 足够）
气象适配 擅长多因素复杂关系建模 优势领域：带噪声时序数据 擅长多周期叠加要素 优势领域：非线性突变要素 优势领域：多尺度气象模式
过拟合风险 低（内置正则化和提前停止） 高（需 Dropout 和 L2 正则化） 低（需手动设置变点先验） 高（需 Dropout、L2 正则化） 低（需调整超参）
2.2.6 评估指标
（1）数据值预测指标（用于温度、AQI、PM2.5、臭氧等）
假设是真实值，是预测值，是平均值，n 是样本数量。
① 均方误差（Mean Squared Error, MAE）是回归任务中最常用的评估指标之一，其数学表达式为：

                             （19）

说明：计算每个样本的预测误差的绝对值，然后取平均。
② 均方根误差（Root Mean Squared Error, RMSE）计算方式为误差平方的平均值的平方根：
（20）
说明：计算每个样本的预测误差的平方，取平均后，再开平方根。由于平方的存在，它对较大的误差给予更高的权重。
③ 决定系数（R²）表示模型能解释的因变量变异比例：
（21）
说明：表示模型预测的方差在总方差中所占的比例。值越接近 1，模型解释能力越强。
④ 平均绝对百分比误差（Mean Absolute Percentage Error, MAPE）以百分比形式表示误差大小：
（22）
说明：  计算每个样本的预测误差相对于真实值的百分比的绝对值，然后取平均。
（2）分类型预测指标（用于天气状况分类）
TP (True Positives): 真实为正类，预测也为正类的样本数。
FP (False Positives): 真实为负类，但预测为正类的样本数。
FN (False Negatives): 真实为正类，但预测为负类的样本数。
TN (True Negatives): 真实为负类，预测也为负类的样本数。
① 准确率（Accuracy）是分类任务中最直观的评估指标，表示正确分类的样本比例：
（23）
说明：所有样本中被正确分类的比例。
② 加权 F1 分数（Weighted F1）是精确率和召回率的调和平均值：
（24）

                             （25）

说明：当处理多分类问题且类别不平衡时，Weighted F1 是一个常用的指标。它会计算每个类别的 F1 分数，然后根据每个类别中真实样本的数量（即"支持度" support）进行加权平均。
③ 精确率（Precision）衡量预测为某类别的样本中真正属于该类别的比例：
（26）
说明：在所有被预测为正类的样本中，有多少是真正的正类。
④ 召回率（Recall）衡量某类别样本中被正确识别的比例：
（27）
说明：在所有真正的正类样本中，有多少被模型成功预测出来了。
2.3 Flask 框架
Flask 框架是研究构建交互式 Web 应用时所采用的核心后端技术，于系统里充当着数据中枢以及业务逻辑处理器的双重角色，借助 RESTful API 服务达成了从前端交互至后端模型调用的全流程贯通。
其工作流程如图 3 所示，

图 3 Flask 工作流程示意图
在具体实现方面，Flask 承担着三大核心职能，如图 4 所示，

图 4 Flask 内部框架构成图
3 系统需求分析
3.1 可行性分析
3.1.1 技术可行性
在数据获取方面，系统采用 Python 网络爬虫技术，再结合 requests 请求库和 BeautifulSoup 解析库可以做到高效地从天气后报网站(tianqihoubao.com)上爬取历史气象与空气质量数据；在算法模型方面，LightGBM、LSTM、GRU、TCN 和 Prophet 等算法在时序预测领域都已有较多应用，可以通过 scikit-learn 和 TensorFlow 等开源框架来实现标准化部署；在系统架构方面，基于 Flask 的轻量级 Web 框架配合 ECharts 可视化库就可满足交互式数据展示的性能要求。从这三个层面来看，都有完善的文档支持和活跃社区，能够很好地确保系统开发的技术风险可控。
3.1.2 操作可行性
从操作层面考察，系统设计采用 B/S 架构模式，用户使用浏览器即可轻松访问所有的功能，无需再安装其他的软件。界面设计则是遵循了简洁直观的原则，通过城市选择下拉框、时间跨度选择器等交互组件简化用户操作流程。预测结果是直接以表格和图表形式来呈现，就比如历史数据与预测值对比、误差分析和多模型预测结果对比等，极大程度地降低了用户的门槛。而数据存储则是采用轻量级 SQLite 数据库，它便于移植和维护，可以很好地确保系统运行的稳定性，符合原型系统的需求。
3.1.3 社会可行性
系统开发具有显著的社会意义和发展空间。随着雾霾、沙尘暴等极端天气越来越频繁出现，准确的气象预测与空气质量分析对于农业生产、城市规划与环境治理来说都具有重要意义[14]。系统还可以为政府环保部门提供污染预警决策支持，为农业种植提供科学指导，为公众健康防护等提供参考依据[15]。系统符合国家生态文明建设的技术需求，完成了"数据-模型-应用"链路实现气象监测技术从实验室向应用场景的转化，较好地推动了气象服务向精细化、智能化、精准化方向的发展，具有很好的社会价值和潜在的经济利益等。
3.2 功能需求分析
根据对气象分析预测的应用需求，系统功能可以分为三大模块。数据爬虫模块需要用网络爬虫技术从天气后报网站采集多源异构的气象数据，系统使用了智能重试机制和多层次伪装策略，对温度等数值变量使用了线性插值与中位数填充等策略，对天气状况等分类变量则使用了前向填充和常见值补充等策略，以此来保证数据的完整性。多算法预测模块需要实现 LightGBM、LSTM、GRU、TCN 和 Prophet 五种算法和它们的融合算法，对于不同的演出目标，如温度、PM2.5、O3、AQI 等，通过 MAE 和准确率等评估指标来综合选择出最优算法，实现精度较高的预测。可视化分析模块需要使用 ECharts 框架来构建交互式数据视图，实现历史数据回溯、多模型预测结果对比与动态预测等功能，通过双轴组合图表和模型信息卡片展示预测结果，为用户提供直观的数据分析与决策支持工具。系统还需要实现用户认证、数据查询和信息管理等以 Web 应用形式提供基础功能的服务。
4 系统功能设计
该系统有三大模块功能，其一为数据采集与预处理体系构建，需开发网络爬虫以实现多源气象与空气质量数据的采集，运用插值法、标准化处理以及特征工程来优化数据质量，构建包含温度、PM2.5、O3 浓度等关键指标的预测数据集，其二是多算法预测模型集成创新，鉴于时间序列预测特性，分别构建 LightGBM、LSTM、GRU 及 Prophet 和 TCN 这五种预测模型，通过多项评估指标得出不同预测目标的最优模型。其三是可视化预测系统开发，基于 Flask 框架构建 Web 应用，集成数据查询、多模型预测结果对比以及可视化功能，为用户提供支持，使其可借助交互式界面进行数据分析、回溯与预测。因此系统整体功能设计如图 5 所示。
图 5 系统整体功能模块图
4.1 数据爬虫与处理功能设计
在数据采集所用的引擎方面，研究搭建了基于 Python requests 和 BeautifulSoup 库的网络爬虫系统，该系统整合了天气后报网站的数据，以此达成数据的采集。此系统支持 HTTP 协议传输，可兼容 CSV、JSON 等多种数据格式的解析，所采集的要素有温度、风速、PM2.5、O3 等。
系统设计了多层次伪装策略以及行为模拟技术，动态 User-Agent 轮换模块可以模拟 Chrome、Firefox 等主流浏览器指纹，再结合 IP 代理池管理，能有效降低被封禁的风险。
对于爬取数据下来存储时，会尝试多种编码形式，确保内容被正确解析。
为挖掘数据的深层价值，系统构建了包含时间特征、滞后特征和滚动特征的特征工程框架，有效提取数据中的时间模式和序列关联性。
存储优化方面，数据存储采用 SQLite 数据库，采用优化的表结构和索引设计，实现高效的数据查询和分析.
数据库存储结果如图 6、图 7 所示：
图 6 天气数据爬虫结果保存截图

图 7 天气质量爬虫结果保存截图
4.2 多算法预测模型功能设计
（1）差异化模型库构建
研究依据问题特性来构建多算法模型库，针对不同的预测任务对比选择最优算法。
（2）模型优化与预测
每种模型针对特定任务进行独立训练和优化，通过常规参数配置和性能评估来选出合适的模型。
（3）温度场预测
针对温度预测任务，模型经过严格的训练-测试集评估，以 MAE 等指标为评估标准来确保温度预测的准确性。
（4）污染物浓度预测
针对 PM2.5、O3 等污染物浓度预测，系统用的是 LightGBM、LSTM 和 Prophet 等算法。可以有效地对污染物浓度变化进行预测。
（5）气象-空气质量关联分析
气象数据与空气质量数据的关联整合是通过数据库连接建立的，包含了时间、天气条件、温度范围、风力信息和多种污染物指标（PM2.5、O3 等）等。
（6）预测结果可视化
将各模型预测结果以统一格式输出，便于在前端进行可视化展示和对比分析。
（7）预测结果可视化
设计了一套完整的可视化预测框架，将各个预测模型的输出转换为统一的标准格式，以支持不同预测目标的图形化界面展示。
4.3 可视化模块功能设计
（1）数据回溯
构建基于 ECharts 框架的时空数据可视化引擎，可支持气象要素以及空气质量参数的回溯分析。
（2）数据查询与筛选
系统开发了基于参数化 API 的数据检索功能，用户可通过前端界面来进行查询。
（3）多模型对比分析
构建起预测结果视图，在视图上对 LightGBM、LSTM、Prophet、TCN 和 GPU 的预测轨迹展开
（4）多维度交互控制
交互方式为简便的下拉菜单选择，以日期范围过滤为主，支持图表参数的动态调整切换。
（5）系统用户注册
允许新用户借助前端界面填写个人注册信息。用户信息会以结构化形式存入 SQLite 数据库，为后续的登录认证提供依据。
（6）系统用户修改
后端凭借 Python 和 Flask 逻辑处理，判断用户登录状态以及身份权限，保证信息修改操作的安全性和有效性。
（7）登录密码修改
出于信息安全考虑，系统支持用户密码的修改。新密码将采用加盐哈希算法进行加密存储，防止明文密码泄露。
4.4 数据库设计
数据库中的数据是以时间序列形式组织，支持按城市和时间范围检索。数据库索引主要建立在城市和日期字段上，优化查询效率。模型训练所需的特征工程（如时间特征提取、滞后特征、滚动统计特征）在运行时通过代码动态生成，而不是预先存储在数据库中。整体数据库设计遵循简单实用原则，符合项目作为原型系统的定位，同时为预测模型提供了必要的历史数据支持。
4.4.1 数据库概要设计
系统使用 SQLite 关系型数据库来构建预测系统的数据库，存储和管理了气象数据和空气质量数据。SQLite 作为轻量级数据库，它易于部署和维护，在中小型应用系统中得到广泛应用。根据实际需求，系统主要包含 weather_data 和 aqi_data 两张核心表，分别用于存储气象数据和空气质量数据。系统的 E-R 图如下图 8、图 9、图 10、图 11 所示。

图 8 用户信息实体表

图 9 天气质量信息实体表

图 10 天气数据实体表
4.4.2 数据库表设计
表 2 用户信息表
字段名 数据类型 约束条件 功能描述
MemberId INT PRIMARY KEY IDENTITY 用户唯一标识符，自增主键
UserName NVARCHAR(50) UNIQUE NOT NULL 登录用户名，需唯一
UserPwd NVARCHAR(50) NOT NULL 用户密码
表 3 天气质量表
字段名 数据类型 约束条件 功能描述
id INT PRIMARY KEY IDENTITY 气象数据唯一标识符
Time NVARCHAR(50) 时间
AQIValue FLOAT AQI 质量
AQILeval FLOAT AQI 指数
PM2.5 FLOAT PM2.5
PM10 FLOAT PM10
S02 FLOAT S02
N02 FLOAT N02
C0 FLOAT C0
O3 FLOAT O3
表 4 天气数据表
字段名 数据类型 约束条件 功能描述
id INT PRIMARY KEY IDENTITY 气象数据唯一标识符
续表 4 天气数据表
字段名 数据类型 约束条件 功能描述
city_name NVARCHAR(150) NOT NULL 城市名称
Time NVARCHAR(50) 时间
weather_condition FLOAT 天气状况
temperature_range FLOAT 气温
wind_info FLOAT 风力方向
5 系统功能实现
5.1 数据爬虫实现
对天气质量数据进行自动爬取和存储，采用模块化的设计，其技术实现可分成数据源适配、动态请数据、多维数据解析和稳定的数据存储四个环节。在数据源的解析环节，通过 BeautifulSoup 库解析 HTML 表格，通过简单的 HTML 元素定位，获得包括空气质量指数(AQI)、PM2.5、O3 等 13 类核心指标在内的数据行。
5.2 爬虫数据的清洗
爬虫数据清洗是针对数据实施多环节处理流程，比如缺失值填补、异常检测、标准化处理和特征构建等步骤。在缺失值处理方面，系统对温度等数值变量采用了线性插值、前向填充和中位数填补相结合的方法，对天气状况等分类变量则使用了前后填充和频率统计法替换缺失项。
通过这些清洗步骤，数据完整率可以达到 99.9%以上，可以作为下一步建模训练的数据来源。
5.3 用户登录界面展示
登录界面采用简洁的设计风格，输入框包含用户名和密码两个输入字段。提供了登录和注册两个操作。登录认证功能则是基于 Flask-Login 框架来实现的，后端通过 SQLite 数据库进行存储和验证用户凭据。密码验证采用单向验证，即使用 Werkzeug 安全模块的哈希函数对密码进行加密存储，确保用户的密码不会被泄露。登录后在界面顶部弹出用户名和操作下拉框进行退出操作；未登录弹出"登录/注册"。该界面设计既保证了系统安全性，又兼顾了用户使用的流畅性和便捷性。
登录界面如图 12、图 13 所示：

图 12 系统首页界面

图 13 用户登录实现界面
5.4 查询功能
5.4.1 历史天气查询
该功能采用三级联动查询界面设计，用户可按城市、年份、月份进行过滤，并查询历史天气数据。系统在前端绘制温度历史变化趋势图，以折线图形式展示选定时间段内的温度走势；同时采用散点图展示不同日期的天气状况分布情况。查询结果以表格形式展示详细信息，包括日期、天气状况、最高气温、最低气温、风力风向等数据。
历史天气查询界面如 14 图所示：

图 14 历史天气查询界面示意图
5.4.2 天气年度变化分析
主要用于对长时间跨度的气象数据进行横向对比分析。页面包含三个主要图表：温度年度变化趋势图、天气状况分布饼图和风力风向统计条形图，分别从不同角度展示气象要素的长期变化规律。
天气年度变化分析界面如图 15 所示：
图 15 天气年度变化分析界面示意图
5.4.3 月度气温对比
专注于分析不同年份同一月份的气温变化情况。设计了简洁的城市和月份选择界面，用户可通过下拉框和查询按钮快速定位目标数据。系统支持三种不同的图表展示方式：柱状图、折线图和横向条形图，用户可根据分析需求灵活切换。图表中同时展示不同年份同月的平均最高温度和平均最低温度，直观呈现温差变化。
月度气温对比界面如图 16 所示：

图 16 月度气温对比界面示意图
5.4.4 AQI 年度变化分析功能
该功能对城市空气质量指数及各污染物的年度变化情况进行分析。页面提供了多项污染物指标的选择，用户可明确选择 AQI、PM2.5、PM10、SO2、NO2、CO、O3 等指标来进行分析。而且系统还支持柱状图和折线图这两种展示方式，以满足不同的数据分析需求。页面包含了两个主要图表，其中单指标趋势图展示单一污染物的年内变化，而污染物组成堆叠图则是用来展示各类污染物的组成比例及其随时间的变化。
AQI 年度变化分析界面如图 17 所示：

图 17 AQI 年度变化分析界面示意图
5.4.5 污染物占比分析
该功能对城市空气中各污染物的占比进行分析，并对占比变化趋势进行分析。页面设计了饼图来展示各污染物在总污染中的占比情况，还设计了折线图来展示选定年份内各污染物浓度的变化趋势。系统还提供了一个表格，有每日的空气质量数据，包括 AQI、PM2.5、PM10、SO2、NO2、CO、O3 等指标的具体数值。
污染物占比分析界面如图 18 所示：

图 18 污染物占比分析界面示意图
5.4.6 气温预测功能
该功能提供年、月、日三级联动选择器，用户可以对特定日期进行预测分析。预测模块集成了统计模型和机器学习模型，能够生成对气象要素的预测结果，并将其与实际观测值进行对比。页面中的组合图同时展示了预测温度、实际温度和预测误差，反映了预测精度；详细的对比表格则展示了天气状况、最高气温、最低气温、风力风向、AQI 指数等多项要素的预测值与实际值、和它们之间的差值。
气温预测分析界面如图 19 所示：

图 20 气温预测分析界面示意图
5.5 温度预测模块实现
本系统实现了完整的温度预测功能，通过多种算法模型的应用与对比，找出最适合气象温度预测的算法组合。以下详细介绍各模型在温度预测方面的实现与效果。

5.5.1 LGBM 模型预测
LightGBM 作为系统的基准模型，在温度预测任务上表现出优异的性能。该模型基于梯度提升决策树原理，针对气象数据特点进行了优化配置。模型使用历史温度数据、时间特征及气象关联因素作为输入，通过滞后特征和移动平均等特征工程手段增强预测能力。

通过实验优化后，LGBM 模型在温度预测任务上表现出极高的准确性和稳定性，模型评估指标显示 MAE 控制在 0.13℃ 以内，R² 高达 0.999，在所有模型中表现最为优异。这一结果证明了 LGBM 对结构化气象数据的出色处理能力。

5.5.2 LSTM 模型预测
针对温度数据的时序特性，系统引入 LSTM 神经网络模型，充分利用其长短期记忆能力捕获温度变化的动态模式。模型采用三层 LSTM 架构，通过 dropout 层防止过拟合，并使用 Adam 优化器提高训练效率。

LSTM 模型对温度的短期波动和趋势变化具有良好的预测能力，特别是在温度急剧变化时段表现出比传统模型更强的适应性。评估指标显示，该模型 MAE 为 0.582℃，R² 为 0.594，虽然整体精度不及 LGBM，但在捕捉长期依赖关系方面有其独特优势。

5.5.3 Prophet 模型预测
Prophet 模型专为具有季节性和周期性的时间序列预测设计，特别适合处理气象数据中的季节性模式。该模型将时间序列分解为趋势、季节和假日成分，通过贝叶斯框架进行拟合，能有效处理数据中的缺失值和异常值。

在温度预测任务中，Prophet 模型能够很好地捕捉季节性变化趋势，但在短期精确预测方面不及其他模型，其 MAE 为 2.518℃，R² 为 0.852。不过，该模型的优势在于可以提供预测区间，量化预测的不确定性，为决策提供更全面的参考。

5.5.4 融合模型预测
为充分发挥各模型的优势，系统实现了模型融合策略，通过加权平均、动态加权等方法将多个基础模型的预测结果进行整合。融合过程考虑了各模型在历史验证数据上的表现，动态调整权重比例。

实验结果表明，加权融合模型在温度预测任务上取得了较好的综合效果，MAE 为 10.12℃，R² 为 0.856，较单一 Prophet 模型有所提升。动态融合策略能够根据预测时段自动调整各模型的权重贡献，使预测结果更加稳健。

5.6 AQI 预测模块实现
空气质量指数(AQI)是衡量空气污染程度的重要指标，系统针对 AQI 预测实现了多种算法模型，以满足不同精度和时效性需求。

5.6.1 LGBM 模型预测
LGBM 模型在 AQI 预测任务中采用了特别优化的参数配置，包括增加特征交互深度和调整正则化参数，以适应 AQI 数据的复杂模式。模型输入特征包括历史 AQI 数据、气象要素及其交互特征，特别关注 PM2.5、PM10 等与 AQI 高度相关的污染物指标。

评估结果显示，LGBM 在 AQI 预测任务上表现优异，MAE 为 4.207，R² 高达 0.93，能够准确捕捉 AQI 的短期波动和趋势变化，为空气质量预警提供可靠依据。

5.6.2 LSTM 模型预测
考虑到 AQI 指数的时序特性，系统引入 LSTM 模型处理序列依赖关系。模型架构包含两层 LSTM 层，每层 64 个神经元，输入为过去 14 天的 AQI 和气象数据序列，通过门控机制捕捉长短期依赖关系。

LSTM 模型在 AQI 预测中显示出中等性能，MAE 为 20.566，R² 为 0.575。虽然整体精度不及 LGBM，但在捕捉长期趋势和季节性变化方面表现较好，特别是在数据稀疏区域能提供更平滑的预测结果。

5.6.3 Prophet 模型预测
Prophet 模型在 AQI 预测任务中主要关注季节性和趋势成分，能够处理 AQI 数据中的多周期模式。模型设置了周期性和季节性成分，并考虑了节假日效应对空气质量的影响。

在 AQI 预测任务中，Prophet 模型的 MAE 为 21.325，R² 为 0.207，整体表现不及其他模型。但其优势在于能够分解出 AQI 的长期趋势和季节性成分，为空气质量管理提供更直观的参考。

5.6.4 融合模型预测
针对 AQI 预测，系统实现了多种融合策略，通过综合各模型的优势提高整体预测精度。融合过程考虑了各模型在不同 AQI 范围内的表现差异，对高污染和低污染状况分别优化权重配置。

加权融合模型在 AQI 预测任务上的 MAE 为 14.444，R² 为 0.600，较单一 Prophet 模型有明显提升。动态融合策略能够在污染高发期自动增加 LGBM 模型的权重贡献，提高关键时段的预测准确性。

5.7 PM2.5 预测模块实现
PM2.5 是直径小于或等于 2.5 微米的颗粒物，对人体健康有重要影响。系统针对 PM2.5 浓度预测实现了多种算法模型，以满足不同场景需求。

5.7.1 LGBM 模型预测
LGBM 模型在 PM2.5 预测任务中采用了针对性优化，包括增加树的深度和叶子节点数，以捕捉 PM2.5 浓度的复杂变化模式。模型特别关注气象因素（如风速、湿度）与 PM2.5 的交互关系，并加入时间特征增强季节性理解。

评估结果显示，LGBM 在 PM2.5 预测任务上表现出色，MAE 为 4.1μg/m³，R² 高达 0.936，能够准确捕捉 PM2.5 浓度的短期波动和趋势变化，为空气质量预警提供精确依据。

5.7.2 LSTM 模型预测
考虑到 PM2.5 浓度的时序特性，系统引入 LSTM 模型处理复杂的序列依赖关系。模型架构包含三层 LSTM 层，输入为过去 14 天的 PM2.5 浓度和气象数据序列，通过记忆单元捕捉长短期依赖关系。

LSTM 模型在 PM2.5 预测中的 MAE 为 16.68μg/m³，R² 为 0.556。虽然整体精度不及 LGBM，但在捕捉污染事件的峰值和持续模式方面表现较好，特别是在污染物迅速积累阶段能提供更敏感的预测结果。

5.7.3 Prophet 模型预测
Prophet 模型在 PM2.5 预测任务中主要关注季节性和趋势成分，能够处理污染物浓度的多周期变化模式。模型特别考虑了周、月、季度等多个周期的季节性效应，以及节假日对污染排放的影响。

在 PM2.5 预测任务中，Prophet 模型的 MAE 为 16.252μg/m³，R² 为 0.336。虽然整体精度不高，但其分解出的季节性成分能够揭示 PM2.5 浓度的周期性变化规律，为污染防控提供长期参考。

5.7.4 融合模型预测
针对 PM2.5 预测，系统实现了基于模型性能的融合策略，重点考虑各模型在不同污染程度下的表现差异。融合过程中，对高浓度 PM2.5 区间赋予更高权重，提高严重污染事件的预测准确性。

加权融合模型在 PM2.5 预测任务上的 MAE 为 11.603μg/m³，R² 为 0.639，综合性能较单一模型有所提升。动态融合策略能够在季节转换期自动调整模型权重，提高对污染物浓度变化趋势的预测灵敏度。

5.8 O3 预测模型模块实现
臭氧(O3)是一种重要的二次污染物，其生成受到光化学反应的影响，预测难度较大。系统针对 O3 浓度预测实现了多种算法模型，以满足不同精度和时效性需求。

5.8.1 LGBM 模型预测
LGBM 模型在 O3 预测任务中引入了更多与光化学反应相关的特征，如紫外线强度、NO2 浓度等，并优化了模型参数以适应 O3 生成的非线性特性。模型特别关注温度、日照等与臭氧生成密切相关的气象因素。

评估结果显示，LGBM 在 O3 预测任务上表现较好，MAE 为 8.428μg/m³，R² 为 0.855，能够较准确地捕捉 O3 浓度的日内变化规律，为臭氧污染预警提供有效支持。

5.8.2 LSTM 模型预测
考虑到 O3 浓度的复杂时空特性，系统引入 LSTM 模型处理多时间尺度的依赖关系。模型架构经过特别优化，增加了注意力机制，以捕捉气象因素与 O3 生成之间的动态关联。

LSTM 模型在 O3 预测中表现出色，MAE 为 7.663μg/m³，R² 为 0.211。在所有预测任务中，这是 LSTM 模型唯一优于 LGBM 的指标，显示出其在处理臭氧这类受多因素复杂影响的污染物时的独特优势。

5.8.3 Prophet 模型预测
Prophet 模型在 O3 预测任务中特别关注日内和季节性变化，针对臭氧形成的日周期和年周期特性进行了优化。模型考虑了光照强度的季节变化对臭氧生成的影响，提高了长期预测的准确性。

在 O3 预测任务中，Prophet 模型的 MAE 为 15.852μg/m³，R² 为 0.493。该模型能够较好地捕捉臭氧浓度的季节性变化趋势，为长期臭氧污染防控提供参考依据。

5.8.4 融合模型预测
针对 O3 预测，系统实现了特别优化的融合策略，综合考虑日内变化和季节性因素。融合过程中，LSTM 模型在预测白天高峰期的 O3 浓度时获得较高权重，而 Prophet 模型在预测季节性趋势时获得更多关注。

加权融合模型在 O3 预测任务上的 MAE 为 10.009μg/m³，R² 为 0.398。虽然整体精度不如单一的 LGBM 模型，但融合模型提供了更全面的预测视角，特别是在不同时间尺度上的变化趋势预测更为准确。

5.9 天气类别预测模块实现
天气类别预测是系统的重要功能之一，目标是预测未来天气状况（如晴、多云、雨等），属于分类任务而非回归任务。系统针对天气类别预测实现了多种算法模型，以满足不同精度和时效性需求。

5.9.1 LGBM 模型预测
针对天气类别预测，LGBM 模型使用了多分类配置，通过树结构学习不同天气类型的特征模式。模型输入特征包括历史气象数据、季节性特征以及前期天气状况的滞后特征，通过特征工程增强对天气演变规律的学习。

评估结果显示，LGBM 在天气类别预测任务上的准确率为 44.8%，加权 F1 分数为 0.415。虽然不如其他模型，但 LGBM 在预测"晴"、"多云"等高频天气类型时表现较好，为基础预测提供了可靠参考。

5.9.2 GRU 模型预测
考虑到天气演变的序列特性，系统引入 GRU 模型处理天气类型的时序变化。GRU 作为 LSTM 的简化版本，计算效率更高，同时保持了对长期依赖关系的建模能力。模型采用三层 GRU 架构，配合 Softmax 输出层实现多分类预测。

GRU 模型在天气类别预测中表现最为出色，准确率高达 64.3%，加权 F1 分数为 0.584。该模型特别擅长捕捉天气类型的连续性变化规律，如晴天转多云、多云转阴的过渡模式，提供了最准确的天气类型预测结果。

5.9.3 TCN 模型预测
时间卷积网络(TCN)是系统专门针对天气类别预测引入的深度学习架构，通过膨胀卷积有效扩大感受野，同时保持并行计算的高效性。TCN 模型能够同时捕捉短期和长期的天气变化模式，特别适合处理季节性明显的气象数据。

TCN 模型在天气类别预测中的准确率为 57.1%，加权 F1 分数为 0.416。虽然整体表现不如 GRU 模型，但在处理复杂的天气变化序列时展现出独特优势，特别是对长期依赖关系的建模能力使其在季节性预测上表现突出。

5.9.4 融合模型预测
针对天气类别预测，系统实现了投票式融合策略，综合多个模型的分类结果得出最终预测。融合过程中，GRU 模型由于其卓越表现获得了更高的投票权重，同时考虑了不同天气类型的预测难度，对罕见天气类型进行了特别优化。

加权融合模型在天气类别预测任务上的准确率为 58.7%，加权 F1 分数为 0.491。虽然没有超过 GRU 模型的表现，但融合模型在预测不同天气类型时表现更为均衡，减少了极端预测错误，提高了整体预测的可靠性。

5.5.6 单目标预测
系统实现了高度模块化的单目标预测功能，核心通过 predict_single_target 函数封装，提供针对温度、PM2.5、O3、AQI 和天气状况的预测功能。预测流程遵循标准化的数据获取-预处理-特征工程-模型推理-结果转换五阶段流程，整个 API 设计采用 RESTful 来设计，通过 Flask 框架的路由系统实现。用户可通过 HTTP GET 请求访问预测接口，可以指定模型类型、预测目标、城市名称和预测天数等参数，系统返回统一格式的预测结果。

5.5.7 多目标预测
多目标联合预测功能是通过 predict_multi_target 函数实现的，能同时预测温度、PM2.5、O3、AQI 和天气状况五类目标变量。此模块基于特征共享和协同训练原理构建，对真实环境中多变量相互影响的复杂场景进行建模。系统通过了 Flask 框架定义的路由接口提供多目标预测服务，用户可通过 HTTP GET 请求，指定城市名称和预测天数，而后获取所有目标变量的预测结果。

5.6 深度分析模块实现
系统设计了全面的深度分析可视化模块，以便直观展示气象与空气质量数据的内在规律、异常事件、因素相关性以及预测模型性能等，为用户提供数据驱动的决策支持。分析可视化平台是围绕相关性分析、异常检测、特征重要性和模型评估四个核心功能构建的，采用了交互式的设计，以支持用户灵活探索和理解复杂的气象与空气质量数据。

5.6.1 分析概览设计
分析概览模块通过精简高效的统计卡片形式，提供气象与空气质量数据分析的关键指标概览。系统有设计四个核心统计卡片：分析天数、异常天气天数、异常天气比例和强相关因素对数。每个卡片都采用醒目的样式设计，顶部显示大号数字，底部配以简洁的描述性文本，实现了"一览即知"的信息展示效果。

统计卡片是由 JavaScript 动态生成的，其数据来源于后端分析 API。系统在运行分析时，后端聚合计算得出关键统计指标，眉山市气象数据的总分析天数为 1824 天、检测到的异常天气天数为 59 天、异常天气占比为 3.23%以及强相关性因素对数量有 12 对。这些数值通过 DOM 操作实时更新到页面中，为用户提供分析的基本背景信息。

该统计卡片采用 Bootstrap 的卡片组件实现，并使用自定义样式增强视觉效果。每个卡片添加了左侧边框色彩标识，并且使用了阴影效果提升层次感。数值展示则是采用渐进式呈现策略，系统先展示占位符（"-"），待数据加载完成后再以动画效果更新为实际数值，提升用户体验。这种设计不仅美观，而且食用，还可以确保在数据加载过程中，用户也能看到清晰的界面结构。如图 37 所示：

图 37 分析概览统计卡片设计

5.6.2 相关性分析可视化
相关性分析模块探索气象因素之间的内在关联，采用热图和表格两种互补的可视化方式，全面展示数据间的复杂关系。核心组件是气象因素相关性热图，它基于 Pearson 相关系数矩阵构建，通过颜色深浅直观表达不同因素间的相关强度。热图采用蓝红双色标记方案，蓝色表示负相关，红色表示正相关，颜色深浅对应相关性强度。

相关性热图这是基于 Matplotlib 库在后端生成，通过 analyze_weather_correlation()函数计算各气象和空气质量指标间的相关系数矩阵。系统计算了温度、空气质量、PM2.5、PM10、NO2、O3 和 CO 等 7 种种关键指标之间的相关关系。热图是通过精心设计的配色方案和网格线增强可读性的，同时还会生成为静态 PNG 图像在前端加载展示。

而强相关因素对表格则提供了更精确的数值信息，展示相关系数绝对值大于 0.5 的因素对。表格包含三列：因素 A、因素 B 和相关系数，是通过 JavaScript 动态生成的。相关系数根据正负关系采用不同颜色标记：如果是正相关则为蓝色，如果是负相关则为红色，这能很好地帮助用户直观识别关系类型。而当无强相关因素对时，系统会显示"未发现强相关因素"的友好提示。

通过相关性分析，系统发现了多组有价值的强相关因素对：如 PM2.5 与 PM10 的相关系数为 0.90 这表明两种颗粒物通常同时出现；温度与 O3 相关系数 0.77 揭示了高温环境促进臭氧生成的现象。这些发现为污染源追踪和预警机制提供了科学依据。

结果如图 38、图 39 所示：

图 38 气象因素相关性热图

图 39 气象因素强相关因素表

5.6.3 异常天气检测可视化
异常天气检测模块这是针对气象与空气质量数据中的异常模式进行识别和可视化，是采用了月度异常统计图和异常详情图两级展示方式。系统基于统计方法检测异常值，主要关注温度、AQI 指数和 PM2.5 三类关键指标的异常情况。

月度异常统计图采用柱状图形式，直观地展示异常天气的时间分布特征。横轴表示年月，纵轴表示异常天数。图表也是基于 Matplotlib 库在后端生成，通过分析一年内各月份的异常事件频次，很好地揭示了显著的季节性模式：眉山市 PM2.5 异常主要集中在冬季（11-2 月），AQI 异常也主要出现在在冬季（11-2 月），这一发现与季节性气候特征和人类活动规律高度一致。

系统还通过横向进度条设计，展示每种异常指标的占比情况。进度条清晰地显示了异常天数、总占比，以及视觉化的比例表达。分析结果表明，在眉山市的异常天气中，AQI 异常占 1.9%，PM2.5 异常占 1.8%，PM10 异常占 1.2%，O3 异常占 0.2%，CO 异常占 0.9%.这些数据分布有助于环保部门确定重点监测指标。

异常详情图则是采用模态框方式实现深入分析，用户可通过点击对应的异常详情按钮，查看特定指标（温度、AQI、PM2.5 等）的异常分析详图。详情图包括异常值时间序列、异常阈值标记等信息。系统通过异步加载机制，点击按钮时，加载对应的异常详情图，确保界面响应流畅。

异常天气检测可视化为预警系统提供了直观的数据支持，帮助用户识别潜在的极端天气事件和空气污染事件，对提升气象服务和环境管理的精准性具有重要意义。

异常状况如图 40、图 41、图 42 所示：

图 40 温度月度异常统计图与异常详情展示

图 41 aqi 月度异常统计图与异常详情展示

图 42 pm2.5 月度异常统计图与异常详情展示

5.6.4 特征重要性可视化
特征重要性可视化模块是展示影响预测结果的关键因素，能帮助用户理解模型决策机制。系统针对三个核心预测目标——平均温度、AQI 指数和天气类别，分别设计了特征重要性条形图，采用了水平条形图形式，直观展示各输入特征对预测结果的影响程度。

特征重要性分析是基于 SHAP 值计算的，通过 analyze_feature_importance()函数来实现。对于每个预测目标来说，系统都会从训练好的 LightGBM 模型中提取特征重要性信息，然后选取影响最大的 10-15 个特征进行可视化。条形图横轴表示平均|SHAP 值|，纵轴为特征名称，按重要性降序排列，并采用蓝色配色方案，增强视觉吸引力和辨识度。

温度预测特征重要性图显示，最具影响力的前三特征从高到低排序为高温、低温、前一天的温度。这表明温度预测主要依赖历史温度数据，符合温度变化的物理规律。而 AQI 预测特征重要性图则显示 PM10、前一天的 aqi、co 这三者的影响最大，反映了空气质量与颗粒物浓度和气象条件的紧密关联。天气类别预测则是主要受前天天气状况、高温和回滚 7 天平均温度最小值影响，这体现了天气系统的连续性特征。

特征重要性可视化不仅提升了模型的可解释性，还较好地为特征工程提供了指导方向。通过分析结果，系统可以有针对性地优化特征选择，如对温度预测增强对历史温度数据的利用，对 AQI 预测加强对 PM2.5 和气象因素的综合考虑，从而提升模型性能。

主要特征分析结果如图 43、图 44、图 45 所示：

图 43 温度预测特征重要性

图 44 AQI 预测特征重要性

图 45 天气类别预测特征重要性

5.6.5 模型评估可视化实现
模型评估可视化模块是此系统的核心组成部分之一，为此提供了全面的预测模型性能评估工具。系统采用双层导航架构：第一层是指标选择导航栏，包括"平均温度"、"AQI 指数"、"PM2.5"、"臭氧(O3)"和"天气类别"五个选项卡；第二层这是模型选择按钮组，针对不同预测目标提供相对应的模型选择（如 LightGBM、LSTM、Prophet 等）。

对于回归型预测任务，系统实现了五类标准化评估图表，分别是针对预测准确性、误差分布和时序表现进行评估的。实际值与预测值对比散点图展示模型预测的整体准确性，横轴为实际观测值，纵轴为预测值，理想情况下点应分布在 45 度对角线上。散点图使用蓝色点表示测试样本，添加了红色拟合线和灰色理想线（y=x），帮助用户判断模型是否存在系统性偏差。

残差分布直方图分析预测误差的统计特性，展示残差（预测值减实际值）的频率分布。图表采用浅蓝色柱形和蓝色密度曲线，并标注了平均误差（红线）和标准差范围（虚线）。理想情况下，残差应呈正态分布且中心接近零，表示无系统性偏差。残差与预测值关系图则检测预测误差与预测值大小的相关性，通过蓝色散点和红色 LOWESS 平滑线，揭示可能存在的预测偏差模式，如高值区域预测不准确等问题。

模型评估指标图以水平条形图形式展示 MAE、RMSE、R² 等关键指标，不同指标使用不同颜色区分，并在条形末端标注具体数值。时间序列预测图展示模型在连续时间上的预测表现，蓝线表示实际值，红线表示预测值，中间以垂直分隔线标明历史数据与预测数据的界限。对于 Prophet 模型，还添加了 95%预测区间的浅红色阴影区。该图表直观展示模型捕捉时间趋势和波动的能力。在 O3 浓度预测分析中，LightGBM 和 LSTM 模型均能准确捕捉短期波动，而 Prophet 模型则更善于把握长期季节性趋势，但对短期波动的响应较弱。

模型估计结果如图 46 所示：

图 46 LGBM 预测温度评估可视化组合

对于分类型预测任务，系统实现了与回归任务截然不同的评估可视化方案。系统还设计了分类性能指标图，以水平条形图形式展示准确率(Accuracy)、精确率(Precision)、召回率(Recall)和 F1 分数四项关键指标，不同指标使用对比鲜明的颜色区分，并在条形末端标注具体数值。类别预测概率分布图则通过柱状图展示模型对每个测试样本的类别概率预测。横轴代表测试样例，纵轴为概率分布，不同颜色代表不同天气类别的预测概率。天气类别预测的时间序列可视化采用类别标记图设计：蓝线表示实际天气状况，红线表示模型预测的天气状况，每个天气类别映射为固定的数值代码。实验分析表明，GRU 模型在预测上比 LightGBM 模型表现更稳定，但在总体准确率上低于 TCN 模型。通过这种可视化设计，用户可以快速识别各模型的优势类别，例如 LightGBM 在"中雨"和"多云"类别上表现最佳，而 GRU 则在"晴"等类型上具有优势，TCN 在"阴"类别上表现最佳。评估可视化的交互性设计与回归任务保持一致，支持点击扩展、模型切换和详情查看等操作。

模型估计结果如图 47 所示：

图 47 天气状况分类评估可视化组合

5.6.6 交互式分析界面设计
系统整合前述各类分析与评估功能，构建了统一的交互式分析仪表板。界面采用响应式设计，基于 Bootstrap 框架实现，能自适应不同屏幕尺寸，提供一致的用户体验。分析界面主要包含三个功能区：分析控制区、结果概览区和详细分析区，通过优雅的布局设计和交互增强，实现了复杂数据的直观呈现。

分析控制区位于页面顶部，包含城市选择下拉菜单和"运行分析"按钮。用户选择目标城市并点击分析按钮后，系统通过 AJAX 请求调用后端 API，异步执行一系列分析任务，包括数据加载、相关性分析、异常检测、特征重要性计算和模型评估等。分析过程中，系统显示优雅的加载动画和进度提示，完成后自动展示分析结果。这种设计使复杂的数据分析过程变得简单直观，适合非专业用户使用。

系统实现了多种交互增强功能提升用户体验。图表全屏查看功能允许用户点击图表右上角的扩展按钮，将图表切换至全屏模式，提供更清晰的数据视图。系统实现图表状态切换，在全屏模式下还会重新加载高分辨率图像，确保显示质量。

交互式界面如图 48 所示：

图 48 交互式分析仪表板界面设计

6 系统测试
6.1 软件测试意义
软件测试是气象分析与预测系统开发过程中的关键环节，直接关系到系统功能的可靠性和预测结果的准确性。系统拥有数据采集、多算法预测和可视化展示等多个复杂功能，任何单一环节的错误都可能导致整个系统预测结果失准。通过系统化的测试活动，可以验证爬虫模块在网络不稳定情况下的数据获取能力和编码自适应能力，保证数据源数据的完整性；测试数据预处理阶段是否完成了缺失值处理与标准化的转换，保证输入模型的数据完整性测试数据预处理流程中的缺失值处理和标准化转换是否符合设计要求，保证输入模型的数据质量；测试各种机器学习模型（LightGBM、LSTM、GRU、TCN 和 Prophet）在不同预测任务上的性能表现，寻找最优的预测方案；测试可视化组件的渲染效果和交互体验，确保用户能直观理解预测结果。系统测试活动能够及时发现系统内存在的不足，并指导系统性能的完善与提升，保证系统稳定性及使用满意度。尤其是涉及到气象、空气质量预测这样和公众直接相关的服务，充分的测试能够确保系统对外输出的预测结果真实可靠，研究成果从理想变为现实。

6.2 测试项目
对眉山市 2020-2024 年实测数据进行基本功能测试，主要测试数据处理、模型预测和前端交互功能。系统测试用例如表 5 所示。
表 5 系统测试用例
测试点 输入 预期输出 实际输出 结果
爬虫数据源可靠性验证 在网络连接不稳定的情况下爬取数据 系统应执行最多 3 次重试，并记录失败原因 成功实现 3 次重试逻辑，超时后终止 通过
爬虫编码自适应测试 爬取含多种编码格式的网页（如 UTF-8、GBK） 系统自动检测编码并正确解析内容 成功尝试 utf-8、gbk、gb2312、gb18030 等编码 通过
缺失值处理功能测试 输入包含缺失值的历史天气数据 系统应按顺序使用：线性插值 → 前向填充 → 后向填充 → 中位数填充 成功按预期流程填充数据 通过
标准化处理实现测试 输入不同量级原始数据（温度、PM2.5 等） 使用 MinMaxScaler(feature\_
range=(0, 1))对训练数据标准化 数据成功归一化到 0-1 区间 通过
LightGBM 预测功能测试 输入 PM2.5 时序数据 模型应分析出趋势并提供评估指标 成功返回包含评估指标的预测结果 通过
LSTM 序列预测测试 输入臭氧时序数据 模型应分析出趋势并提供评估指标 成功输出预测结果 通过
Prophet 趋势预测测试 输入 AQI 时序数据 模型应分析出趋势并提供置信区间 成功返回包含置信区间的预测结果 通过
多模型对比测试 同一预测目标使用不同模型进行预测 系统应分别返回各模型结果供比较 前端正确展示多模型预测结果 通过
图表库功能测试 前端请求各类预测和历史数据 系统应使用 ECharts 库渲染折线图和数据可视化 ECharts 成功渲染并支持基本交互 通过
图表库功能测试 前端请求各类预测和历史数据 系统应使用 ECharts 库渲染折线图和数据可视化 ECharts 成功渲染并支持基本交互 通过
HTTP 500 错误处理测试 在文件损坏情况下请求预测 API 系统应捕获异常并返回 500 状态码和 JSON 格式错误信息 成功捕获模型加载异常并返回 500 状态码和预期错误信息 通过
7 总结与展望
7.1 总结
研究着眼于气象预测及空气质量分析领域的精度瓶颈和应用局限，构建了集数据采集、多模型融合及可视化交互于一体的预测系统。在数据层面，通过定制爬虫从公共气象网站爬取眉山市 2020-2024 年气象和空气质量数据，使用多种方法填补缺失值，进行特征提取，获得如时序差、滚动数据等高质量特征，增强数据质量。在模型融合层面，突破单一模型局限，对 LightGBM、LSTM、GRU 及 Prophet 模型进行差异化部署，构建起了覆盖短期突变及长期趋势的观测平台。
实测结果显示，LightGBM 在温度预测(MAE=0.13，R²=0.999)和 AQI 预测(MAE=4.207)方面表现最为准确；LSTM 在 O3 预测(MAE=7.663)稍高；GRU 在天气分类任务上(准确率 64.3%)表现最好。特征分析显示，PM10 对 PM2.5 预测影响很(强相关系数为 0.90)，验证了多源数据融合的必要性。
在应用层面，基于 Flask 的 Web 系统实现了查询、预测及可视化全流程，集成 ECharts 图表库创建多种可视化组件，为气象环境决策提供直观支持。系统在眉山市数据集上实现温度预测 MAE 为 0.13℃，PM2.5 预测 MAE 为 4.1μg/m³，AQI 预测 MAE 为 4.207 的性能，实现业务化应用要求。
表 6 不同模型在不同预测任务上的性能比较
比较维度 LightGBM LSTM Prophet GRU TCN
计算速度 快（秒级） 慢（分钟级） 中等（秒级） 慢（分钟级） 中等（秒级）
参数数量 中等 大量 少量 大量 大量
训练资源需求 低（内置正则化和提前停止） 高（需 Dropout 和 L2 正则化） 低（需手动设置变点先验） 高（需 Dropout/L2 正则化） 中等（需调整膨胀率）
过拟合风险 低（内置正则化） 高（需 Dropout/早停） 低（自动变点检测） 高（需 Dropout/早停） 中等（残差连接提高稳定性）
表 7 不同模型在温度预测任务上的评估指标比较
指标 LightGBM LSTM Prophet 平均融合 加权融合 动态融合
MAE 0.13 0.582 2.518 0.991 10.12 1.023
RMSE 0.21 0.756 3.069 1.278 1.211 1.251
R2 0.999 0.594 0.852 0.831 0.856 0.839
MAPE 1.311 11.843 22.37 11.13 11.01 10.66
表 8 不同模型在 AQI 预测任务上的评估指标比较
指标 LightGBM LSTM Prophet 平均融合 加权融合 动态融合
MAE 4.207 20.566 21.325 14.137 14.444 14.598
RMSE 7.972 25.892 26.745 19.193 18.183 18.789
R2 0.93 0.575 0.207 0.582 0.600 0.588
MAPE 7.728 23.973 50.35 25.71 25.44 24.61
表 9 不同模型在 PM2.5 预测任务上的评估指标比较
指标 LightGBM LSTM Prophet 平均融合 加权融合 动态融合
MAE 4.1 16.68 16.252 11.356 11.603 11.727
RMSE 6.422 21.014 20.671 15.234 14.432 14.913
R2 0.936 0.556 0.336 0.621 0.639 0.627
MAPE 17.678 28.164 89.699 42.47 42.02 44.66

表 10 不同模型在 O3 预测任务上的评估指标比较
指标 LightGBM LSTM Prophet 平均融合 加权融合 动态融合
MAE 8.428 7.663 15.852 9.786 10.009 10.116
RMSE 10.971 8.768 20.486 12.738 12.067 12.469
R2 0.855 0.211 0.493 0.387 0.398 0.390
MAPE 17.086 27.934 30.653 23.71 23.45 22.70
表 11 不同模型在天气类别预测任务上的评估指标比较
指标 LightGBM GRU TCN 平均融合 加权融合 动态融合
Accuracy 0.448 0.643 0.571 0.582 0.587 0.576
F1(weighted) 0.415 0.584 0.416 0.486 0.491 0.500
Precision(weighted)) 0.449 0.554 0.327 0.461 0.478 0.456
Recall(weighted)) 0.448 0.643 0.571 0.571 0.565 0.593
7.2 展望
虽然研究系统在分析数据的收集、多元算法模型预测、交互可视化方面等方面获得了一定的进展，但由于气象环境领域应用场景复杂多变，所以有多项技术方向仍可进行深入研究和探索。在数据采集与扩展方面，当前系统仅从 tianqihoubao.com 爬取眉山市 2020-2024 年的气象和空气质量数据，未来可将基于 requests 的爬虫系统转换为基于 Scrapy 框架的爬虫系统，以实现分布式爬取和更灵活的数据源管理，同时还可扩展数据采集范围至更多的城市，还可以增加数据来源，比如中国气象局、环保部门开放数据平台等，以构建更全面完整的数据生态系统。而在模型优化与创新方面，通过评估指标可以得出 LightGBM 在所有预测任务中表现最佳，而 LSTM 和 GRU 在特定任务上也有其独特的优势，在未来，引入 Transformer 架构来增强对长序列气象数据的依赖关系捕捉能力，可以用来替代当前的 LSTM 模型对温度的预测。目前，在存储与计算架构方面，系统使用了 SQLite 存储数据，随着数据量增长和实时预测需求提升，可以实现向分布式数据库的迁移，引入 MongoDB 存储非结构化数据和 InfluxDB 等时序数据库处理时间序列信息，构建起独特的混合存储架构，同时在计算架构方面，还可使用 Docker 实现模型服务的标准化部署，并且通过 Kubernetes 来进行编排，使系统在高并发预测请求下也可实现弹性扩展。
致谢
时光飞逝，岁月如梭，我的大学生涯即将结束，在这篇论文完成之际，对所有支持和帮助我的老师、同学和朋友表示由衷的感谢。
整个毕业设计完成的过程充满曲折，感谢指导老师给予的指导和帮助，使我最终顺利完成毕业设计。

参考文献
[1]孙健, 曹卓, 李恒, 等. 人工智能技术在数值天气预报中的应用[J]. 应用气象学报, 2021, 32(1): 1-11.
[2]Leutbecher M, Lock S J, Ollinaho P, et al.Stochastic representations of model uncertainties at ECMWF:State of the art and future vision[J].Quarterly Journal of the Royal Meteorological Society, 2017, 143(707): 2315-2339.
[3]Kalnay E, Kanamitsu M, Kistler R, et al.The NCEP/NCAR 40-year reanalysis project[M]//Renewable energy.Routledge, 2018: Vol1_146-Vol1_194.
[4]师春香, 潘旸, 谷军霞, 等.多源气象数据融合格点实况产品研制进展[J].气象学报, 2019, 77(4):774-783.
[5]王婧卓, 陈法敬, 陈静, 等.GRAPES 区域集合预报对 2019 年中国汛期降水预报评估[J].大气科学, 2021, 45(3): 664-682.
[6]常煜, 温建伟, 杨雪峰, 等.基于 CMA-TYM 和 SCMOC 的嫩江流域暴雨检验[J].应用气象学报, 2023, 34(2): 154-165.
[7]周春红, 饶晓琴, 盛黎, 等.尺度适应性起沙机制在 CMA-CUACE/Dust 中的应用[J].应用气象学报, 2024, 35(4): 400-413.
[8]孙健, 曹卓, 李恒, 等.人工智能技术在数值天气预报中的应用[J].应用气象学报, 2021, 32(1):1-11.
[9]魏佳妹, 袁书娟, 孔闪闪,等. 轻梯度提升机算法的发展与应用[J].Journal of Computer Engineering & Applications, 2025, 61(5).
[10]季通焱, 黄鹏年, 李艳忠, 等.长短时记忆网络与新安江模型耦合的降雨径流模拟性能[J].水力发电学报, 2024, 43(1): 24-34.
[11]李涛, 崔磊波, 王姣娥, 等.基于时间序列预测的城际出行韧性评估方法与时空格局[J].Tropical Geography, 2024, 44(5).
[12]郭燕, 赖锡军.基于循环神经网络的洞庭湖水位预测研究[J].长江流域资源与环境, 2021, 30(3):689.
[13]刘善峰, 李哲, 陈锦鹏, 等.基于误差修正的极端天气下风速预测[J].Journal of Nanjing University of Information Science & Technology (Natural Science Edition)/Nanjing Xinxi Gongcheng Daxue Xuebao (ziran kexue ban), 2023, 15(5).
[14]李社宏.大数据时代气象数据分析应用的新趋势[J].陕西气象, 2014, 2:41-44.
[15]代明慧, 于法稳.气候变化背景下农业绿色发展能力提升研究[J].中州学刊, 2024, 4:49-56.
