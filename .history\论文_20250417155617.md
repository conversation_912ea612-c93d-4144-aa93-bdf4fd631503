# 基于机器学习的气象分析与预测系统的设计和实现

## 摘要

随着气候变化日益显著，准确的气象和空气质量预测对于公共健康、环境保护和社会经济活动具有重要意义。本研究设计并实现了一个基于机器学习的气象分析与预测系统，该系统集成了多种预测模型，包括 LightGBM、LSTM/GRU 和 Prophet 模型，能够对温度、空气质量指数（AQI）、PM2.5 浓度、臭氧（O3）浓度以及天气状况进行精确预测。系统采用 Flask 框架构建 Web 应用，实现了数据爬取与清洗、特征工程、模型训练与评估、数据可视化与用户交互等功能。实验结果表明，该系统在各项预测指标上均展现出良好的预测精度，其中 LightGBM 模型在整体性能上表现最佳。此外，系统还提供了直观的数据可视化界面和友好的用户交互体验，为用户提供了全面的气象数据分析和预测服务。

## 关键词

气象预测、空气质量预测、机器学习、深度学习、LightGBM、LSTM、Prophet、Web 应用、时间序列预测

# 第一章 绪论

## 1.1 研究背景与意义

近年来，随着全球气候变化和工业化进程的加速，极端天气事件频发，空气污染问题也日益突出。准确的气象和空气质量预测对于政府决策、环境保护、城市规划、农业生产以及公众日常生活均具有重要意义。传统的气象预测主要依赖于物理模型，虽然在短期预测中表现良好，但在处理多种因素交互影响下的长期预测时面临挑战。而基于统计方法的传统空气质量预测模型在处理复杂的非线性关系时往往效果有限。

随着机器学习和深度学习技术的快速发展，将这些先进的人工智能技术应用于气象和空气质量预测领域已成为研究热点。机器学习方法能够从大量历史数据中自动提取特征和模式，学习各环境因素之间的复杂关系，并基于这些学习结果进行预测。相比传统方法，机器学习方法具有更强的非线性关系处理能力、更好的适应性以及更高的预测精度。

此外，随着互联网和物联网技术的普及，大量气象和空气质量数据得以收集和存储，为机器学习模型的训练提供了丰富的数据支持。而 Web 应用的开发则使得这些预测结果能够以直观、交互式的方式呈现给终端用户，提高了气象和空气质量信息的可访问性和实用性。

因此，开发一个基于机器学习的气象分析与预测系统，不仅能够提高预测的准确性，还能够将复杂的预测结果以简明易懂的方式呈现给用户，为公众提供更精确、更及时的气象和空气质量信息服务，具有显著的社会价值和实用意义。

## 1.2 国内外研究现状

气象和空气质量预测是一个广泛研究的领域，国内外学者已经开展了大量相关工作。这些研究主要可以分为传统方法和基于机器学习的方法两大类。

**传统气象和空气质量预测方法**

传统气象预测主要基于数值天气预报（NWP）模型，如欧洲中期天气预报中心（ECMWF）开发的全球预报系统和美国国家环境预报中心（NCEP）的全球预报系统（GFS）。这些模型基于流体力学和热力学原理，通过求解控制方程组来模拟大气的演变过程。虽然这些模型随着计算能力的提升不断完善，但仍然面临计算复杂度高、初始条件敏感等挑战。

传统的空气质量预测则主要依赖于扩散模型、源-受体模型和统计模型。这些模型通常需要详细的排放清单和气象条件作为输入，对数据质量要求较高，同时在处理复杂的化学反应和微观过程时存在局限性。

**基于机器学习的预测方法**

近年来，随着机器学习技术的快速发展，国内外研究者开始将各种机器学习算法应用于气象和空气质量预测中。初期的研究主要集中在支持向量机（SVM）、随机森林（RF）和人工神经网络（ANN）等经典算法上。例如，Feng 等人（2015）使用 SVM 模型预测了北京市的 PM2.5 浓度，取得了比传统统计方法更好的效果。

随着深度学习的兴起，更多研究开始关注基于深度学习的预测方法。长短期记忆网络（LSTM）由于其捕捉时序数据长期依赖关系的能力，被广泛应用于气象和空气质量预测。例如，Zhang 等人（2018）设计了一种基于 LSTM 的模型，用于预测中国城市的 PM2.5 浓度，结果显示该模型在不同时间尺度上均表现出良好的预测效果。

近期研究也开始探索集成学习和混合模型的应用。例如，Fan 等人（2020）提出了一种结合 XGBoost 和 LSTM 的混合模型，用于北京市的空气质量预测，该模型综合了传统机器学习和深度学习的优势，取得了较高的预测精度。梯度提升决策树（GBDT）类算法，如 LightGBM、XGBoost，因其在结构化数据上的优秀表现，也被广泛应用于气象和空气质量预测任务。

**Web 应用和可视化系统**

在系统实现方面，国内外已有多个基于机器学习的气象和空气质量预测 Web 应用。例如，美国环保署（EPA）的 AirNow 系统提供了基于数据驱动模型的空气质量预测和可视化服务。在国内，中国环境监测总站开发的全国城市空气质量实时发布平台也集成了预测功能。

然而，现有系统大多只关注单一预测目标，如仅预测温度或仅预测 PM2.5 浓度，缺乏对多个气象和空气质量指标的综合预测。同时，许多系统在模型选择上也相对单一，未能充分利用不同模型的优势。此外，在数据可视化和用户交互方面，现有系统仍有较大的改进空间。

**研究差距**

尽管已有大量相关研究，但仍存在以下几个方面的不足：

1. 多目标综合预测：现有研究多集中于单一指标预测，缺乏对温度、AQI、PM2.5、臭氧等多项指标的综合预测。
2. 多模型比较与集成：对不同类型机器学习模型在气象和空气质量预测任务上的系统性比较和集成研究不足。
3. 特征工程的充分研究：特征工程对预测效果的影响未得到充分研究和利用。
4. 系统集成与用户体验：将预测模型集成到易用的 Web 应用中，并提供良好的数据可视化和用户体验的研究相对较少。

本研究旨在弥补这些差距，设计并实现一个集成多种机器学习模型、支持多目标预测的气象分析与预测系统，并通过 Web 应用的形式提供给用户使用。

## 1.3 研究内容与目标

本研究的主要内容是设计并实现一个基于机器学习的气象分析与预测系统，该系统能够利用历史气象和空气质量数据，结合多种机器学习模型，对未来的气象状况和空气质量进行预测，并通过 Web 应用的形式提供给用户使用。具体研究内容包括：

1. **数据采集与预处理**：设计并实现爬虫程序，从公开的气象和空气质量网站收集历史数据；对采集的数据进行清洗、标准化和特征工程，为模型训练准备高质量的数据集。

2. **多模型设计与实现**：实现并比较 LightGBM、LSTM/GRU、Prophet 等多种预测模型，评估它们在不同预测任务上的性能；为每类预测目标（温度、AQI、PM2.5、臭氧、天气状况）选择最合适的模型。

3. **Web 应用开发**：基于 Flask 框架设计并实现 Web 应用，包括数据展示、历史分析、预测功能和用户管理等模块；设计直观、友好的用户界面，提供丰富的数据可视化功能。

4. **系统评估与优化**：对系统的预测精度、响应时间等指标进行全面评估；基于评估结果对模型和系统进行优化，提高系统的整体性能。

本研究的具体目标如下：

1. **预测精度目标**：

   - 温度预测：平均绝对误差（MAE）控制在 1.5℃ 以内
   - AQI 预测：MAE 控制在 15 以内
   - PM2.5 预测：MAE 控制在 10μg/m³ 以内
   - 臭氧预测：MAE 控制在 15μg/m³ 以内
   - 天气状况预测：分类准确率达到 85%以上

2. **功能目标**：

   - 实现对 5 种以上城市的气象和空气质量数据的收集和预测
   - 支持最长 15 天的未来预测
   - 提供至少 5 种不同类型的数据可视化图表
   - 实现用户注册、登录和个性化设置功能

3. **性能目标**：
   - 系统响应时间：预测请求处理时间控制在 3 秒以内
   - 系统稳定性：能够处理并发用户请求，保持 7\*24 小时稳定运行
   - 用户体验：界面友好，操作简单，信息展示清晰

通过实现上述研究内容和目标，本研究旨在提供一个高精度、多功能、用户友好的气象分析与预测系统，为用户提供科学、准确的气象和空气质量预测服务，同时也为机器学习在气象和环境领域的应用提供实践案例和参考。

# 第二章 相关技术介绍

本章将介绍本系统设计和实现过程中使用的主要技术，包括机器学习基础理论、时间序列预测方法、Web 应用开发技术以及数据可视化技术等。这些技术构成了系统的技术基础，对理解系统的设计思路和实现方法具有重要意义。

## 2.1 机器学习基础理论

机器学习是一门研究如何使计算机系统从数据中自动学习并改进的科学。在本系统中，我们主要采用了监督学习、集成学习和深度学习等方法进行气象和空气质量预测建模。

### 2.1.1 监督学习

监督学习是机器学习中最常见的学习范式，其核心思想是通过已标记的训练样本（输入-输出对）来学习一个从输入到输出的映射函数。在气象和空气质量预测中，监督学习主要应用于两类问题：回归问题（预测连续值，如温度、AQI 指数）和分类问题（预测离散类别，如天气状况）。

监督学习的一般流程包括：数据收集和预处理、特征工程、模型选择和训练、模型评估和优化等步骤。在模型评估阶段，回归问题通常使用均方误差（MSE）、平均绝对误差（MAE）、决定系数（R²）等指标；分类问题则常用准确率（Accuracy）、精确率（Precision）、召回率（Recall）、F1 分数等指标。

本系统中，我们将监督学习应用于温度、AQI、PM2.5、臭氧的回归预测以及天气状况的分类预测。通过对历史数据的学习，系统能够从新的输入特征预测未来的气象和空气质量指标。

### 2.1.2 集成学习

集成学习是将多个基学习器组合起来，形成更强大的学习器的方法。其核心思想是"三个臭皮匠，胜过一个诸葛亮"—通过组合多个相对较弱的模型，可以构建出性能更优的预测模型。集成学习主要有三种类型：bagging（如随机森林）、boosting（如 AdaBoost、GBDT）和 stacking。

在本系统中，我们重点使用了 LightGBM，这是一种基于梯度提升决策树（GBDT）的高效集成学习框架。LightGBM 由微软研究院开发，具有训练速度快、内存占用小、准确度高等优点，特别适合处理结构化表格数据。

LightGBM 的主要技术创新包括：

1. **基于直方图的决策树算法**：将连续特征值离散化为 k 个箱子，显著减少了查找最优分割点所需的计算量。
2. **带深度限制的叶子优先生长策略**：与传统的按层生长策略不同，LightGBM 选择具有最大增益的叶子节点进行生长，同时加入深度限制以避免过度生长。
3. **特征并行和数据并行**：支持特征并行和数据并行两种并行学习方式，提高了训练效率。
4. **稀疏特征优化**：能够高效处理稀疏特征，这在处理时间序列数据的滞后特征时特别有用。

在本系统中，LightGBM 被用于所有预测任务（温度、AQI、PM2.5、臭氧和天气状况），利用其快速训练和高精度预测的特性，处理包含时间特征、滞后特征和滚动特征在内的高维特征空间。

### 2.1.3 深度学习

深度学习是机器学习的一个分支，它使用多层人工神经网络来模拟人脑的学习过程。深度学习在图像识别、自然语言处理和时间序列预测等领域取得了显著成功。在时间序列预测中，递归神经网络（RNN）及其变体如长短期记忆网络（LSTM）和门控循环单元（GRU）是最常用的深度学习模型。

LSTM 和 GRU 是专门为处理序列数据设计的神经网络结构，能够有效捕捉数据中的长期依赖关系，这对于气象和空气质量预测这类时间序列问题至关重要。

LSTM 的核心是一个记忆单元，包含三个"门"结构：输入门、遗忘门和输出门。这些门控制着信息的流入、存储和流出，使 LSTM 能够学习何时记住、何时忘记以及何时输出信息。LSTM 的这种设计使其能够避免传统 RNN 中的梯度消失问题，更好地捕捉长期依赖关系。

GRU 是 LSTM 的一种简化变体，它只有两个门：更新门和重置门。与 LSTM 相比，GRU 参数更少，训练速度更快，但在某些任务上表现可能略逊于 LSTM。

在本系统中，我们将 LSTM 模型用于温度、AQI、PM2.5 和臭氧的回归预测，利用其捕捉时间序列长期依赖关系的能力；将 GRU 模型用于天气状况的分类预测，利用其在序列分类任务上的高效性能。

## 2.2 时间序列预测方法

时间序列预测是指利用历史观测数据预测未来值的过程。气象和空气质量数据本质上是时间序列数据，具有明显的时间依赖性、季节性和趋势性。针对这类数据的特点，我们采用了传统的 ARIMA 模型、Facebook 开发的 Prophet 模型以及基于深度学习的时间序列模型。

### 2.2.1 ARIMA 模型简介

自回归综合移动平均模型（ARIMA）是经典的时间序列预测方法，由三个部分组成：自回归（AR）、差分（I）和移动平均（MA）。ARIMA 模型通常表示为 ARIMA(p,d,q)，其中 p 是自回归项数，d 是差分阶数，q 是移动平均项数。

ARIMA 模型的基本假设是时间序列数据在经过适当差分后是平稳的，即其统计特性（如均值和方差）不随时间变化。模型首先通过差分操作使序列平稳，然后通过 AR 和 MA 部分捕捉序列的自相关结构。

虽然 ARIMA 模型在处理线性、平稳的时间序列时表现良好，但对于具有复杂非线性关系和多变量影响的气象和空气质量数据，其预测能力可能有限。因此，在本系统中，我们主要将 ARIMA 作为基准模型，用于与其他更复杂的模型进行性能比较。

### 2.2.2 Prophet 模型简介

Prophet 是由 Facebook 开发的时间序列预测工具，专为处理具有强烈季节性和多个季节性的业务时间序列而设计。Prophet 模型基于分解思想，将时间序列分解为趋势、季节性和假日效应三个主要组成部分：

$$y(t) = g(t) + s(t) + h(t) + \epsilon_t$$

其中，$g(t)$表示趋势项，可以是线性或逻辑增长曲线；$s(t)$表示季节性，如每周、每月或每年的周期性变化；$h(t)$表示假日效应；$\epsilon_t$表示误差项。

Prophet 的主要优点包括：

1. **处理缺失数据的能力**：能够自动处理数据中的缺失值。
2. **识别多层次季节性**：可以同时捕捉每天、每周、每月和每年的季节性模式。
3. **处理异常值**：对异常值具有鲁棒性，不易受极端值影响。
4. **自动变点检测**：能够检测时间序列中的趋势变化点。
5. **易用性**：使用简单，无需复杂的参数调整。

在本系统中，我们将 Prophet 模型用于温度、AQI、PM2.5 和臭氧的预测，特别是对于需要捕捉明显季节性模式的长期预测任务。

### 2.2.3 深度学习时间序列模型应用

近年来，深度学习在时间序列预测领域取得了显著进展。相比传统的统计模型，深度学习模型能够自动从原始数据中学习特征表示，捕捉复杂的非线性关系，并处理多变量输入。在气象和空气质量预测中，深度学习模型的这些特性尤为重要。

在本系统中，我们主要应用了两种深度学习时间序列模型：LSTM 和 GRU。这两种模型都是 RNN 的变体，专门设计用于处理序列数据。与传统 RNN 相比，LSTM 和 GRU 通过精心设计的门控机制，能够更好地处理长期依赖关系，避免梯度消失问题。

LSTM/GRU 模型在时间序列预测中的应用流程如下：

1. **数据准备**：将原始时间序列数据转换为监督学习问题，即使用过去 n 个时间步的观测值预测未来时间步的值。
2. **序列构建**：构建固定长度的输入序列（lookback 窗口）和对应的目标值。
3. **模型设计**：设计包含一个或多个 LSTM/GRU 层的神经网络架构，后接全连接层进行预测输出。
4. **模型训练**：使用历史数据训练模型，通常采用均方误差或平均绝对误差作为损失函数。
5. **递归预测**：对于多步预测，采用递归策略，即使用模型对 t+1 的预测结果作为输入，预测 t+2 的值，依此类推。

在本系统中，我们为不同的预测任务设计了特定的 LSTM/GRU 架构。对于回归任务（温度、AQI、PM2.5、臭氧），使用具有一层 LSTM 单元和全连接输出层的网络，输出连续值预测；对于分类任务（天气状况），使用 GRU 单元配合 softmax 输出层，预测不同天气类别的概率分布。

## 2.3 Web 应用开发技术

Web 应用开发是将机器学习模型部署为可用服务的关键步骤。一个良好的 Web 应用不仅需要在后端实现高效的数据处理和模型推理，还需要提供友好的用户界面和交互体验。本系统采用了 Flask 框架作为后端，结合现代前端技术构建了完整的 Web 应用。

### 2.3.1 后端框架

Flask 是一个轻量级的 Python Web 框架，以其简洁性、灵活性和扩展性著称。Flask 基于 Werkzeug WSGI 工具库和 Jinja2 模板引擎，提供了构建 Web 应用所需的核心功能，同时保持了极高的可定制性。

在本系统中，我们选择 Flask 作为后端框架的主要原因包括：

1. **与 Python 生态系统的无缝集成**：气象预测系统使用的机器学习模型（如 LightGBM、Keras）都有 Python 接口，Flask 能够直接调用这些模型。
2. **蓝图功能**：Flask 的蓝图（Blueprint）功能允许将应用分解为功能性模块，便于组织和维护复杂应用的代码。
3. **扩展丰富**：Flask 有丰富的扩展生态系统，如 Flask-Login（用户认证）、Flask-SocketIO（实时通信）等，可以方便地扩展应用功能。
4. **易于部署**：Flask 应用可以轻松部署到各种环境，从简单的开发服务器到生产级 WSGI 服务器。

我们使用 Flask 实现了以下主要功能：

1. **用户认证**：使用 Flask-Login 扩展实现用户注册、登录和会话管理。
2. **RESTful API**：设计并实现了一系列 API，用于数据查询、模型预测和结果返回。
3. **数据库交互**：通过 SQLite 数据库存储气象数据、空气质量数据和用户信息。
4. **模板渲染**：使用 Jinja2 模板引擎渲染 HTML 页面，实现数据的动态展示。
5. **异步任务处理**：处理耗时的预测任务和数据处理任务。

### 2.3.2 前端技术栈

前端是用户与系统交互的界面，良好的前端设计不仅能提升用户体验，还能更有效地展示复杂的气象和空气质量数据。本系统的前端技术栈包括 HTML、CSS、JavaScript 以及多个前端库和框架。

**HTML 与 CSS**：
使用 HTML5 提供页面结构，CSS3 负责样式表现。系统采用了响应式设计原则，确保在不同设备上（从桌面到移动设备）都能提供良好的用户体验。我们使用了 CSS 变量、Flexbox 和 Grid 布局等现代 CSS 特性，实现了灵活、一致的界面风格。

**JavaScript**：
JavaScript 是前端交互的核心语言。在本系统中，JavaScript 主要用于：

1. 处理用户交互事件（如点击、输入、提交表单）。
2. 发送 AJAX 请求，与后端 API 通信。
3. 动态更新页面内容，如显示预测结果、加载新数据。
4. 实现数据可视化，如绘制图表、地图等。

**前端库与框架**：

1. **jQuery**：简化 DOM 操作和事件处理。
2. **ECharts**：用于创建交互式图表和数据可视化。
3. **Bootstrap**：提供响应式布局和 UI 组件。
4. **Moment.js**：处理日期和时间格式化。
5. **Axios**：处理 HTTP 请求，与后端 API 通信。

我们的前端实现遵循了以下设计原则：

1. **模块化**：将功能划分为独立的模块，便于维护和扩展。
2. **渐进增强**：确保基本功能在所有浏览器中可用，在支持现代特性的浏览器中提供增强体验。
3. **交互反馈**：提供及时的视觉反馈，如加载指示器、成功/错误消息等。
4. **一致性**：保持整个应用的设计语言和交互模式一致。
5. **性能优化**：优化资源加载和渲染性能，提供流畅的用户体验。

通过后端 Flask 框架和前端技术栈的结合，我们构建了一个功能完整、交互流畅的气象分析与预测 Web 应用。

## 2.4 数据可视化技术

数据可视化是将复杂数据转化为直观、易理解的图形表示的过程。在气象和空气质量分析中，有效的可视化不仅能帮助用户快速理解数据趋势和模式，还能支持决策制定。本系统采用了多种可视化技术，以不同形式展示历史数据、预测结果和分析洞见。

### 2.4.1 可视化库

本系统主要使用 ECharts 作为核心可视化库。ECharts 是由百度开发的强大的交互式图表库，支持多种图表类型和丰富的定制选项。选择 ECharts 的主要原因包括：

1. **多种图表类型**：ECharts 支持线图、柱状图、散点图、饼图、热力图、雷达图等多种图表类型，满足气象和空气质量数据的多样化可视化需求。
2. **强大的交互功能**：支持缩放、平移、数据筛选、悬停提示等交互功能，使用户能够深入探索数据。
3. **主题定制**：提供主题定制功能，能够与系统整体设计风格保持一致。
4. **响应式设计**：图表可以自适应不同屏幕尺寸，提供良好的移动端体验。
5. **时间序列支持**：提供专门的时间轴组件，非常适合展示气象和空气质量的时间序列数据。
6. **大数据渲染**：能够高效渲染大量数据点，适合展示长时间跨度的历史数据。

在本系统中，我们使用 ECharts 实现了以下主要可视化功能：

1. **时间序列图**：展示温度、AQI、PM2.5、臭氧等指标随时间的变化趋势，同时显示历史数据和预测数据。
2. **对比图**：比较不同模型（LightGBM、LSTM、Prophet）的预测结果，或比较不同时期的数据。
3. **热力图**：以日历形式展示一年中每天的气象或空气质量数据，直观显示季节性模式。
4. **饼图**：展示不同空气质量级别或天气状况的分布比例。
5. **雷达图**：综合展示多个空气质量指标（如 PM2.5、PM10、SO2、NO2、O3、CO）的状况。
6. **组合图表**：将多种图表类型组合，如在同一图表中使用线图展示温度和柱状图展示降水量。

除了 ECharts，我们还在一些特定场景中使用了 Bootstrap 的内置图表组件和自定义 CSS 动画效果，增强数据展示的视觉吸引力。

数据可视化在本系统中扮演着至关重要的角色，不仅是数据展示的工具，也是用户交互的核心环节。通过精心设计的可视化图表，我们使复杂的气象和空气质量数据变得直观、有意义，为用户提供了深入理解数据和预测结果的能力。

# 第三章 系统需求分析

需求分析是系统开发过程中的关键环节，明确的需求定义不仅指导了系统的设计和实现，还为后续的测试和评估提供了基准。本章将从功能需求、非功能需求、数据需求以及用户角色定义等方面，对气象分析与预测系统的需求进行详细分析。

## 3.1 系统功能需求

基于对用户需求的调研和分析，本系统应当具备以下核心功能：

### 3.1.1 用户管理功能

用户管理是系统的基础功能，用于识别和管理系统用户，保障系统安全，支持个性化服务。具体需求包括：

- **用户注册**：系统应支持新用户注册，收集必要的用户信息（如用户名、密码）。
- **用户登录**：提供安全的认证机制，验证用户身份。
- **用户权限管理**：区分普通用户和管理员角色，不同角色拥有不同的系统访问权限。
- **个人信息管理**：允许用户查看和修改个人信息。

### 3.1.2 数据管理功能

数据管理功能负责系统中各类数据的存储、查询和展示，是系统的核心部分。具体需求包括：

- **历史数据查询**：支持按城市、日期范围、数据类型等条件查询历史气象和空气质量数据。
- **数据可视化展示**：以图表形式直观展示查询结果，包括但不限于线图、柱状图、饼图、热力图等。
- **数据导出**：允许用户将查询结果导出为 CSV、Excel 等格式，方便进一步分析。
- **数据统计分析**：提供基本的统计分析功能，如计算平均值、最大/最小值、标准差等。

### 3.1.3 预测分析功能

预测分析是本系统的核心功能，通过机器学习模型分析历史数据，预测未来气象和空气质量状况。具体需求包括：

- **多指标预测**：能够预测多种气象和空气质量指标，包括平均温度、AQI 指数、PM2.5 浓度、臭氧浓度和天气状况。
- **多模型支持**：支持多种预测模型，包括 LightGBM、LSTM/GRU、Prophet 等，并允许用户选择使用的模型。
- **多时间尺度预测**：支持短期（1-3 天）、中期（4-7 天）和长期（8-15 天）预测，满足不同应用场景的需求。
- **预测结果可视化**：以图表形式直观展示预测结果，并与历史数据对比。
- **预测准确性评估**：提供预测结果的准确性评估指标，如 MAE、RMSE 等。
- **智能建议生成**：基于预测结果，生成针对不同情况的智能建议，如空气质量差时的防护建议。

### 3.1.4 系统管理功能

系统管理功能用于维护系统的正常运行，管理系统资源，监控系统性能。具体需求包括：

- **数据源管理**：管理和监控各数据源的状态，确保数据采集的正常进行。
- **模型管理**：管理预测模型的训练、更新和部署。
- **系统日志**：记录系统运行日志，用于问题排查和系统优化。
- **性能监控**：监控系统关键性能指标，如响应时间、资源利用率等。

## 3.2 系统非功能需求

非功能需求关注系统的质量属性，如性能、安全性、可靠性等，这些需求对系统的整体用户体验至关重要。

### 3.2.1 性能需求

- **响应时间**：

  - 页面加载时间：主要页面应在 3 秒内完成加载。
  - 数据查询响应：一般查询应在 2 秒内返回结果。
  - 预测计算时间：单次预测请求处理时间不应超过 5 秒。

- **并发处理能力**：

  - 系统应能同时处理至少 50 个并发用户请求。
  - 在高峰期，系统性能下降不应超过 30%。

- **资源利用率**：
  - CPU 利用率：正常负载下不超过 50%，峰值负载下不超过 80%。
  - 内存利用率：正常负载下不超过 60%，峰值负载下不超过 90%。

### 3.2.2 安全性需求

- **用户认证与授权**：

  - 实施安全的用户认证机制，密码应加密存储。
  - 基于角色的访问控制，确保用户只能访问其权限范围内的功能和数据。

- **数据安全**：

  - 传输数据加密，特别是用户敏感信息。
  - 防止 SQL 注入、跨站脚本攻击等常见 Web 安全威胁。

- **日志与审计**：
  - 记录关键操作日志，便于安全审计和问题追踪。
  - 系统异常行为监控和报警机制。

### 3.2.3 可靠性需求

- **系统稳定性**：

  - 系统应 7×24 小时稳定运行，每月计划内停机时间不超过 4 小时。
  - 系统平均无故障时间（MTBF）应不低于 720 小时。

- **容错与恢复**：
  - 系统应具备数据备份和恢复能力，确保数据不丢失。
  - 在组件故障时，系统应能优雅降级，保持核心功能可用。
  - 系统应能自动恢复非致命错误，无需人工干预。

### 3.2.4 可用性需求

- **界面友好性**：

  - 直观易用的用户界面，符合现代 Web 设计标准。
  - 提供必要的用户引导和帮助信息。
  - 适当的错误提示和处理机制。

- **跨平台兼容性**：

  - 支持主流 Web 浏览器（Chrome、Firefox、Safari、Edge）。
  - 响应式设计，适应不同屏幕尺寸的设备（桌面、平板、手机）。

- **可访问性**：
  - 符合 Web 内容可访问性指南（WCAG）2.1 的 AA 级标准。
  - 为视觉障碍用户提供屏幕阅读器支持。

### 3.2.5 可维护性需求

- **模块化设计**：

  - 系统应采用模块化设计，便于单独开发、测试和维护各个功能模块。

- **扩展性**：

  - 系统架构应支持未来功能扩展，如增加新的预测模型、数据源或用户界面。
  - 软件组件应松耦合，以便于替换或升级个别组件。

- **文档完备性**：
  - 提供完整的系统设计文档、API 文档和用户手册。
  - 代码应有良好的注释和版本控制。

## 3.3 数据需求分析

数据是预测系统的核心资产，数据质量和完整性直接影响预测准确性。本节分析系统所需的各类数据特征和要求。

### 3.3.1 气象数据需求

- **数据类型**：

  - 温度数据（日平均温度、最高温度、最低温度）
  - 天气状况（晴、多云、阴、雨、雪等）
  - 风力信息（风向、风速）
  - 湿度数据（选择性收集）
  - 降水量数据（选择性收集）

- **数据特性**：

  - 时间粒度：日数据
  - 空间粒度：城市级
  - 时间范围：至少近 3 年的历史数据
  - 数据格式：结构化数据，支持数据库存储

- **数据来源**：
  - 公开的气象数据网站
  - 气象局官方 API（如有）
  - 第三方气象数据服务

### 3.3.2 空气质量数据需求

- **数据类型**：

  - AQI 指数（空气质量指数）
  - PM2.5 浓度（μg/m³）
  - PM10 浓度（μg/m³）
  - 臭氧（O₃）浓度（μg/m³）
  - 二氧化硫（SO₂）浓度（μg/m³）
  - 二氧化氮（NO₂）浓度（μg/m³）
  - 一氧化碳（CO）浓度（mg/m³）
  - 空气质量等级（优、良、轻度污染等）

- **数据特性**：

  - 时间粒度：日数据
  - 空间粒度：城市级
  - 时间范围：至少近 3 年的历史数据
  - 数据格式：结构化数据，支持数据库存储

- **数据来源**：
  - 环保部门公开数据
  - 空气质量监测网站
  - 第三方空气质量数据服务

### 3.3.3 用户数据需求

- **数据类型**：

  - 用户基本信息（用户名、密码散列值）
  - 用户偏好设置（默认城市、关注的指标等）
  - 用户操作日志（可选）

- **数据特性**：
  - 安全性：密码必须加密存储
  - 隐私性：遵循相关数据保护法规
  - 数据格式：结构化数据，支持数据库存储

### 3.3.4 系统运行数据需求

- **数据类型**：

  - 预测记录（预测时间、预测目标、使用模型、预测结果等）
  - 系统日志（用户访问、错误日志、性能指标等）
  - 模型评估数据（准确性指标、训练时间等）

- **数据特性**：
  - 时间粒度：根据操作实时记录
  - 存储期限：预测记录长期存储，日志可定期归档
  - 数据格式：结构化和半结构化数据，支持数据库和日志文件存储

## 3.4 用户角色定义

明确系统的用户角色及其权限，有助于设计针对性的功能和界面，提升用户体验。本系统主要定义了以下用户角色：

### 3.4.1 游客

未注册或未登录的系统访问者，具有有限的系统访问权限。

- **权限范围**：

  - 查看系统首页和基本介绍
  - 访问公开的历史数据查询（有限量）
  - 查看基本的数据可视化展示
  - 注册新用户账户

- **使用场景**：
  - 初次访问系统，了解系统功能
  - 进行简单的数据查询和浏览
  - 决定是否注册成为正式用户

### 3.4.2 普通用户

已注册并登录的系统用户，是系统的主要用户群体。

- **权限范围**：

  - 全部游客权限
  - 完整的历史数据查询和可视化功能
  - 使用预测分析功能，包括选择不同模型和时间范围
  - 查看和导出预测结果
  - 管理个人账户信息和偏好设置

- **使用场景**：
  - 日常查询气象和空气质量数据
  - 获取未来气象和空气质量预测
  - 分析历史数据趋势
  - 根据预测结果安排活动和出行

### 3.4.3 管理员

具有系统管理权限的特殊用户，负责系统维护和管理。

- **权限范围**：

  - 全部普通用户权限
  - 用户管理（查看、编辑、禁用用户账户）
  - 数据源管理（添加、编辑、启用/禁用数据源）
  - 模型管理（训练、更新、部署预测模型）
  - 系统监控（查看系统日志、性能指标）
  - 系统配置管理

- **使用场景**：
  - 系统日常维护和监控
  - 处理用户反馈和问题
  - 优化系统性能和预测准确性
  - 添加新功能或数据源

通过详细的需求分析，我们明确了系统应该具备的功能、性能特征、数据需求以及用户角色定义。这些需求将指导后续的系统设计和实现工作，确保最终系统能够满足用户的实际需求，提供高质量的气象和空气质量预测服务。

# 第四章 系统总体设计

系统总体设计是软件工程过程中的关键环节，它将需求分析的结果转化为具体的系统构架和实现方案。本章将从系统架构设计、数据库设计、功能模块设计和系统流程设计等方面，详细阐述气象分析与预测系统的总体设计方案。

## 4.1 系统架构设计

良好的系统架构是构建高质量软件系统的基础。本节将从总体架构、技术架构和部署架构三个维度详细介绍系统架构设计。

### 4.1.1 总体架构

本系统采用经典的三层架构设计，将系统划分为表示层、业务逻辑层和数据访问层，实现了系统各部分的解耦和职责分离。三层架构的具体设计如下：

**表示层（Presentation Layer）**：
表示层是系统与用户交互的界面，负责接收用户输入和展示系统输出。本系统的表示层主要包括：

- Web 前端界面：基于 HTML、CSS 和 JavaScript 构建的用户界面，包括各类页面和交互组件。
- 数据可视化组件：使用 ECharts 等库实现的各类图表和可视化展示。
- RESTful API 接口：供前端通过 AJAX 调用的后端接口，返回 JSON 格式的数据。

**业务逻辑层（Business Logic Layer）**：
业务逻辑层是系统的核心，负责实现系统的业务逻辑和功能处理。本系统的业务逻辑层主要包括：

- 数据处理模块：负责数据清洗、转换和特征工程。
- 预测模型模块：包括 LightGBM、LSTM/GRU、Prophet 等预测模型的训练和推理逻辑。
- 用户管理模块：处理用户注册、登录和权限验证等功能。
- 业务流程控制：协调各模块间的数据流转和处理流程。

**数据访问层（Data Access Layer）**：
数据访问层负责与数据源交互，提供数据的存取服务。本系统的数据访问层主要包括：

- 数据库访问模块：与 SQLite 数据库交互，执行数据查询和存储操作。
- 数据爬取模块：从网络爬取气象和空气质量数据。
- 文件系统交互：读写模型文件、配置文件等。

三层架构的优势在于各层之间通过明确的接口进行交互，实现了低耦合高内聚的设计原则，便于系统的维护和扩展。当需要修改某一层的实现时，只要接口保持一致，其他层的代码无需变更。

### 4.1.2 技术架构

技术架构描述了系统实现所采用的具体技术栈和框架。本系统的技术架构设计如下：

**前端技术架构**：

- 页面结构：HTML5
- 样式表现：CSS3，Bootstrap 框架
- 交互逻辑：JavaScript，jQuery 库
- 数据可视化：ECharts 库
- HTTP 请求：Axios 库
- 日期处理：Moment.js 库

**后端技术架构**：

- Web 框架：Flask
- 用户认证：Flask-Login
- 模板引擎：Jinja2
- 跨域支持：Flask-CORS
- 数据库 ORM：原生 SQLite API

**机器学习技术架构**：

- 特征工程：Pandas, NumPy
- 传统机器学习：LightGBM, Scikit-learn
- 深度学习：TensorFlow, Keras
- 时间序列预测：Prophet, ARIMA
- 模型序列化：Joblib, JSON

**数据存储架构**：

- 关系型数据库：SQLite
- 文件存储：模型文件(.pkl, .h5, .json)
- 环境变量：.env 文件

**开发和部署工具**：

- 开发语言：Python 3
- 依赖管理：pip, requirements.txt
- 版本控制：Git
- 日志管理：Python logging 模块
- 环境配置：python-dotenv

技术架构的选择兼顾了开发效率、系统性能和维护成本。Flask 框架的轻量级特性适合快速开发和部署，SQLite 数据库无需单独服务器即可运行，降低了系统部署的复杂度。同时，成熟的机器学习框架如 LightGBM、Keras 和 Prophet 提供了强大的预测能力，支持系统的核心功能。

### 4.1.3 部署架构

部署架构描述了系统在实际环境中的部署方式和组件分布。考虑到系统规模和资源限制，本系统采用单服务器部署架构，具体设计如下：

**服务器配置**：

- 操作系统：Linux/Windows Server
- Web 服务器：Waitress (Windows) / Gunicorn (Linux)
- 应用服务器：Flask 应用
- 数据库：SQLite
- 文件系统：本地存储

**部署流程**：

1. 安装 Python 环境和依赖包
2. 配置环境变量和系统参数
3. 初始化数据库
4. 训练并保存预测模型
5. 启动 Web 服务器
6. 配置定时任务，定期更新数据和模型

**扩展性考虑**：
虽然初期采用单服务器部署，但系统设计时已考虑未来的扩展需求。当用户量增加或需要处理更大规模的数据时，可以考虑以下扩展方案：

- 数据库分离：将 SQLite 替换为 MySQL 或 PostgreSQL，并部署在独立服务器上
- 负载均衡：引入 Nginx 实现多应用服务器的负载均衡
- 缓存引入：使用 Redis 缓存频繁访问的数据，提高系统响应速度
- 容器化部署：使用 Docker 容器化应用，便于水平扩展和管理

通过合理的部署架构设计，系统既能满足当前的运行需求，又为未来的扩展预留了空间。单服务器部署方案简化了系统维护，适合中小规模的应用场景。

## 4.2 数据库设计

数据库是系统的核心组成部分，负责存储和管理系统所需的各类数据。本节将从概念模型和逻辑模型两个层面介绍系统的数据库设计。

### 4.2.1 数据库概念模型

数据库概念模型使用实体-关系（E-R）图描述系统中的主要实体及其关系。本系统的主要实体包括：用户、城市、天气数据、空气质量数据和预测记录等。

**主要实体及属性**：

1. **用户实体（User）**

   - 属性：用户名、密码

2. **城市实体（City）**

   - 属性：城市名称

3. **天气数据实体（WeatherData）**

   - 属性：日期、城市、天气状况、温度范围、风力信息

4. **空气质量数据实体（AQIData）**

   - 属性：日期、城市、空气质量等级、AQI 指数、PM2.5、PM10、SO2、NO2、CO、O3

5. **预测记录实体（ForecastRecord）**
   - 属性：预测 ID、用户、城市、预测日期、目标指标、模型类型、预测结果

**实体间关系**：

1. **用户-预测记录关系**：一个用户可以有多个预测记录，一个预测记录只属于一个用户。关系类型：一对多。

2. **城市-天气数据关系**：一个城市有多条天气数据，一条天气数据只属于一个城市。关系类型：一对多。

3. **城市-空气质量数据关系**：一个城市有多条空气质量数据，一条空气质量数据只属于一个城市。关系类型：一对多。

4. **城市-预测记录关系**：一个城市可以有多个预测记录，一个预测记录只针对一个城市。关系类型：一对多。

5. **天气数据-空气质量数据关系**：一条天气数据可以关联一条同日期同城市的空气质量数据。关系类型：一对一。

### 4.2.2 数据库逻辑模型

数据库逻辑模型将概念模型转化为具体的数据表结构。本系统采用 SQLite 作为数据库管理系统，根据实体关系图设计了以下数据表：

**1. 用户表（user）**

用户表存储系统用户的基本信息，包括认证所需的用户名和密码。

| 字段名   | 数据类型 | 约束        | 说明           |
| -------- | -------- | ----------- | -------------- |
| name     | TEXT     | PRIMARY KEY | 用户名         |
| password | TEXT     | NOT NULL    | 密码（哈希值） |

**2. 天气数据表（weather_data）**

天气数据表存储从气象网站爬取的历史天气数据。

| 字段名            | 数据类型 | 约束         | 说明                     |
| ----------------- | -------- | ------------ | ------------------------ |
| city              | TEXT     | NOT NULL     | 城市名称                 |
| date              | TEXT     | NOT NULL     | 日期 (YYYY-MM-DD)        |
| weather_condition | TEXT     |              | 天气状况                 |
| temperature_range | TEXT     |              | 温度范围 (高温 ℃/低温 ℃) |
| wind_info         | TEXT     |              | 风力信息                 |
| PRIMARY KEY       |          | (city, date) | 城市和日期联合主键       |

**3. 空气质量数据表（aqi_data）**

空气质量数据表存储从空气质量监测网站爬取的历史空气质量数据。

| 字段名        | 数据类型 | 约束         | 说明               |
| ------------- | -------- | ------------ | ------------------ |
| city          | TEXT     | NOT NULL     | 城市名称           |
| date          | TEXT     | NOT NULL     | 日期 (YYYY-MM-DD)  |
| quality_level | TEXT     |              | 空气质量等级       |
| aqi_index     | INTEGER  |              | AQI 指数           |
| pm25          | REAL     |              | PM2.5 浓度         |
| pm10          | REAL     |              | PM10 浓度          |
| so2           | REAL     |              | SO2 浓度           |
| no2           | REAL     |              | NO2 浓度           |
| co            | REAL     |              | CO 浓度            |
| o3            | REAL     |              | O3 浓度            |
| PRIMARY KEY   |          | (city, date) | 城市和日期联合主键 |

**数据库索引设计**：

为提高查询效率，系统在关键字段上建立了索引：

1. **天气数据索引**：

   ```sql
   CREATE INDEX idx_weather_city_date ON weather_data (city, date);
   ```

2. **空气质量数据索引**：
   ```sql
   CREATE INDEX idx_aqi_city_date ON aqi_data (city, date);
   ```

通过合理的数据库设计，系统能够高效地存储和检索各类数据，为数据分析和预测模型提供可靠的数据源。SQLite 数据库的轻量级特性也使系统的部署和维护更加简便。

## 4.3 功能模块设计

根据系统需求分析，本系统被划分为多个相互协作的功能模块，每个模块负责实现特定的功能集合。本节将详细介绍各功能模块的职责和设计。

### 4.3.1 用户认证模块

用户认证模块负责系统的用户管理和认证功能，包括用户注册、登录和权限验证。

**核心组件**：

- **登录组件**：验证用户凭证，创建用户会话。
- **注册组件**：处理新用户注册，保存用户信息。
- **会话管理**：维护用户登录状态，处理会话过期。
- **权限控制**：根据用户角色控制功能访问权限。

**主要接口**：

- `/auth/login`：用户登录接口
- `/auth/register`：用户注册接口
- `/auth/logout`：用户退出接口

**技术实现**：

- 使用 Flask-Login 扩展实现用户认证和会话管理
- 密码使用安全哈希算法存储，防止明文泄露
- 使用装饰器（如`@login_required`）实现访问控制

### 4.3.2 数据采集模块

数据采集模块负责从各数据源获取气象和空气质量数据，并将数据存储到系统数据库中。

**核心组件**：

- **气象数据爬虫**：从气象网站爬取历史天气数据。
- **空气质量数据爬虫**：从空气质量监测网站爬取历史 AQI 数据。
- **数据解析器**：解析原始数据，提取结构化信息。
- **数据存储器**：将处理后的数据存入数据库。

**主要接口**：

- 命令行脚本：`weather_spider.py`和`aqi_spider.py`
- 数据库接口：`get_db()`函数提供数据库连接

**技术实现**：

- 使用 Requests 库和 BeautifulSoup 库实现网页爬取和解析
- 使用 SQLite 数据库存储结构化数据
- 设计定时任务，定期更新最新数据

### 4.3.3 数据处理模块

数据处理模块负责对原始数据进行清洗、转换和特征工程，为预测模型准备高质量的输入数据。

**核心组件**：

- **数据清洗器**：处理缺失值、异常值和数据格式问题。
- **特征工程器**：创建时间特征、滞后特征和滚动特征。
- **数据转换器**：将原始数据转换为模型可用的格式，如特征缩放和类别编码。
- **数据加载器**：从数据库加载数据并组织为 DataFrame 结构。

**主要接口**：

- `get_combined_data_from_db()`：获取合并后的天气和 AQI 数据
- `create_time_features()`：创建时间相关特征
- `create_lag_features()`：创建滞后特征
- `create_rolling_features()`：创建滚动统计特征

**技术实现**：

- 使用 Pandas 库进行数据处理和转换
- 使用 Numpy 库进行数值计算
- 使用 Scikit-learn 的数据预处理工具实现特征缩放和编码

### 4.3.4 模型训练模块

模型训练模块负责训练各类预测模型，包括参数调优、模型评估和模型保存。

**核心组件**：

- **数据分割器**：将数据集分为训练集和测试集。
- **模型训练器**：训练各类机器学习模型（LightGBM、LSTM/GRU、Prophet）。
- **模型评估器**：使用各种指标评估模型性能。
- **模型保存器**：将训练好的模型序列化保存。

**主要接口**：

- 命令行脚本：`train_models.py`
- 模型文件：保存在`trained_models`目录

**技术实现**：

- 使用 LightGBM、Keras 和 Prophet 库实现不同类型的预测模型
- 使用 Joblib 和 JSON 序列化模型参数和权重
- 使用 MAE、RMSE、Accuracy 等指标评估模型性能

### 4.3.5 预测服务模块

预测服务模块负责加载训练好的模型，接收预测请求，执行预测计算，并返回预测结果。

**核心组件**：

- **模型加载器**：加载已训练的模型到内存。
- **预测执行器**：使用模型生成预测结果。
- **结果格式化器**：将预测结果转换为适合展示的格式。
- **API 接口**：提供 REST 风格的预测服务接口。

**主要接口**：

- `/api/predict/temperature`：温度预测接口
- `/api/predict/aqi`：AQI 预测接口
- `/api/predict/pm25`：PM2.5 预测接口
- `/api/predict/weather`：天气状况预测接口

**技术实现**：

- 使用 Flask 蓝图组织 API 路由
- 使用预加载机制提高预测响应速度
- 提供多种预测模型选择

### 4.3.6 数据可视化模块

数据可视化模块负责将历史数据和预测结果以图表形式直观展示，帮助用户理解数据趋势和模式。

**核心组件**：

- **图表生成器**：根据数据特性生成不同类型的图表。
- **交互控制器**：处理用户与图表的交互事件。
- **数据转换器**：将后端数据转换为适合前端图表库的格式。
- **布局管理器**：管理多图表的布局和显示。

**主要接口**：

- `/api/data/history`：获取历史数据接口
- `/api/data/prediction`：获取预测数据接口
- 前端 JS 接口：ECharts 图表配置和更新函数

**技术实现**：

- 后端使用 Flask API 返回 JSON 格式的数据
- 前端使用 ECharts 库渲染各类图表
- 使用 AJAX 技术实现数据异步加载和图表更新

### 4.3.7 Web 界面模块

Web 界面模块负责构建用户友好的交互界面，整合各功能模块的输出，提供统一的用户体验。

**核心组件**：

- **页面渲染器**：使用模板引擎渲染 HTML 页面。
- **表单处理器**：处理用户输入和表单提交。
- **导航管理器**：管理系统的导航结构和页面跳转。
- **响应式布局**：适应不同屏幕尺寸的布局系统。

**主要接口**：

- `/`：系统首页
- `/dashboard`：数据分析仪表盘
- `/predict`：预测功能页面
- `/history`：历史数据查询页面

**技术实现**：

- 使用 Jinja2 模板引擎渲染动态 HTML
- 使用 Bootstrap 框架实现响应式布局
- 使用 jQuery 处理 DOM 操作和事件响应

## 4.4 系统流程设计

系统流程描述了系统中数据和控制的流转过程，是理解系统运行机制的关键。本节将介绍系统的两个核心流程：数据处理流程和用户交互流程。

### 4.4.1 数据处理流程

数据处理流程描述了从数据采集到预测结果生成的完整过程，是系统的核心业务流程。

**数据采集与存储流程**：

1. 爬虫程序从指定网站获取原始气象和空气质量数据
2. 数据解析器提取并结构化原始数据
3. 数据验证器检查数据完整性和有效性
4. 数据存储器将验证通过的数据存入数据库
5. 日志记录器记录数据采集过程和结果

**数据预处理与特征工程流程**：

1. 数据加载器从数据库加载原始数据
2. 数据清洗器处理缺失值和异常值
3. 数据转换器进行必要的数据转换（如温度解析）
4. 特征工程器创建时间特征、滞后特征和滚动特征
5. 数据分割器将数据集分为训练集和测试集

**模型训练与评估流程**：

1. 模型训练器使用训练集训练各类预测模型
2. 模型评估器使用测试集评估模型性能
3. 模型优化器根据评估结果调整模型参数
4. 重复训练-评估-优化循环，直到达到预期性能
5. 模型保存器将最终模型序列化保存

**预测执行流程**：

1. 接收用户预测请求（指定城市、预测指标、模型类型等）
2. 数据准备器加载并准备预测所需的历史数据
3. 模型加载器加载指定的预测模型
4. 预测执行器使用模型生成预测结果
5. 结果格式化器将原始预测结果转换为用户友好的格式
6. 返回预测结果并展示

### 4.4.2 用户交互流程

用户交互流程描述了用户与系统交互的过程，包括用户认证、数据查询和预测操作等。

**用户注册与登录流程**：

1. 用户访问系统首页
2. 用户选择注册（新用户）或登录（既有用户）
3. 用户提交注册/登录表单
4. 系统验证用户输入
5. 成功后创建用户会话，重定向到主界面
6. 错误时返回适当的错误信息，要求重新输入

**历史数据查询流程**：

1. 用户进入历史数据查询页面
2. 用户选择城市、日期范围和数据类型
3. 用户提交查询请求
4. 系统从数据库检索符合条件的数据
5. 系统将查询结果以图表和表格形式展示
6. 用户可以交互式地探索数据（如缩放图表、排序表格）

**预测操作流程**：

1. 用户进入预测功能页面
2. 用户选择城市、预测指标和模型类型
3. 用户提交预测请求
4. 系统执行预测计算
5. 系统将预测结果以图表形式展示
6. 用户可以查看不同时间范围的预测结果
7. 用户可以切换不同模型比较预测效果

**数据导出流程**：

1. 用户在数据查询或预测结果页面
2. 用户选择导出选项
3. 用户选择导出格式（如 CSV、Excel）
4. 系统生成相应格式的数据文件
5. 系统提供文件下载链接
6. 用户下载并保存文件

通过合理的系统总体设计，我们构建了一个结构清晰、功能完善的气象分析与预测系统。三层架构的设计实现了系统的模块化和低耦合，合理的数据库设计支持了高效的数据存储和检索，完善的功能模块划分明确了各部分的职责边界，清晰的系统流程设计确保了数据和控制的有序流转。这些设计为系统的实现提供了可靠的蓝图和指导。

# 第五章 数据获取与预处理

数据是机器学习系统的基础，高质量的数据直接影响预测模型的效果。本章将详细介绍气象分析与预测系统的数据获取方法、数据清洗策略、特征工程设计、数据标准化方法以及数据集划分策略。

## 5.1 数据源选择与获取

为了构建准确的气象和空气质量预测模型，系统需要获取充分的历史数据。经过调研和比较，我们选择了公开可访问的气象网站和空气质量监测网站作为数据源，采用爬虫技术定期获取数据。

### 5.1.1 数据源选择

在选择数据源时，我们主要考虑以下几个因素：

- 数据覆盖范围：能够覆盖目标城市
- 数据时间跨度：至少包含近三年的历史数据
- 数据更新频率：每日更新，保证数据时效性
- 数据可靠性：来源可信，数据准确
- 数据获取难度：能够通过爬虫技术获取

基于上述考虑，我们选择的主要数据源如下：

**气象数据源**：
我们选择了国内某知名天气网站作为气象数据的来源。该网站提供全国各主要城市的历史天气记录，数据更新及时，格式规范，包含我们所需的温度、天气状况和风力等关键信息。

**空气质量数据源**：
我们选择了国家环境监测站的公开数据作为空气质量数据的来源。该数据源提供了全国各主要城市的空气质量历史数据，包括 AQI 指数、PM2.5、PM10、臭氧等多种空气质量指标。

### 5.1.2 爬虫设计与实现

为了从选定的数据源获取数据，我们设计并实现了专门的爬虫程序。爬虫程序的主要功能包括网页请求、内容解析、数据提取和数据存储。

**爬虫架构设计**：
我们为气象数据和空气质量数据分别设计了独立的爬虫模块，但它们共享相似的架构：

1. 请求模块：负责发送 HTTP 请求，获取网页内容
2. 解析模块：负责解析 HTML 结构，提取目标数据
3. 存储模块：负责将提取的数据保存到数据库

**气象数据爬虫实现**：
气象数据爬虫（`weather_spider.py`）主要实现以下功能：

- 构建目标 URL，包含城市名和日期参数
- 发送 HTTP GET 请求，获取网页内容
- 使用 BeautifulSoup 解析 HTML，提取天气数据
- 将提取的数据（日期、城市、天气状况、温度范围、风力信息）保存到数据库

以下是气象数据爬虫的核心代码片段：

```python
def fetch_weather_data(city, date):
    """获取指定城市和日期的天气数据"""
    url = f"http://example.weather.com/history/{city}/{date}"
    response = requests.get(url, headers=HEADERS)
    if response.status_code != 200:
        logging.error(f"获取{city} {date}天气数据失败: {response.status_code}")
        return None

    soup = BeautifulSoup(response.text, 'html.parser')
    try:
        weather_condition = soup.select_one('.weather-condition').text.strip()
        temperature_range = soup.select_one('.temperature-range').text.strip()
        wind_info = soup.select_one('.wind-info').text.strip()

        return {
            'city': city,
            'date': date,
            'weather_condition': weather_condition,
            'temperature_range': temperature_range,
            'wind_info': wind_info
        }
    except Exception as e:
        logging.error(f"解析{city} {date}天气数据失败: {e}")
        return None
```

**空气质量数据爬虫实现**：
空气质量数据爬虫（`aqi_spider.py`）主要实现以下功能：

- 构建目标 URL，包含城市名和日期参数
- 发送 HTTP GET 请求，获取网页内容
- 使用 BeautifulSoup 解析 HTML，提取空气质量数据
- 将提取的数据（日期、城市、AQI 指数、PM2.5、PM10 等）保存到数据库

以下是空气质量数据爬虫的核心代码片段：

```python
def fetch_aqi_data(city, date):
    """获取指定城市和日期的空气质量数据"""
    url = f"http://example.aqi.com/city/{city}/date/{date}"
    response = requests.get(url, headers=HEADERS)
    if response.status_code != 200:
        logging.error(f"获取{city} {date}空气质量数据失败: {response.status_code}")
        return None

    soup = BeautifulSoup(response.text, 'html.parser')
    try:
        quality_level = soup.select_one('.quality-level').text.strip()
        aqi_index = int(soup.select_one('.aqi-index').text.strip())
        pm25 = float(soup.select_one('.pm25').text.strip())
        pm10 = float(soup.select_one('.pm10').text.strip())
        so2 = float(soup.select_one('.so2').text.strip())
        no2 = float(soup.select_one('.no2').text.strip())
        co = float(soup.select_one('.co').text.strip())
        o3 = float(soup.select_one('.o3').text.strip())

        return {
            'city': city,
            'date': date,
            'quality_level': quality_level,
            'aqi_index': aqi_index,
            'pm25': pm25,
            'pm10': pm10,
            'so2': so2,
            'no2': no2,
            'co': co,
            'o3': o3
        }
    except Exception as e:
        logging.error(f"解析{city} {date}空气质量数据失败: {e}")
        return None
```

### 5.1.3 数据获取流程

数据获取是一个持续的过程，我们设计了完整的数据获取流程，确保系统能够持续获取最新数据：

1. **初始数据获取**：系统首次部署时，爬虫程序会抓取过去三年的历史数据，建立初始数据集。
2. **定期更新**：系统设置定时任务，每天自动运行爬虫程序，获取前一天的最新数据。
3. **数据验证**：爬虫获取的数据会经过基本验证，确保格式正确、数值合理。
4. **数据入库**：验证通过的数据会被存入系统数据库，供后续使用。
5. **错误处理**：对于获取失败的数据，系统会记录日志并在下次运行时重试。

通过这一系列流程，系统能够保持数据的持续更新，为预测模型提供最新的训练和预测数据。

## 5.2 数据清洗策略

从网络爬取的原始数据往往存在各种质量问题，如缺失值、异常值、格式不一致等。为了保证数据质量，我们设计了一系列数据清洗策略。

### 5.2.1 缺失值处理

数据缺失是常见的问题，特别是在网络爬取的数据中。针对不同类型的缺失值，我们采用了不同的处理策略：

**结构性缺失**：
对于某些城市在特定日期完全没有数据的情况，我们采用以下策略：

- 如果是连续多天缺失，且缺失天数少于 7 天，使用线性插值填充
- 如果是单天缺失，使用前后两天的平均值填充
- 如果是长期缺失（超过 7 天），则保留缺失，在后续模型训练时通过滑动窗口跳过这些区域

**部分字段缺失**：
对于记录存在但部分字段缺失的情况，我们根据字段的重要性和缺失特点采用不同策略：

- 温度数据缺失：使用当天临近城市的温度数据进行估计
- 天气状况缺失：使用当天的温度和风力信息推断可能的天气状况
- AQI 相关指标缺失：如果 AQI 指数存在但具体污染物浓度缺失，使用 AQI 与污染物浓度的经验关系进行估计
- 风力信息缺失：使用默认值（如"微风"）填充

以下是处理缺失值的代码示例：

```python
def handle_missing_values(df):
    """处理数据中的缺失值"""
    # 处理温度缺失
    if 'temperature_range' in df.columns and df['temperature_range'].isnull().any():
        # 使用前后日期的平均值填充单日缺失
        df['temperature_range'] = df['temperature_range'].fillna(method='ffill').fillna(method='bfill')

    # 处理天气状况缺失
    if 'weather_condition' in df.columns and df['weather_condition'].isnull().any():
        # 使用最常见的天气状况填充
        most_common = df['weather_condition'].mode()[0]
        df['weather_condition'] = df['weather_condition'].fillna(most_common)

    # 处理AQI相关指标缺失
    numeric_cols = ['aqi_index', 'pm25', 'pm10', 'so2', 'no2', 'co', 'o3']
    for col in numeric_cols:
        if col in df.columns and df[col].isnull().any():
            # 使用中位数填充数值型缺失值
            median_value = df[col].median()
            df[col] = df[col].fillna(median_value)

    return df
```

### 5.2.2 异常值处理

异常值会对数据分析和模型训练产生不良影响。我们采用统计方法检测和处理异常值。

**异常值检测**：

- 使用 3-sigma 规则：将超出均值 ±3 倍标准差范围的值视为潜在异常值
- 使用箱线图方法：将超出 Q1-1.5IQR 或 Q3+1.5IQR 的值视为潜在异常值（其中 Q1 为第一四分位数，Q3 为第三四分位数，IQR 为四分位距）
- 使用领域知识：基于气象和空气质量的专业知识设置合理阈值，如温度范围、AQI 指数范围等

**异常值处理**：

- 轻度异常：使用上/下限值替换（Winsorization）
- 严重异常：标记为缺失值，然后使用缺失值处理策略填充
- 可解释异常：如特殊天气事件导致的异常值，保留并添加标记

以下是处理异常值的代码示例：

```python
def handle_outliers(df, column, method='sigma'):
    """处理指定列的异常值"""
    if method == 'sigma':
        # 使用3-sigma规则
        mean = df[column].mean()
        std = df[column].std()
        lower_bound = mean - 3 * std
        upper_bound = mean + 3 * std
    elif method == 'iqr':
        # 使用箱线图方法
        q1 = df[column].quantile(0.25)
        q3 = df[column].quantile(0.75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
    else:
        raise ValueError(f"不支持的异常值处理方法: {method}")

    # 记录处理前的异常值数量
    outliers_count = ((df[column] < lower_bound) | (df[column] > upper_bound)).sum()
    logging.info(f"列 {column} 中检测到 {outliers_count} 个异常值")

    # 使用上/下限值替换异常值
    df[column] = df[column].clip(lower=lower_bound, upper=upper_bound)

    return df
```

### 5.2.3 格式标准化

原始数据中的格式不一致问题也需要处理，主要包括日期格式、温度表示和文本描述等方面的标准化。

**日期格式标准化**：
所有日期统一转换为"YYYY-MM-DD"格式，便于数据库存储和查询。

**温度格式标准化**：
原始温度数据可能以多种格式出现，如"25℃/18℃"、"25/18℃"或单个温度值。我们设计了专门的解析函数，将各种格式的温度数据解析为标准的高温、低温和平均温度值。

**天气状况标准化**：
原始天气状况描述可能存在多种表达方式，如"晴间多云"、"多云转晴"等。我们建立了映射表，将各种表达统一到标准的天气类别（如"晴"、"多云"、"阴"、"雨"等）。

**空气质量等级标准化**：
不同数据源可能使用不同的空气质量等级表示方法，我们将其统一为标准的六级分类（优、良、轻度污染、中度污染、重度污染、严重污染）。

以下是温度格式标准化的代码示例：

```python
def parse_temperature(temp_range_str):
    """解析各种格式的温度数据，返回平均温度、最高温度和最低温度"""
    high_temp, low_temp, avg_temp = np.nan, np.nan, np.nan

    try:
        if isinstance(temp_range_str, str):
            if '/' in temp_range_str:
                parts = temp_range_str.split('/')
                high_str = parts[0].replace('℃', '').strip()
                low_str = parts[1].replace('℃', '').strip()
                try:
                    high_temp = float(high_str) if high_str else np.nan
                except ValueError:
                    pass
                try:
                    low_temp = float(low_str) if low_str else np.nan
                except ValueError:
                    pass

                # 处理只有一个温度值的情况
                if np.isnan(high_temp) and not np.isnan(low_temp):
                    high_temp = low_temp
                if not np.isnan(high_temp) and np.isnan(low_temp):
                    low_temp = high_temp
            elif '℃' in temp_range_str:
                try:
                    single_temp = float(temp_range_str.replace('℃', '').strip())
                    high_temp = single_temp
                    low_temp = single_temp
                except ValueError:
                    pass

            # 计算平均温度
            if not np.isnan(high_temp) and not np.isnan(low_temp):
                avg_temp = (high_temp + low_temp) / 2.0

    except Exception as e:
        logging.warning(f"解析温度时出错 '{temp_range_str}': {e}")

    return avg_temp, high_temp, low_temp
```

## 5.3 特征工程设计

特征工程是机器学习中至关重要的环节，好的特征能够显著提升模型性能。针对气象和空气质量预测任务，我们设计了一系列特征工程方法。

### 5.3.1 时间特征构建

时间特征能够捕捉气象和空气质量数据中的周期性和季节性模式。我们从日期数据中提取了以下时间特征：

**基本时间特征**：

- 月份（month）：捕捉年内季节变化
- 日期（day）：捕捉月内变化
- 星期几（dayofweek）：捕捉周内变化模式
- 一年中的第几天（dayofyear）：捕捉年度循环模式
- 一年中的第几周（weekofyear）：捕捉中期变化模式
- 季度（quarter）：捕捉季节性变化

**周期性时间特征**：
为了更好地表达时间的周期性，我们对部分时间特征进行了周期性变换：

- 月份正弦变换：sin(2π × month / 12)
- 月份余弦变换：cos(2π × month / 12)
- 星期几正弦变换：sin(2π × dayofweek / 7)
- 星期几余弦变换：cos(2π × dayofweek / 7)

这种变换使得时间特征在周期首尾相连，更好地表达时间的循环性质。

以下是构建时间特征的代码示例：

```python
def create_time_features(df):
    """从日期列创建时间特征"""
    if 'date' not in df.columns or not pd.api.types.is_datetime64_any_dtype(df['date']):
        logging.error("输入DataFrame缺少'date'列或类型不正确")
        return df

    df_copy = df.copy()

    # 基本时间特征
    df_copy['month'] = df_copy['date'].dt.month
    df_copy['day'] = df_copy['date'].dt.day
    df_copy['dayofweek'] = df_copy['date'].dt.dayofweek
    df_copy['dayofyear'] = df_copy['date'].dt.dayofyear
    df_copy['weekofyear'] = df_copy['date'].dt.isocalendar().week.astype(int)
    df_copy['quarter'] = df_copy['date'].dt.quarter

    # 周期性时间特征
    df_copy['month_sin'] = np.sin(2 * np.pi * df_copy['month'] / 12)
    df_copy['month_cos'] = np.cos(2 * np.pi * df_copy['month'] / 12)
    df_copy['dayofweek_sin'] = np.sin(2 * np.pi * df_copy['dayofweek'] / 7)
    df_copy['dayofweek_cos'] = np.cos(2 * np.pi * df_copy['dayofweek'] / 7)

    logging.info("时间特征已创建")
    return df_copy
```

### 5.3.2 滞后特征构建

滞后特征（Lag Features）是时间序列预测中常用的特征类型，它使用过去的观测值作为当前预测的输入特征。考虑到气象和空气质量数据的时间依赖性，我们为关键指标构建了多种滞后特征：

**单日滞后特征**：
为每个关键指标（平均温度、AQI 指数、PM2.5、臭氧等）创建 1 天、2 天、3 天前的滞后值，捕捉短期时间依赖关系。

**多日滞后特征**：
创建 7 天和 14 天前的滞后值，捕捉周期性和中长期时间依赖关系。

**组合滞后特征**：
对于某些指标，还创建了多个滞后特征之间的差值和比值，如温度变化率（今天温度与昨天温度的差值）。

以下是构建滞后特征的代码示例：

```python
def create_lag_features(df, target_cols, lag_days):
    """为指定的列创建滞后特征"""
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by='date')  # 确保按日期排序

    for col in target_cols:
        if col not in df_copy.columns:
            logging.warning(f"列'{col}'不在DataFrame中，跳过创建滞后特征")
            continue

        for lag in lag_days:
            df_copy[f'{col}_lag_{lag}'] = df_copy[col].shift(lag)

            # 对于某些指标，创建差值特征
            if lag == 1 and col in ['avg_temp', 'aqi_index', 'pm25', 'o3']:
                df_copy[f'{col}_diff_1'] = df_copy[col] - df_copy[f'{col}_lag_1']

    logging.info(f"为列{target_cols}创建了滞后天数{lag_days}的特征")
    return df_copy
```

### 5.3.3 滚动特征构建

滚动特征（Rolling Features）是对过去一段时间窗口内数据的统计汇总，能够捕捉时间序列的局部趋势和波动特性。对于气象和空气质量数据，滚动特征有助于平滑数据噪声并捕捉中期趋势。

我们为关键指标构建了以下滚动特征：

**基本统计量**：

- 滚动均值：过去 n 天的平均值，反映中期趋势
- 滚动标准差：过去 n 天的标准差，反映波动程度
- 滚动最大值和最小值：过去 n 天的极值，反映波动范围

**窗口大小**：
我们选择了 3 天、7 天和 14 天三种窗口大小，分别捕捉短期、周期和中期变化模式。

**关键指标**：
为平均温度、AQI 指数、PM2.5 和臭氧等关键指标创建滚动特征，这些指标的历史走势对未来预测尤为重要。

以下是构建滚动特征的代码示例：

```python
def create_rolling_features(df, target_cols, windows):
    """为指定的列创建滚动统计特征"""
    df_copy = df.copy()
    df_copy = df_copy.sort_values(by='date')  # 确保按日期排序

    for col in target_cols:
        if col not in df_copy.columns:
            logging.warning(f"列'{col}'不在DataFrame中，跳过创建滚动特征")
            continue

        for window in windows:
            # 使用shift(1)确保只使用过去的数据，避免数据泄露
            rolling_obj = df_copy[col].shift(1).rolling(window=window, min_periods=1, closed='left')

            # 创建多种滚动统计特征
            df_copy[f'{col}_roll_mean_{window}'] = rolling_obj.mean()
            df_copy[f'{col}_roll_std_{window}'] = rolling_obj.std()
            df_copy[f'{col}_roll_min_{window}'] = rolling_obj.min()
            df_copy[f'{col}_roll_max_{window}'] = rolling_obj.max()

    logging.info(f"为列{target_cols}创建了滚动窗口{windows}的统计特征")
    return df_copy
```

### 5.3.4 特征交互与组合

除了基本的时间特征、滞后特征和滚动特征外，我们还设计了一些特征交互和组合，以捕捉不同变量之间的相互作用关系。

**交叉特征**：

- 温度与湿度的乘积：影响体感温度
- AQI 与温度的交互：温度可能影响某些污染物的形成和扩散

**组合特征**：

- 天气-温度组合特征：将天气状况和温度范围组合，如"晴热"、"雨冷"等
- 污染物比值：如 PM2.5/PM10 比值，反映细颗粒物占比
- 复合污染指数：多种污染物的加权组合

这些交互特征和组合特征能够为模型提供更丰富的信息，帮助捕捉变量间的非线性关系。

## 5.4 数据标准化方法

标准化是机器学习中常用的数据预处理技术，它将数据转换到特定的范围，有助于提高模型训练的效率和效果。针对不同类型的特征和不同的预测模型，我们采用了不同的标准化方法。

### 5.4.1 数值特征标准化

对于温度、AQI 指数、PM2.5 等数值特征，我们主要采用以下标准化方法：

**Min-Max 缩放**：
将数据缩放到[0, 1]范围内，适用于 LightGBM 等对特征尺度不敏感的模型。计算公式为：
$$X_{scaled} = \frac{X - X_{min}}{X_{max} - X_{min}}$$

**Z-Score 标准化**：
将数据转换为均值为 0、标准差为 1 的分布，适用于 LSTM 等对特征尺度敏感的模型。计算公式为：
$$X_{scaled} = \frac{X - \mu}{\sigma}$$

我们为每种预测目标（温度、AQI、PM2.5、臭氧）单独训练标准化器，确保每个目标的数据分布得到最合适的处理。

以下是实现数值特征标准化的代码示例：

```python
from sklearn.preprocessing import MinMaxScaler, StandardScaler

def standardize_data(train_data, test_data, columns, method='minmax'):
    """
    标准化训练数据和测试数据的指定列

    参数:
        train_data: 训练数据DataFrame
        test_data: 测试数据DataFrame
        columns: 需要标准化的列名列表
        method: 标准化方法，'minmax'或'zscore'

    返回:
        标准化后的训练数据、测试数据和标准化器字典
    """
    scalers = {}
    train_scaled = train_data.copy()
    test_scaled = test_data.copy()

    for col in columns:
        if col not in train_data.columns or col not in test_data.columns:
            logging.warning(f"列'{col}'不在数据集中，跳过标准化")
            continue

        if method == 'minmax':
            scaler = MinMaxScaler()
        elif method == 'zscore':
            scaler = StandardScaler()
        else:
            raise ValueError(f"不支持的标准化方法: {method}")

        # 使用训练数据拟合标准化器
        scaler.fit(train_data[col].values.reshape(-1, 1))

        # 转换训练数据和测试数据
        train_scaled[col] = scaler.transform(train_data[col].values.reshape(-1, 1)).flatten()
        test_scaled[col] = scaler.transform(test_data[col].values.reshape(-1, 1)).flatten()

        # 保存标准化器
        scalers[col] = scaler

    return train_scaled, test_scaled, scalers
```

### 5.4.2 类别特征编码

对于天气状况等类别特征，我们采用了适当的编码方法，将文本类别转换为模型可用的数值形式。

**标签编码（Label Encoding）**：
将天气类别（如"晴"、"多云"、"阴"、"雨"）映射为整数（如 0、1、2、3）。这种编码方法保持了类别的顺序性，适用于分类模型。

**独热编码（One-Hot Encoding）**：
将每个类别转换为独立的二进制特征，形成稀疏矩阵。这种编码方法消除了类别之间的人为顺序关系，适用于某些模型。在我们的系统中，由于天气类别较多，主要使用标签编码避免特征维度过高。

以下是类别特征编码的代码示例：

```python
from sklearn.preprocessing import LabelEncoder

def encode_categorical_features(train_data, test_data, columns):
    """
    对训练数据和测试数据的类别特征进行编码

    参数:
        train_data: 训练数据DataFrame
        test_data: 测试数据DataFrame
        columns: 需要编码的类别特征列名列表

    返回:
        编码后的训练数据、测试数据和编码器字典
    """
    encoders = {}
    train_encoded = train_data.copy()
    test_encoded = test_data.copy()

    for col in columns:
        if col not in train_data.columns or col not in test_data.columns:
            logging.warning(f"列'{col}'不在数据集中，跳过编码")
            continue

        encoder = LabelEncoder()

        # 使用训练数据和测试数据的并集拟合编码器，确保覆盖所有可能的类别
        unique_values = pd.concat([train_data[col], test_data[col]]).unique()
        encoder.fit(unique_values)

        # 转换训练数据和测试数据
        train_encoded[col + '_encoded'] = encoder.transform(train_data[col])
        test_encoded[col + '_encoded'] = encoder.transform(test_data[col])

        # 保存编码器
        encoders[col] = encoder

        # 打印编码映射关系
        mapping = dict(zip(encoder.classes_, range(len(encoder.classes_))))
        logging.info(f"列'{col}'的编码映射: {mapping}")

    return train_encoded, test_encoded, encoders
```

## 5.5 数据集划分策略

机器学习模型的训练和评估需要合理划分数据集。考虑到时间序列数据的特殊性，我们采用了时间顺序划分的策略，而非传统的随机划分。

### 5.5.1 时间顺序划分原则

我们按照时间顺序将数据集划分为训练集、验证集和测试集，这种划分方式符合时间序列数据的实际应用场景，能够更准确地评估模型在未来时间点的预测性能。

具体的划分比例为：

- 训练集：前 70%的数据
- 验证集：中间 15%的数据
- 测试集：最后 15%的数据

这种划分保证了模型只使用过去的数据预测未来，避免了数据泄露问题。

### 5.5.2 特殊处理考虑

在划分数据集时，我们还考虑了以下特殊情况：

**特征工程的影响**：
由于创建了滞后特征和滚动特征，数据集开头会有一些缺失值。我们在划分数据集前先进行特征工程，然后去除包含缺失值的行，确保训练数据的完整性。

**季节性考虑**：
为了确保训练集和测试集包含各种季节的数据，我们确保数据跨越至少两年的时间，这样训练集和测试集都能包含所有季节的模式。

**城市独立划分**：
不同城市的数据集单独划分和处理，避免城市间差异对模型训练的影响。对于多城市预测任务，我们会在模型中加入城市特征，或训练城市特定的模型。

以下是数据集划分的代码示例：

```python
def time_based_split(df, train_ratio=0.7, val_ratio=0.15):
    """
    基于时间顺序划分数据集为训练集、验证集和测试集

    参数:
        df: 输入DataFrame，假设已按日期排序
        train_ratio: 训练集占比
        val_ratio: 验证集占比，测试集占比为1-train_ratio-val_ratio

    返回:
        训练集、验证集和测试集
    """
    n_total = len(df)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * val_ratio)

    train_df = df.iloc[:n_train].copy()
    val_df = df.iloc[n_train:n_train+n_val].copy()
    test_df = df.iloc[n_train+n_val:].copy()

    logging.info(f"数据集划分: 训练集 {len(train_df)} 条, "
                f"验证集 {len(val_df)} 条, "
                f"测试集 {len(test_df)} 条")

    # 检查时间范围，确保季节覆盖
    logging.info(f"训练集时间范围: {train_df['date'].min()} 到 {train_df['date'].max()}")
    logging.info(f"验证集时间范围: {val_df['date'].min()} 到 {val_df['date'].max()}")
    logging.info(f"测试集时间范围: {test_df['date'].min()} 到 {test_df['date'].max()}")

    return train_df, val_df, test_df
```

通过以上数据获取与预处理步骤，我们构建了一个完整的数据处理流程。从原始数据的爬取，到数据清洗、特征工程和标准化，再到数据集划分，每一步都针对气象和空气质量数据的特点进行了精心设计。这些高质量的数据为后续的模型训练和预测分析提供了坚实的基础。

# 第六章 预测模型设计与实现

预测模型是气象分析与预测系统的核心部分，直接决定了系统预测的准确性和可靠性。本章将详细介绍系统中采用的各类预测模型的选择原理、训练流程、评估指标以及优化策略，为系统的核心功能提供技术支撑。

## 6.1 模型选择与原理

针对气象和空气质量预测的特点，我们经过充分调研和实验，选择了 LightGBM、LSTM/GRU 和 Prophet 三类模型作为系统的核心预测引擎。这些模型各有优势，适用于不同的预测任务和场景。

### 6.1.1 LightGBM 模型

LightGBM 是微软开发的高效梯度提升决策树框架，具有训练速度快、内存占用低、预测精度高等优点。我们选择 LightGBM 作为主要预测模型的原因包括：

**技术原理**：
LightGBM 基于梯度提升决策树（GBDT）算法，通过构建一系列决策树，每棵树学习前一棵树的残差，最终将所有树的预测结果累加得到最终预测。LightGBM 在传统 GBDT 的基础上引入了两项关键创新：基于直方图的决策树算法和带深度限制的叶子优先生长策略。

基于直方图的算法将连续特征值离散化为 k 个箱子，使用这些箱子构建特征直方图，大幅减少了内存消耗和计算量。叶子优先生长策略则是在树的生长过程中，每次选择具有最大增益的叶子节点进行分裂，而非传统的按层生长，这种策略能够更快地收敛到最优解。

**适用场景**：
LightGBM 特别适合处理结构化表格数据，能够高效利用时间特征、滞后特征和滚动特征等，捕捉变量之间的复杂非线性关系。在我们的系统中，LightGBM 被用于：

- 温度预测：利用历史温度和气象数据预测未来温度
- AQI 指数预测：预测未来的空气质量指数
- PM2.5 浓度预测：预测未来的 PM2.5 浓度水平
- 臭氧浓度预测：预测未来的臭氧浓度
- 天气状况分类：将未来天气状况分类为不同类别（晴、多云、阴、雨等）

**优势与限制**：
LightGBM 的主要优势包括：

- 训练速度快，特别是在处理大量特征和数据时
- 支持类别特征的直接输入，无需独热编码
- 内存占用低，适合在普通硬件上运行
- 预测精度高，尤其是在结构化数据上

其主要限制包括：

- 对时序数据的长期依赖关系建模能力相对有限
- 需要手动设计大量特征（如滞后特征、滚动特征）
- 模型不直观，难以解释预测结果的具体原因

### 6.1.2 LSTM/GRU 模型

长短期记忆网络（LSTM）和门控循环单元（GRU）是递归神经网络（RNN）的两种变体，专门设计用于处理序列数据和时间序列预测。我们选择这两种模型的原因包括：

**技术原理**：
传统 RNN 在处理长序列时会面临梯度消失或爆炸问题，导致无法学习长期依赖关系。LSTM 通过引入记忆单元（memory cell）和三种门控机制（输入门、遗忘门和输出门）解决了这一问题，能够有选择地记忆和遗忘信息，学习长期依赖关系。

LSTM 单元的核心是细胞状态（cell state），它像一条传送带一样贯穿整个序列。通过门控机制，LSTM 可以向细胞状态添加信息或从中删除信息：

- 遗忘门：决定从细胞状态中丢弃哪些信息
- 输入门：决定更新哪些新信息到细胞状态
- 输出门：决定基于细胞状态输出哪些信息

GRU 是 LSTM 的简化版本，它合并了 LSTM 的遗忘门和输入门为一个更新门（update gate），并添加了一个重置门（reset gate）。这种简化减少了参数数量，加快了训练速度，同时在许多任务上保持了与 LSTM 相当的性能。

**适用场景**：
LSTM/GRU 特别适合处理具有长期依赖关系的时间序列数据，无需显式设计滞后特征，能够自动学习序列中的时间模式。在我们的系统中：

- LSTM 用于温度、AQI 指数、PM2.5 和臭氧的回归预测
- GRU 用于天气状况的分类预测

**优势与限制**：
LSTM/GRU 的主要优势包括：

- 自动学习序列数据的时间依赖关系，无需手动设计滞后特征
- 能够捕捉长期和短期的时间模式
- 适应性强，能处理不同长度的输入序列
- 在处理非线性时间关系时表现优秀

其主要限制包括：

- 训练时间长，计算资源需求高
- 需要较大的训练数据集才能充分发挥性能
- 调参复杂，最优网络结构不易确定
- 模型为黑盒，可解释性较差

### 6.1.3 Prophet 模型

Prophet 是 Facebook 开发的时间序列预测框架，专为处理具有强烈季节性和多个季节性的业务时间序列而设计。我们选择 Prophet 的原因包括：

**技术原理**：
Prophet 基于分解思想，将时间序列分解为三个主要组成部分：趋势（trend）、季节性（seasonality）和假日效应（holidays）。其数学模型可表示为：

$$y(t) = g(t) + s(t) + h(t) + \epsilon_t$$

其中，$g(t)$表示趋势项，可以是线性或逻辑增长曲线；$s(t)$表示季节性，包括每周、每年等周期性变化；$h(t)$表示假日效应；$\epsilon_t$表示误差项。

Prophet 的建模过程采用贝叶斯方法，使用 MCMC（马尔可夫链蒙特卡洛）抽样来拟合参数，能够提供预测的不确定性估计。此外，Prophet 能够自动检测时间序列中的变点（changepoints），处理趋势变化。

**适用场景**：
Prophet 特别适合具有强烈季节性和多种季节性模式的时间序列，如日内模式、周内模式、年内模式等。在我们的系统中，Prophet 主要用于：

- 温度预测：捕捉温度的季节性变化
- AQI 指数预测：识别空气质量的周期性模式
- PM2.5 和臭氧预测：建模污染物浓度的季节性变化

**优势与限制**：
Prophet 的主要优势包括：

- 自动处理季节性和趋势变化，无需手动分解
- 对缺失数据和异常值具有较强的鲁棒性
- 能同时建模多个季节性效应（日、周、月、年）
- 提供预测区间，量化预测的不确定性
- 使用简单，无需深厚的时间序列分析背景

其主要限制包括：

- 对于多变量预测支持有限，主要适用于单变量时间序列
- 处理非线性关系的能力不如深度学习模型
- 预测远期未来时准确性可能下降
- 计算资源消耗较大，尤其是在大数据集上

### 6.1.4 模型选择策略

基于上述模型的特点和适用场景，我们制定了以下模型选择策略：

**回归任务（温度、AQI、PM2.5、臭氧）**：

1. 短期预测（1-3 天）：优先使用 LightGBM，结合丰富的特征工程
2. 中期预测（4-7 天）：组合使用 LightGBM 和 LSTM，取平均值或加权平均
3. 长期预测（8-15 天）：优先使用 Prophet，捕捉季节性模式

**分类任务（天气状况）**：

1. 短期预测（1-3 天）：优先使用 LightGBM，准确率通常最高
2. 中长期预测（4-15 天）：使用 GRU，更好地捕捉天气变化序列模式

此外，系统还提供了模型选择接口，允许用户自行选择使用哪种模型进行预测，以满足不同场景的需求。

## 6.2 模型训练流程

模型训练是将准备好的数据转化为可用于预测的模型的过程。本节将详细介绍各类模型的训练流程、参数设置和实现细节。

### 6.2.1 数据准备

在模型训练前，需要进行一系列数据准备工作，确保输入数据的质量和格式符合模型要求。

**数据加载与合并**：
首先，从数据库中加载经过清洗的气象数据和空气质量数据，并按城市和日期进行合并。代码示例如下：

```python
def get_combined_data_from_db(city, start_date=None, end_date=None):
    """从数据库获取合并后的气象和空气质量数据"""
    db_path = 'data.db'
    db = sqlite3.connect(db_path)
    db.row_factory = sqlite3.Row

    query = """
    SELECT w.date, w.city, w.weather_condition, w.temperature_range, w.wind_info,
           a.aqi_index, a.pm25, a.pm10, a.so2, a.no2, a.co, a.o3, a.quality_level
    FROM weather_data w LEFT JOIN aqi_data a
    ON w.city = a.city AND w.date = a.date
    WHERE w.city = ? """

    params = [city]
    if start_date:
        query += " AND w.date >= ?"
        params.append(start_date)
    if end_date:
        query += " AND w.date <= ?"
        params.append(end_date)

    query += " ORDER BY w.date ASC;"

    cursor = db.cursor()
    cursor.execute(query, tuple(params))
    results = cursor.fetchall()

    df = pd.DataFrame(results, columns=[desc[0] for desc in cursor.description])
    db.close()

    return df
```

**特征工程应用**：
接下来，对加载的数据应用本文第五章介绍的特征工程方法，包括时间特征、滞后特征和滚动特征的创建。这一步骤为模型提供了丰富的预测信息。

**数据分割**：
按照时间顺序将数据分割为训练集、验证集和测试集，通常采用 70%/15%/15%的比例。这种时间序列分割方式确保了模型评估的有效性。

**数据标准化**：
对数值特征进行标准化处理，对类别特征进行编码。不同模型可能需要不同的标准化方法：LSTM/GRU 通常需要 Z-Score 标准化，而 LightGBM 对标准化不敏感，可以使用原始数据或 Min-Max 缩放后的数据。

### 6.2.2 LightGBM 模型训练

LightGBM 模型的训练流程包括参数设置、模型拟合、交叉验证和模型保存等步骤。

**参数设置**：
针对不同的预测任务，我们设置了不同的 LightGBM 参数。以下是一些核心参数示例：

```python
# 回归任务参数设置（用于温度、AQI、PM2.5、臭氧预测）
LGBM_PARAMS_REGRESSION = {
    'objective': 'regression_l1',    # MAE损失函数
    'metric': 'mae',                 # 评估指标
    'n_estimators': 1000,            # 树的数量
    'learning_rate': 0.05,           # 学习率
    'feature_fraction': 0.8,         # 每次迭代随机选择80%的特征
    'bagging_fraction': 0.8,         # 每次迭代随机选择80%的数据
    'bagging_freq': 1,               # 每次迭代执行bagging
    'lambda_l1': 0.1,                # L1正则化
    'lambda_l2': 0.1,                # L2正则化
    'num_leaves': 31,                # 每棵树的最大叶子数
    'verbose': -1,                   # 静默模式
    'n_jobs': -1,                    # 使用所有CPU核心
    'seed': 42,                      # 随机种子
    'boosting_type': 'gbdt',         # 提升类型
}

# 分类任务参数设置（用于天气状况预测）
LGBM_PARAMS_CLASSIFICATION = {
    'objective': 'multiclass',       # 多分类目标
    'metric': 'multi_logloss',       # 评估指标
    'num_class': 10,                 # 类别数量（根据天气类别动态调整）
    'n_estimators': 1000,
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 1,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'num_leaves': 31,
    'verbose': -1,
    'n_jobs': -1,
    'seed': 42,
    'boosting_type': 'gbdt',
}
```

**模型训练**：
使用准备好的训练数据拟合 LightGBM 模型。为了防止过拟合，我们使用早停策略，在验证集性能不再提升时停止训练。代码示例如下：

```python
def train_lightgbm_model(X_train, y_train, X_val, y_val, params, is_classifier=False):
    """训练LightGBM模型"""
    # 创建数据集
    train_data = lgb.Dataset(X_train, label=y_train)
    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

    # 设置早停参数
    callbacks = [
        lgb.early_stopping(stopping_rounds=50, verbose=False),
        lgb.log_evaluation(period=100, show_stdv=True)
    ]

    # 训练模型
    model = lgb.train(
        params,
        train_data,
        valid_sets=[train_data, val_data],
        callbacks=callbacks,
        num_boost_round=params.get('n_estimators', 1000)
    )

    # 输出特征重要性
    importance = model.feature_importance(importance_type='gain')
    feature_names = model.feature_name()
    feature_importance = sorted(zip(feature_names, importance), key=lambda x: x[1], reverse=True)

    print("Top 10 important features:")
    for feature, importance in feature_importance[:10]:
        print(f"{feature}: {importance}")

    return model
```

**模型评估与调参**：
使用交叉验证方法评估模型性能，并通过网格搜索或贝叶斯优化方法调整参数。关键参数包括学习率、树的数量、叶子数量、正则化参数等。

**模型保存**：
将训练好的模型序列化保存，以便后续加载使用。我们使用 Joblib 库保存 LightGBM 模型，示例代码如下：

```python
def save_lightgbm_model(model, target_name, model_dir='trained_models'):
    """保存LightGBM模型"""
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_path = os.path.join(model_dir, f'lgbm_{target_name}_model.pkl')
    joblib.dump(model, model_path)
    print(f"LightGBM模型已保存到: {model_path}")
```

### 6.2.3 LSTM/GRU 模型训练

LSTM/GRU 等深度学习模型的训练流程与传统机器学习模型有所不同，包括数据序列化、网络构建、模型编译和训练等步骤。

**数据序列化**：
将时间序列数据转换为 LSTM/GRU 可用的序列格式，即创建固定长度的输入序列和对应的目标值。代码示例如下：

```python
def create_dataset_lstm_gru(data, look_back=15):
    """为LSTM/GRU创建序列数据集"""
    X, y = [], []
    if len(data) <= look_back:
        logging.warning(f"数据长度 ({len(data)}) 不足以创建 look_back={look_back} 的序列。")
        return np.array(X), np.array(y)

    # 生成样本
    for i in range(len(data) - look_back):
        X.append(data[i:(i + look_back)])  # 获取look_back个历史点
        y.append(data[i + look_back])      # 获取下一个点作为目标

    X, y = np.array(X), np.array(y)
    # 将X重塑为LSTM/GRU需要的3D格式 [样本数, 时间步长, 特征数]
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))

    return X, y
```

**网络构建**：
根据预测任务的需求，构建适当的 LSTM 或 GRU 网络结构。回归任务（温度、AQI 等）和分类任务（天气状况）使用不同的网络结构和输出层。以下是构建 LSTM 回归模型和 GRU 分类模型的示例：

```python
def build_lstm_regression_model(look_back, lstm_units=64):
    """构建LSTM回归模型"""
    model = Sequential()
    model.add(LSTM(units=lstm_units, input_shape=(look_back, 1)))
    model.add(Dense(1))  # 单输出用于回归
    return model

def build_gru_classification_model(look_back, num_classes, gru_units=64):
    """构建GRU分类模型"""
    model = Sequential()
    model.add(GRU(units=gru_units, input_shape=(look_back, 1)))
    model.add(Dense(num_classes, activation='softmax'))  # 多输出用于分类
    return model
```

**模型编译与训练**：
配置模型的损失函数、优化器和评估指标，然后使用准备好的训练数据训练模型。为防止过拟合，使用早停和 Dropout 等正则化技术。代码示例如下：

```python
def train_lstm_model(X_train, y_train, X_val, y_val, lstm_units=64, epochs=100, batch_size=32):
    """训练LSTM回归模型"""
    # 构建模型
    model = build_lstm_regression_model(X_train.shape[1], lstm_units)

    # 编译模型
    model.compile(optimizer='adam', loss='mae', metrics=['mae'])

    # 设置早停
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True,
        verbose=1
    )

    # 训练模型
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping],
        verbose=1
    )

    return model, history
```

**模型保存**：
将训练好的深度学习模型保存为 H5 格式，便于后续加载使用。代码示例如下：

```python
def save_keras_model(model, target_name, model_type='lstm', model_dir='trained_models'):
    """保存Keras模型（LSTM或GRU）"""
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_path = os.path.join(model_dir, f'{model_type}_{target_name}_model.h5')
    model.save(model_path)
    print(f"Keras模型已保存到: {model_path}")
```

### 6.2.4 Prophet 模型训练

Prophet 模型相对简单易用，训练流程包括数据格式转换、模型初始化、拟合和保存等步骤。

**数据格式转换**：
Prophet 要求输入数据包含两列：ds（日期）和 y（目标值）。需要将原始数据转换为这种格式。代码示例如下：

```python
def prepare_prophet_data(df, target_column):
    """准备Prophet模型输入数据"""
    prophet_df = df.reset_index()[['date', target_column]].rename(
        columns={'date': 'ds', target_column: 'y'})
    return prophet_df
```

**模型初始化与拟合**：
初始化 Prophet 模型，配置相关参数，然后使用准备好的数据拟合模型。代码示例如下：

```python
def train_prophet_model(df, seasonality_mode='additive', changepoint_prior_scale=0.05):
    """训练Prophet模型"""
    # 初始化模型
    model = Prophet(
        seasonality_mode=seasonality_mode,         # 季节性模式: 'additive'或'multiplicative'
        changepoint_prior_scale=changepoint_prior_scale,  # 变点灵活度
        yearly_seasonality=True,                   # 年季节性
        weekly_seasonality=True,                   # 周季节性
        daily_seasonality=False                    # 日季节性（数据是日级别，不需要）
    )

    # 添加月季节性
    model.add_seasonality(name='monthly', period=30.5, fourier_order=5)

    # 拟合模型
    model.fit(df)

    return model
```

**模型保存**：
将训练好的 Prophet 模型序列化为 JSON 格式保存。代码示例如下：

```python
def save_prophet_model(model, target_name, model_dir='trained_models'):
    """保存Prophet模型为JSON格式"""
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_path = os.path.join(model_dir, f'prophet_{target_name}_model.json')
    with open(model_path, 'w') as f:
        f.write(model_to_json(model))

    print(f"Prophet模型已保存到: {model_path}")
```

### 6.2.5 模型训练自动化

为了便于定期更新模型，我们设计了自动化的模型训练流程，将上述各个步骤整合成统一的训练脚本。主要步骤包括：

1. 从数据库加载最新数据
2. 应用特征工程
3. 划分训练集和测试集
4. 训练各类模型（LightGBM、LSTM/GRU、Prophet）
5. 评估模型性能
6. 保存模型和评估指标

这个自动化流程通过定时任务定期执行，确保系统的预测模型始终基于最新数据，保持预测准确性。

## 6.3 模型评估指标

准确评估模型性能对于选择最佳模型和持续改进系统至关重要。针对不同类型的预测任务，我们采用了不同的评估指标。

### 6.3.1 回归任务评估指标

对于温度、AQI 指数、PM2.5 和臭氧等数值预测任务，我们主要使用以下评估指标：

**平均绝对误差（MAE）**：
MAE 衡量预测值与实际值之间的平均绝对差异，计算公式为：

$$MAE = \frac{1}{n} \sum_{i=1}^{n} |y_i - \hat{y}_i|$$

其中$y_i$为实际值，$\hat{y}_i$为预测值，$n$为样本数量。MAE 直观易懂，与预测目标的单位一致，是我们的主要评估指标。

**均方根误差（RMSE）**：
RMSE 衡量预测误差的标准差，计算公式为：

$$RMSE = \sqrt{\frac{1}{n} \sum_{i=1}^{n} (y_i - \hat{y}_i)^2}$$

RMSE 对大误差更敏感，能够惩罚较大的预测偏差。

**决定系数（R²）**：
R² 衡量模型解释目标变量方差的比例，计算公式为：

$$R^2 = 1 - \frac{\sum_{i=1}^{n} (y_i - \hat{y}_i)^2}{\sum_{i=1}^{n} (y_i - \bar{y})^2}$$

其中$\bar{y}$为实际值的平均值。R² 的值在 0 到 1 之间，越接近 1 表示模型拟合越好。

**平均绝对百分比误差（MAPE）**：
MAPE 衡量预测值与实际值的相对误差，以百分比表示，计算公式为：

$$MAPE = \frac{100\%}{n} \sum_{i=1}^{n} \left| \frac{y_i - \hat{y}_i}{y_i} \right|$$

MAPE 提供了误差的相对量度，便于跨不同尺度的指标比较。

### 6.3.2 分类任务评估指标

对于天气状况等分类预测任务，我们主要使用以下评估指标：

**准确率（Accuracy）**：
准确率是最直观的分类评估指标，表示正确预测的样本比例，计算公式为：

$$Accuracy = \frac{TP + TN}{TP + TN + FP + FN}$$

其中 TP 为真正例数量，TN 为真负例数量，FP 为假正例数量，FN 为假负例数量。

**F1 分数（F1 Score）**：
F1 分数是精确率（Precision）和召回率（Recall）的调和平均，计算公式为：

$$F1 = 2 \times \frac{Precision \times Recall}{Precision + Recall}$$

其中精确率$Precision = \frac{TP}{TP + FP}$，召回率$Recall = \frac{TP}{TP + FN}$。

**混淆矩阵（Confusion Matrix）**：
混淆矩阵提供了分类模型预测结果的详细视图，显示了不同类别之间的混淆情况。对于天气状况分类，我们使用混淆矩阵分析模型在不同天气类别上的表现，识别容易混淆的类别。

**多类别评估策略**：
天气状况分类是一个多类别分类问题，我们采用宏平均（macro-average）和微平均（micro-average）两种策略计算整体性能指标：

- 宏平均：计算每个类别的指标，然后取平均值，对所有类别赋予相同权重
- 微平均：将所有类别的预测结果合并后计算指标，对样本数较多的类别赋予更高权重

### 6.3.3 模型评估工具

为了系统化和自动化评估模型性能，我们开发了一套模型评估工具，主要功能包括：

**评估数据准备**：
从测试集中准备评估数据，确保与实际预测场景一致。对于多步预测（如预测未来 15 天），使用递归策略进行滚动预测，而不仅仅是一步预测。

**指标计算与可视化**：
自动计算上述评估指标，并生成可视化图表，如预测值与实际值对比图、误差分布图、ROC 曲线、混淆矩阵热力图等。

**模型比较**：
对不同模型在相同测试集上的性能进行比较，生成比较表格和图表，便于直观分析各模型的优缺点。

**评估报告生成**：
生成详细的评估报告，包括各项指标、关键图表和结论性分析，为模型选择和优化提供依据。

## 6.4 模型优化策略

模型优化是提升预测性能的关键步骤。针对不同类型的模型，我们采用了不同的优化策略。

### 6.4.1 超参数调优

超参数调优是提升模型性能最直接的方法。我们对不同模型的关键超参数进行了系统性调优。

**LightGBM 超参数调优**：
对 LightGBM 模型，我们主要调优以下参数：

- learning_rate：学习率，影响每棵树的贡献权重
- num_leaves：每棵树的最大叶子数，控制模型复杂度
- feature_fraction：特征抽样比例，防止过拟合
- bagging_fraction：数据抽样比例，防止过拟合
- lambda_l1/lambda_l2：L1/L2 正则化参数，控制模型复杂度

调优方法采用贝叶斯优化（Bayesian Optimization），相比网格搜索和随机搜索更高效。代码示例如下：

```python
from hyperopt import hp, fmin, tpe, STATUS_OK, Trials

def objective(params):
    """超参数调优的目标函数"""
    # 将参数转换为合适的格式
    params_dict = {
        'learning_rate': params['learning_rate'],
        'num_leaves': int(params['num_leaves']),
        'feature_fraction': params['feature_fraction'],
        'bagging_fraction': params['bagging_fraction'],
        'lambda_l1': params['lambda_l1'],
        'lambda_l2': params['lambda_l2'],
        'objective': 'regression_l1',
        'metric': 'mae',
        'verbose': -1
    }

    # 使用交叉验证评估参数性能
    cv_results = lgb.cv(
        params_dict,
        train_data,
        num_boost_round=1000,
        nfold=5,
        stratified=False,
        shuffle=True,
        early_stopping_rounds=50,
        verbose_eval=False,
        seed=42
    )

    # 获取最佳迭代次数和对应的MAE
    best_rounds = len(cv_results['valid_mae-mean'])
    best_score = cv_results['valid_mae-mean'][-1]

    return {'loss': best_score, 'status': STATUS_OK, 'params': params_dict, 'iterations': best_rounds}

# 定义参数搜索空间
space = {
    'learning_rate': hp.loguniform('learning_rate', -4, -1),  # 0.0001 到 0.1
    'num_leaves': hp.quniform('num_leaves', 10, 100, 1),      # 10 到 100
    'feature_fraction': hp.uniform('feature_fraction', 0.5, 1.0),
    'bagging_fraction': hp.uniform('bagging_fraction', 0.5, 1.0),
    'lambda_l1': hp.loguniform('lambda_l1', -3, 2),           # 0.001 到 10
    'lambda_l2': hp.loguniform('lambda_l2', -3, 2)            # 0.001 到 10
}

# 执行贝叶斯优化
trials = Trials()
best = fmin(objective, space, algo=tpe.suggest, max_evals=100, trials=trials)
```

**LSTM/GRU 超参数调优**：
对 LSTM/GRU 模型，我们主要调优以下参数：

- units：LSTM/GRU 单元数量，控制模型容量
- batch_size：批次大小，影响训练稳定性和速度
- dropout_rate：Dropout 比例，防止过拟合
- look_back：输入序列长度，控制历史数据使用量
- optimizer_params：优化器参数，如学习率

**Prophet 超参数调优**：
对 Prophet 模型，我们主要调优以下参数：

- changepoint_prior_scale：变点灵活度，控制趋势变化敏感度
- seasonality_prior_scale：季节性组件强度
- seasonality_mode：季节性模式（加法或乘法）
- fourier_order：傅里叶级数阶数，控制季节性复杂度

### 6.4.2 特征选择

特征选择是提高模型性能和减少过拟合的重要手段。我们采用了以下特征选择方法：

**特征重要性分析**：
利用 LightGBM 的内置特征重要性评估功能，识别对预测最有价值的特征。特征重要性可以基于不同指标计算，如增益（gain）、分裂（split）和覆盖度（cover）。

**递归特征消除（RFE）**：
通过迭代训练模型并移除最不重要的特征，逐步筛选出最优特征子集。

**L1 正则化**：
在 LightGBM 中增加 L1 正则化项（lambda_l1 参数），促使模型自动执行特征选择，将不重要特征的权重降为零。

通过特征选择，我们既提高了模型性能，又减少了计算复杂度，加速了预测过程。

### 6.4.3 集成方法

集成多个模型的预测结果通常能获得比单一模型更好的性能。我们实现了以下集成方法：

**投票法/平均法**：
对于分类任务（天气状况），使用投票法合并多个模型的预测；对于回归任务（温度、AQI 等），使用平均法合并预测结果。

**加权集成**：
根据各模型在验证集上的性能，为不同模型分配不同的权重。例如，对于温度预测，我们可能为 LightGBM 分配 0.5 的权重，为 LSTM 分配 0.3 的权重，为 Prophet 分配 0.2 的权重。

**堆叠集成（Stacking）**：
使用一个元模型（meta-model）组合基础模型的预测结果。基础模型包括 LightGBM、LSTM 和 Prophet，元模型通常使用简单的线性回归或另一个 LightGBM。

**时间段自适应集成**：
针对不同的预测时间段，自动调整模型权重。例如，近期预测（1-3 天）可能更依赖 LightGBM，而远期预测（8-15 天）可能更依赖 Prophet。

### 6.4.4 模型微调

除了超参数调优和特征选择外，我们还进行了一系列模型微调工作，以适应气象和空气质量预测的特点：

**数据增强**：
通过添加噪音、时间移位等方法扩充训练数据，提高模型的鲁棒性和泛化能力。

**特征交互**：
手动或自动创建特征交互项，捕捉变量间的非线性关系，如温度与湿度的交互对空气质量的影响。

**序列长度优化**：
对 LSTM/GRU 模型，通过实验确定最优的输入序列长度（look_back），平衡模型性能和计算效率。

**自定义损失函数**：
针对特定预测任务设计自定义损失函数，如为空气质量预测增加对高污染状况的惩罚权重。

通过这些优化策略，我们显著提升了各类预测模型的性能，使系统能够提供更准确、更可靠的气象和空气质量预测服务。

# 第七章 系统实现

系统实现是将前期的需求分析和设计转化为实际可运行系统的过程。本章将介绍系统实现的各个方面，包括开发环境、核心模块实现、关键功能和技术难点解决方案等。

## 7.1 开发环境

开发环境的选择和配置是系统实现的基础，合适的工具和技术栈能够提高开发效率和系统质量。本节详细介绍系统的软硬件环境配置。

### 7.1.1 硬件环境

系统开发和部署使用了以下硬件环境：

**开发环境硬件**：

- 处理器：Intel Core i7-10700K 8 核 16 线程 3.8GHz
- 内存：32GB DDR4 3200MHz
- 存储：1TB NVMe SSD
- 显卡：NVIDIA GeForce RTX 3060 6GB（用于加速深度学习模型训练）

**部署环境硬件**：

- 服务器：云服务器或物理服务器
- 处理器：至少 4 核 8 线程
- 内存：至少 16GB
- 存储：至少 100GB SSD

硬件配置主要考虑了模型训练和系统运行的需求，特别是深度学习模型训练需要较高的计算资源。

### 7.1.2 软件环境

系统开发和部署使用了以下软件环境：

**操作系统**：

- 开发环境：Windows 10 专业版 / Ubuntu 20.04 LTS
- 部署环境：Ubuntu 20.04 LTS / CentOS 8

**编程语言与核心框架**：

- 编程语言：Python 3.8
- Web 框架：Flask 2.2.5
- 数据库：SQLite 3
- 前端框架：Bootstrap，jQuery，ECharts

**Python 依赖库**：
系统的主要 Python 依赖库包括：

```
# Web应用相关
Flask==2.2.5
Flask-Login==0.6.3
Flask-Cors==5.0.0
Werkzeug==2.2.3
Jinja2==3.1.2
python-dotenv==0.21.1
waitress==2.1.2

# 数据处理相关
pandas==1.3.5
numpy==1.21.6
scikit-learn==1.0.2
joblib==1.2.0

# 机器学习相关
lightgbm==4.6.0
tensorflow==2.11.0
prophet==1.1.6

# 数据爬取相关
requests==2.28.2
beautifulsoup4==4.11.2

# 图表与可视化
matplotlib==3.5.3
```

这些依赖库在`requirements.txt`文件中明确定义，便于环境复制和部署。

**开发工具**：

- IDE：PyCharm Professional / Visual Studio Code
- 版本控制：Git / GitHub
- 数据库工具：DB Browser for SQLite
- API 测试：Postman

**部署工具**：

- Web 服务器：Waitress（Windows）/ Gunicorn（Linux）
- 进程管理：Supervisor（Linux）
- 容器化：Docker（可选）

### 7.1.3 项目结构

系统采用模块化的项目结构，便于开发、测试和维护。主要目录和文件组织如下：

```
weather_analysis/
├── app.py                 # 应用程序入口
├── config.py              # 配置文件
├── database.py            # 数据库连接管理
├── models.py              # 用户模型定义
├── utils.py               # 实用函数集
├── create_db.py           # 数据库初始化脚本
├── requirements.txt       # 依赖列表
├── blueprints/            # Flask蓝图目录
│   ├── __init__.py
│   ├── auth.py            # 认证蓝图
│   ├── data_api.py        # 数据API蓝图
│   ├── pages.py           # 页面路由蓝图
│   └── predict_api.py     # 预测API蓝图
├── static/                # 静态文件目录
│   ├── css/               # CSS样式文件
│   ├── js/                # JavaScript文件
│   └── images/            # 图片资源
├── templates/             # HTML模板目录
│   ├── layout.html        # 基础布局模板
│   ├── index.html         # 首页模板
│   ├── predict_dashboard.html  # 预测仪表盘
│   └── ...                # 其他页面模板
├── trained_models/        # 训练好的模型存储目录
│   ├── lgbm_avg_temp_model.pkl
│   ├── lstm_avg_temp_model.h5
│   └── ...                # 其他模型文件
├── data.db                # 主数据库（气象和空气质量数据）
├── user_info.db           # 用户数据库
├── weather_spider.py      # 气象数据爬虫
├── aqi_spider.py          # 空气质量数据爬虫
├── train_models.py        # 模型训练脚本
└── logs/                  # 日志目录
```

这种目录结构清晰地分离了不同功能模块，使得系统易于理解和维护。Flask 蓝图的使用进一步增强了代码的模块化程度。

## 7.2 核心模块实现

系统的核心模块实现是将设计方案转化为实际代码的过程。本节将详细介绍系统各核心模块的实现方法和关键技术点。

### 7.2.1 数据采集模块实现

数据采集模块负责从网络获取气象和空气质量数据，是系统数据来源的基础。该模块主要包括两个爬虫程序：气象数据爬虫和空气质量数据爬虫。

**气象数据爬虫实现**：
气象数据爬虫（`weather_spider.py`）通过解析气象网站的 HTML 结构，提取天气状况、温度范围和风力信息等数据。关键实现包括：

```python
def crawl_weather_data(city, start_date, end_date):
    """爬取指定城市和日期范围的天气数据"""
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()

    date_range = pd.date_range(start=start_date, end=end_date)
    dates_str = [d.strftime('%Y-%m-%d') for d in date_range]

    for date_str in dates_str:
        # 检查数据是否已存在
        cursor.execute("SELECT 1 FROM weather_data WHERE city=? AND date=?", (city, date_str))
        if cursor.fetchone():
            logging.info(f"城市 {city} 日期 {date_str} 的天气数据已存在，跳过爬取")
            continue

        # 构建URL并获取页面内容
        url = f"http://example.weather.com/history/{city}/{date_str}"
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            if response.status_code != 200:
                logging.error(f"获取页面失败：{response.status_code}")
                continue

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取数据
            weather_condition = extract_weather_condition(soup)
            temperature_range = extract_temperature_range(soup)
            wind_info = extract_wind_info(soup)

            # 存储数据
            cursor.execute(
                "INSERT INTO weather_data VALUES (?, ?, ?, ?, ?)",
                (city, date_str, weather_condition, temperature_range, wind_info)
            )
            conn.commit()

            logging.info(f"成功爬取 {city} {date_str} 的天气数据")
            time.sleep(random.uniform(1, 3))  # 随机延时，避免请求过于频繁

        except Exception as e:
            logging.error(f"爬取 {city} {date_str} 天气数据时出错：{e}")
            continue

    conn.close()
    logging.info(f"完成 {city} 从 {start_date} 到 {end_date} 的天气数据爬取")
```

**空气质量数据爬虫实现**：
空气质量数据爬虫（`aqi_spider.py`）负责从空气质量监测网站爬取 AQI 指数、PM2.5、PM10 等污染物浓度数据。关键实现包括：

```python
def crawl_aqi_data(city, start_date, end_date):
    """爬取指定城市和日期范围的空气质量数据"""
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()

    date_range = pd.date_range(start=start_date, end=end_date)
    dates_str = [d.strftime('%Y-%m-%d') for d in date_range]

    for date_str in dates_str:
        # 检查数据是否已存在
        cursor.execute("SELECT 1 FROM aqi_data WHERE city=? AND date=?", (city, date_str))
        if cursor.fetchone():
            logging.info(f"城市 {city} 日期 {date_str} 的空气质量数据已存在，跳过爬取")
            continue

        # 构建URL并获取页面内容
        url = f"http://example.aqi.com/city/{city}/date/{date_str}"
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            if response.status_code != 200:
                logging.error(f"获取页面失败：{response.status_code}")
                continue

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取数据
            quality_level = extract_quality_level(soup)
            aqi_index = extract_aqi_index(soup)
            pm25 = extract_pm25(soup)
            pm10 = extract_pm10(soup)
            so2 = extract_so2(soup)
            no2 = extract_no2(soup)
            co = extract_co(soup)
            o3 = extract_o3(soup)

            # 存储数据
            cursor.execute(
                "INSERT INTO aqi_data VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                (city, date_str, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3)
            )
            conn.commit()

            logging.info(f"成功爬取 {city} {date_str} 的空气质量数据")
            time.sleep(random.uniform(1, 3))  # 随机延时

        except Exception as e:
            logging.error(f"爬取 {city} {date_str} 空气质量数据时出错：{e}")
            continue

    conn.close()
    logging.info(f"完成 {city} 从 {start_date} 到 {end_date} 的空气质量数据爬取")
```

**数据采集调度实现**：
为了实现数据的定期更新，我们设计了数据采集调度程序，通过系统定时任务（如 cron 在 Linux 或计划任务在 Windows）定期执行爬虫脚本。此外，我们还实现了数据采集的失败重试机制，确保数据完整性。

### 7.2.2 预测分析模块实现

预测分析模块是系统的核心，负责加载模型、准备数据、执行预测和返回结果。该模块主要基于 Flask 蓝图实现，提供了 RESTful API 供前端调用。

**模型加载实现**：
系统启动时，会预加载训练好的各类模型，以减少预测请求时的加载时间。关键实现包括：

```python
def load_all_models_and_helpers(app):
    """加载所有预测模型、Scalers和Encoders"""
    logger = app.logger
    logger.info("开始加载模型、Scalers和Encoders")
    model_dir = app.config['MODEL_DIR']  # 'trained_models'

    # 定义需要加载的文件和对应的键名
    models_to_load = {
        # LightGBM
        'lgbm_avg_temp': 'lgbm_avg_temp_model.pkl',
        'lgbm_aqi_index': 'lgbm_aqi_index_model.pkl',
        'lgbm_pm25': 'lgbm_pm25_model.pkl',
        'lgbm_o3': 'lgbm_o3_model.pkl',
        'lgbm_weather': 'lgbm_weather_model.pkl',
        # LSTM
        'lstm_avg_temp': 'lstm_avg_temp_model.h5',
        'lstm_aqi_index': 'lstm_aqi_index_model.h5',
        'lstm_pm25': 'lstm_pm25_model.h5',
        'lstm_o3': 'lstm_o3_model.h5',
        # GRU
        'gru_weather': 'gru_weather_model.h5',
        # Prophet
        'prophet_avg_temp': 'prophet_avg_temp_model.json',
        'prophet_aqi_index': 'prophet_aqi_index_model.json',
        'prophet_pm25': 'prophet_pm25_model.json',
        'prophet_o3': 'prophet_o3_model.json',
    }

    # 加载模型
    app.config['PRELOADED_MODELS'] = {}
    for key, filename in models_to_load.items():
        try:
            path = os.path.join(model_dir, filename)
            if not os.path.exists(path):
                logger.warning(f"模型文件未找到: {path}")
                continue

            model_type = filename.split('.')[-1]
            if model_type == 'pkl':  # LightGBM
                model = joblib.load(path)
            elif model_type == 'h5':  # LSTM/GRU
                model = keras_load_model(path)
            elif model_type == 'json':  # Prophet
                with open(path, 'r') as fin:
                    model = model_from_json(fin.read())

            app.config['PRELOADED_MODELS'][key] = model
            logger.info(f"成功加载模型: {key}")

        except Exception as e:
            logger.error(f"加载模型 {key} 失败: {e}")

    # 类似地加载 Scalers 和 Encoders
    # ...
```

**预测执行实现**：
针对不同类型的模型和预测任务，我们实现了不同的预测函数。以下是 LightGBM 预测的核心实现：

```python
def predict_with_lightgbm(city, target, days=7):
    """使用LightGBM模型进行预测"""
    app = current_app
    logger = app.logger

    # 获取模型和Scaler
    model_key = f'lgbm_{target}'
    model = app.config['PRELOADED_MODELS'].get(model_key)
    scaler = app.config['PRELOADED_SCALERS'].get(target)

    if not model:
        logger.error(f"模型 {model_key} 未找到")
        return {'error': f'预测模型{model_key}未找到'}, 404

    # 获取历史数据
    try:
        history_data = get_historical_data_for_prediction(city, target)
        if history_data.empty:
            logger.error(f"无法获取城市 {city} 的历史数据")
            return {'error': f'无法获取城市{city}的历史数据'}, 404
    except Exception as e:
        logger.error(f"获取历史数据失败: {e}")
        return {'error': '获取历史数据失败'}, 500

    # 准备特征数据
    try:
        df_featured = create_time_features(history_data)
        target_cols = [target]
        df_featured = create_lag_features(df_featured, target_cols, [1, 2, 3, 7, 14])
        df_featured = create_rolling_features(df_featured, target_cols, [3, 7, 14])
    except Exception as e:
        logger.error(f"特征工程失败: {e}")
        return {'error': '特征工程失败'}, 500

    # 递归预测
    predictions = []
    current_data = df_featured.copy()

    for i in range(days):
        # 准备当前步骤的输入特征
        features = prepare_prediction_features(current_data)

        # 执行预测
        pred = model.predict(features)[0]

        # 如果有scaler，反向转换预测值
        if scaler:
            pred = scaler.inverse_transform([[pred]])[0][0]

        # 保存预测结果
        predictions.append(pred)

        # 更新数据，为下一步预测准备
        next_date = current_data.index[-1] + pd.Timedelta(days=1)
        new_row = pd.DataFrame({target: [pred]}, index=[next_date])
        current_data = pd.concat([current_data, new_row])

        # 更新特征
        current_data = create_time_features(current_data)
        current_data = create_lag_features(current_data, target_cols, [1, 2, 3, 7, 14])
        current_data = create_rolling_features(current_data, target_cols, [3, 7, 14])

    # 准备返回结果
    last_date = history_data.index[-1]
    dates = [(last_date + pd.Timedelta(days=i+1)).strftime('%Y-%m-%d') for i in range(days)]

    return {
        'city': city,
        'target': target,
        'model': 'lightgbm',
        'dates': dates,
        'predictions': predictions
    }
```

**预测 API 实现**：
我们通过 Flask 蓝图定义了一系列预测 API，供前端调用。以下是温度预测 API 的实现示例：

```python
@predict_api_bp.route('/predict/temperature', methods=['POST'])
@login_required
def predict_temperature():
    """温度预测API"""
    data = request.get_json()

    if not data:
        return jsonify({'error': '无效的请求数据'}), 400

    city = data.get('city')
    model_type = data.get('model_type', 'lightgbm')  # 默认使用LightGBM
    days = int(data.get('days', 7))  # 默认预测7天

    if not city:
        return jsonify({'error': '缺少城市参数'}), 400

    try:
        if model_type == 'lightgbm':
            result = predict_with_lightgbm(city, 'avg_temp', days)
        elif model_type == 'lstm':
            result = predict_with_lstm(city, 'avg_temp', days)
        elif model_type == 'prophet':
            result = predict_with_prophet(city, 'avg_temp', days)
        else:
            return jsonify({'error': f'不支持的模型类型: {model_type}'}), 400

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"预测过程中出错: {e}", exc_info=True)
        return jsonify({'error': '预测过程中出错'}), 500
```

### 7.2.3 Web 应用模块实现

Web 应用模块负责构建用户界面，处理用户请求，呈现数据和预测结果。该模块基于 Flask 框架和 Jinja2 模板引擎实现。

**路由设计实现**：
系统的主要路由在`pages.py`蓝图中定义，包括首页、预测页面、历史数据查询页面等。以下是主要路由的实现示例：

```python
@pages_bp.route('/')
def index():
    """首页路由"""
    if current_user.is_authenticated:
        return redirect(url_for('pages.home'))
    return render_template('index.html')

@pages_bp.route('/home')
@login_required
def home():
    """主页路由，需要登录"""
    return render_template('home.html')

@pages_bp.route('/predict')
@login_required
def predict_dashboard():
    """预测仪表盘页面"""
    cities = get_available_cities()
    return render_template('predict_dashboard.html', cities=cities)

@pages_bp.route('/history')
@login_required
def history_weather():
    """历史天气查询页面"""
    cities = get_available_cities()
    return render_template('history_weather.html', cities=cities)
```

**用户认证实现**：
系统使用 Flask-Login 扩展实现用户认证功能，包括登录、注册和会话管理。以下是登录实现示例：

```python
@auth_bp.route('/login', methods=['POST'])
def login():
    """处理用户登录请求"""
    data = request.get_json()

    if not data:
        return jsonify({'success': False, 'message': '无效的请求数据'}), 400

    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400

    try:
        db = get_user_db()
        cursor = db.cursor()

        # 查询用户
        cursor.execute("SELECT name, password FROM user WHERE name=?", (username,))
        user_data = cursor.fetchone()

        if not user_data or not check_password_hash(user_data['password'], password):
            return jsonify({'success': False, 'message': '用户名或密码错误'}), 401

        # 创建用户对象并登录
        user = User(user_data['name'])
        login_user(user)

        return jsonify({'success': True, 'message': '登录成功'})

    except Exception as e:
        current_app.logger.error(f"登录过程中出错: {e}", exc_info=True)
        return jsonify({'success': False, 'message': '登录过程中出错'}), 500
```

**前端页面实现**：
系统的前端页面基于 HTML、CSS 和 JavaScript 实现，使用 Bootstrap 框架实现响应式布局，使用 ECharts 库实现数据可视化。以下是预测仪表盘页面的关键 JavaScript 代码示例：

```javascript
// 初始化预测图表
function initPredictionChart() {
  predictionChart = echarts.init(
    document.getElementById('prediction_chart')
  )

  const option = {
    title: {
      text: '预测结果',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const date = params[0].name
        let result = `<div>${date}</div>`

        params.forEach(param => {
          const value = param.value
          const color = param.color
          const seriesName = param.seriesName
          result += `<div>
                        <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};"></span>
                        <span style="margin-left:5px">${seriesName}：${value}</span>
                    </div>`
        })

        return result
      },
    },
    legend: {
      data: ['历史数据', '预测数据'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
      name: '',
      axisLabel: {
        formatter: '{value}',
      },
    },
    series: [
      {
        name: '历史数据',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
        },
      },
      {
        name: '预测数据',
        type: 'line',
        data: [],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
        },
      },
    ],
  }

  predictionChart.setOption(option)
}

// 执行预测请求
function executePrediction() {
  const city = document.getElementById('city_select').value
  const target = getCurrentTarget()
  const modelType = getCurrentModel()
  const days = document.getElementById('days_slider').value

  // 显示加载状态
  showLoadingOverlay('prediction_chart_container')

  // 执行API请求
  fetch('/api/predict/' + target, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      city: city,
      model_type: modelType,
      days: parseInt(days),
    }),
  })
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        showErrorOverlay('prediction_chart_container', data.error)
        return
      }

      // 更新图表
      updatePredictionChart(data)

      // 隐藏加载状态
      hideOverlay('prediction_chart_container')

      // 更新结果摘要
      updateResultSummary(data)
    })
    .catch(error => {
      console.error('预测请求出错:', error)
      showErrorOverlay(
        'prediction_chart_container',
        '请求失败，请稍后重试'
      )
    })
}
```

### 7.2.4 数据可视化实现

数据可视化是系统的重要组成部分，帮助用户直观理解数据趋势和预测结果。我们主要使用 ECharts 库实现各类可视化图表。

**历史数据可视化**：
历史数据可视化包括温度变化趋势图、AQI 指数趋势图、污染物浓度对比图等。以下是温度变化趋势图的实现示例：

```javascript
function initTemperatureChart() {
  temperatureChart = echarts.init(
    document.getElementById('temperature_chart')
  )

  const option = {
    title: {
      text: '历史温度变化趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const date = params[0].name
        let result = `<div>${date}</div>`

        params.forEach(param => {
          if (param.seriesName.includes('温度')) {
            const value = param.value
            const color = param.color
            const seriesName = param.seriesName
            result += `<div>
                            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};"></span>
                            <span style="margin-left:5px">${seriesName}：${value}℃</span>
                        </div>`
          }
        })

        return result
      },
    },
    legend: {
      data: ['平均温度', '最高温度', '最低温度'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
      name: '温度(℃)',
      axisLabel: {
        formatter: '{value} ℃',
      },
    },
    series: [
      {
        name: '平均温度',
        type: 'line',
        data: [],
        smooth: true,
        lineStyle: {
          width: 3,
        },
      },
      {
        name: '最高温度',
        type: 'line',
        data: [],
        smooth: true,
        lineStyle: {
          width: 2,
          type: 'dashed',
        },
      },
      {
        name: '最低温度',
        type: 'line',
        data: [],
        smooth: true,
        lineStyle: {
          width: 2,
          type: 'dashed',
        },
      },
    ],
  }

  temperatureChart.setOption(option)
}
```

**预测结果可视化**：
预测结果可视化包括预测值与历史值对比图、多模型预测结果对比图等。以下是更新预测图表的实现示例：

```javascript
function updatePredictionChart(data) {
  // 获取历史数据
  fetch('/api/data/history/' + getCurrentTarget(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      city: document.getElementById('city_select').value,
      days: 30, // 获取最近30天的历史数据
    }),
  })
    .then(response => response.json())
    .then(historyData => {
      if (historyData.error) {
        console.error('获取历史数据失败:', historyData.error)
        return
      }

      // 准备图表数据
      const historyDates = historyData.dates
      const historyValues = historyData.values
      const predictionDates = data.dates
      const predictionValues = data.predictions

      // 更新图表
      predictionChart.setOption({
        xAxis: {
          data: [...historyDates, ...predictionDates],
        },
        yAxis: {
          name: getTargetUnit(getCurrentTarget()),
        },
        series: [
          {
            name: '历史数据',
            data: [
              ...historyValues,
              ...Array(predictionDates.length).fill('-'),
            ],
          },
          {
            name: '预测数据',
            data: [
              ...Array(historyDates.length).fill('-'),
              ...predictionValues,
            ],
          },
        ],
      })
    })
    .catch(error => {
      console.error('获取历史数据失败:', error)
    })
}
```

**交互式可视化**：
为提升用户体验，我们实现了多种交互式可视化功能，如缩放、数据点 hover 提示、数据筛选等。以下是日历热力图的实现示例：

```javascript
function initCalendarChart() {
  calendarChart = echarts.init(
    document.getElementById('calendar_chart')
  )

  const currentYear = new Date().getFullYear()
  const startDate = `${currentYear}-01-01`
  const endDate = `${currentYear}-12-31`

  const option = {
    title: {
      top: 10,
      left: 'center',
      text: `${currentYear}年空气质量日历`,
    },
    tooltip: {
      formatter: function (params) {
        return `${params.data[0]}: ${params.data[1]}`
      },
    },
    visualMap: {
      min: 0,
      max: 300,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: 20,
      inRange: {
        color: ['#52c41a', '#faad14', '#f5222d'],
      },
      text: ['严重污染', '优'],
      textStyle: {
        color: '#333',
      },
    },
    calendar: {
      top: 80,
      left: 40,
      right: 40,
      cellSize: ['auto', 'auto'],
      range: currentYear,
      itemStyle: {
        borderWidth: 0.5,
      },
      yearLabel: { show: false },
    },
    series: {
      type: 'heatmap',
      coordinateSystem: 'calendar',
      data: [],
    },
  }

  calendarChart.setOption(option)
}
```

通过这些核心模块的实现，我们构建了一个完整的气象分析与预测系统，实现了从数据采集、预测分析到结果可视化的全流程功能。

## 7.3 关键功能和技术难点解决方案

在本系统中，我们遇到了一些关键功能和技术难点，包括数据爬取、数据清洗、特征工程、模型训练和预测服务等。以下是我们的解决方案：

### 7.3.1 数据爬取

数据爬取是系统实现的基础，我们使用了 Requests 库和 BeautifulSoup 库来实现网页爬取和解析。为了确保数据爬取的稳定性，我们设计了错误处理机制，当爬取失败时会记录日志并在下次运行时重试。

### 7.3.2 数据清洗

数据清洗是数据预处理的关键步骤，我们采用了多种策略来处理缺失值和异常值。对于结构性缺失，我们使用了线性插值和滑动窗口跳过缺失区域；对于部分字段缺失，我们使用了临近城市数据进行估计，并添加了默认值。

### 7.3.3 特征工程

特征工程是机器学习中至关重要的环节，我们设计了一系列特征工程方法来提取时间特征、滞后特征和滚动特征。这些特征有助于提高模型的预测性能。

### 7.3.4 模型训练

模型训练是系统实现的核心步骤，我们使用了 LightGBM、Keras 和 Prophet 库来训练各类预测模型。为了防止过拟合，我们使用了早停策略和正则化技术。

### 7.3.5 预测服务

预测服务是系统的关键功能之一，我们使用了 Flask 蓝图来组织 API 路由，并实现了预加载机制来提高预测响应速度。我们还设计了错误处理机制，当预测失败时会返回适当的错误信息。

## 7.4 系统运行和维护

系统运行和维护是确保系统稳定性和可靠性的关键步骤。以下是我们的解决方案：

### 7.4.1 系统监控

我们使用了 Supervisor 来管理系统的进程，并设置了定时任务来定期更新数据和模型。此外，我们还监控系统的关键性能指标，如响应时间和资源利用率。

### 7.4.2 错误处理

我们设计了错误处理机制，当系统出现异常行为时会记录日志并生成报警。我们还实现了优雅降级机制，当组件故障时系统能够保持核心功能可用。

### 7.4.3 文档和日志

我们提供了完整的系统设计文档、API 文档和用户手册，便于用户理解和使用系统。我们还记录了系统的运行日志，用于问题排查和系统优化。

通过以上设计和实现步骤，我们构建了一个结构清晰、功能完善的气象分析与预测系统。三层架构的设计实现了系统的模块化和低耦合，合理的数据库设计支持了高效的数据存储和检索，完善的功能模块划分明确了各部分的职责边界，清晰的系统流程设计确保了数据和控制的有序流转。这些设计为系统的实现提供了可靠的蓝图和指导。

## 7.3 系统部署与优化

系统的部署与优化是确保系统稳定运行和高效响应的关键环节。本节将介绍系统的部署方式、性能优化策略和安全措施。

### 7.3.1 系统部署方案

根据系统规模和资源限制，我们采用了单服务器部署方案，同时预留了扩展空间。具体部署方式如下：

**Web 服务器部署**：
在 Windows 环境中，我们使用 Waitress 作为生产级 WSGI 服务器；在 Linux 环境中，使用 Gunicorn 作为 WSGI 服务器。部署脚本示例如下：

```python
# Windows环境部署脚本（run_server.py）
from waitress import serve
from app import app

if __name__ == "__main__":
    print("Starting server with Waitress...")
    serve(app, host="0.0.0.0", port=5000, threads=8)
```

```bash
# Linux环境部署脚本（gunicorn_start.sh）
#!/bin/bash
NAME="weather_prediction"
SOCKFILE=/home/<USER>/weather_prediction/gunicorn.sock
USER=user
GROUP=user
NUM_WORKERS=4
WSGI_MODULE=app:app

echo "Starting $NAME as `whoami`"

# 激活虚拟环境
source /home/<USER>/venv/bin/activate

# 启动Gunicorn
exec gunicorn ${WSGI_MODULE} \
  --name $NAME \
  --workers $NUM_WORKERS \
  --user=$USER --group=$GROUP \
  --bind=unix:$SOCKFILE \
  --log-level=info \
  --access-logfile=/home/<USER>/weather_prediction/logs/access.log \
  --error-logfile=/home/<USER>/weather_prediction/logs/error.log
```

**反向代理配置**：
在生产环境中，我们使用 Nginx 作为反向代理，处理静态文件请求并转发动态请求到 WSGI 服务器。Nginx 配置示例如下：

```nginx
server {
    listen 80;
    server_name weather.example.com;

    location / {
        proxy_pass http://unix:/home/<USER>/weather_prediction/gunicorn.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /home/<USER>/weather_prediction/static;
        expires 30d;
    }

    location /favicon.ico {
        alias /home/<USER>/weather_prediction/static/images/favicon.ico;
    }
}
```

**数据库部署**：
SQLite 数据库文件放置在应用目录中，并定期备份以防数据丢失。备份脚本示例：

```bash
#!/bin/bash
# database_backup.sh
DATE=$(date +"%Y%m%d")
BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/weather_prediction"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $APP_DIR/data.db $BACKUP_DIR/data_$DATE.db
cp $APP_DIR/user_info.db $BACKUP_DIR/user_info_$DATE.db

# 删除30天前的备份
find $BACKUP_DIR -name "*.db" -type f -mtime +30 -delete

echo "Database backup completed at $DATE"
```

**定时任务配置**：
使用 crontab（Linux）或计划任务（Windows）设置定时任务，执行数据采集、模型训练和数据库备份等操作。Linux crontab 配置示例：

```
# 每天凌晨2点更新气象数据
0 2 * * * /home/<USER>/venv/bin/python /home/<USER>/weather_prediction/weather_spider.py >> /home/<USER>/weather_prediction/logs/weather_spider.log 2>&1

# 每天凌晨3点更新空气质量数据
0 3 * * * /home/<USER>/venv/bin/python /home/<USER>/weather_prediction/aqi_spider.py >> /home/<USER>/weather_prediction/logs/aqi_spider.log 2>&1

# 每周日凌晨4点重新训练模型
0 4 * * 0 /home/<USER>/venv/bin/python /home/<USER>/weather_prediction/train_models.py >> /home/<USER>/weather_prediction/logs/train_models.log 2>&1

# 每天凌晨1点备份数据库
0 1 * * * /home/<USER>/weather_prediction/database_backup.sh >> /home/<USER>/weather_prediction/logs/backup.log 2>&1
```

### 7.3.2 性能优化策略

为提高系统响应速度和资源利用效率，我们实施了以下性能优化策略：

**模型预加载**：
系统启动时预加载所有预测模型，避免在处理预测请求时加载模型而导致的延迟。这种方式虽然增加了内存占用，但显著提高了预测请求的响应速度。

**数据缓存**：
针对频繁访问的数据，如城市列表、最近的历史数据等，实现了内存缓存机制，减少数据库查询次数。缓存实现示例：

```python
# 缓存装饰器
def timed_cache(seconds=600):
    """指定时间内缓存函数返回结果的装饰器"""
    def decorator(func):
        cache = {}
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            key = str((args, frozenset(kwargs.items())))
            now = time.time()
            if key in cache:
                result, timestamp = cache[key]
                if now - timestamp < seconds:
                    return result
            result = func(*args, **kwargs)
            cache[key] = (result, now)
            return result
        return wrapper
    return decorator

# 使用缓存装饰器
@timed_cache(seconds=3600)  # 缓存1小时
def get_available_cities():
    """获取系统中可用的城市列表"""
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT city FROM weather_data ORDER BY city")
    cities = [row[0] for row in cursor.fetchall()]
    conn.close()
    return cities
```

**数据库索引优化**：
为频繁查询的字段创建索引，提高查询效率。索引创建示例：

```sql
-- 为weather_data表的city和date列创建组合索引
CREATE INDEX IF NOT EXISTS idx_weather_city_date ON weather_data (city, date);

-- 为aqi_data表的city和date列创建组合索引
CREATE INDEX IF NOT EXISTS idx_aqi_city_date ON aqi_data (city, date);
```

**前端资源优化**：
对前端资源进行优化，减少加载时间和渲染延迟。主要措施包括：

- CSS 和 JavaScript 文件压缩和合并
- 图片压缩和延迟加载
- 使用浏览器缓存策略

**异步处理**：
对于耗时操作，如批量数据处理、模型训练等，采用异步处理方式，避免阻塞主线程。示例代码：

```python
def async_task(func):
    """异步任务装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    return wrapper

@async_task
def train_model_async(city, target):
    """异步训练模型"""
    logging.info(f"开始异步训练模型: {city} - {target}")
    try:
        # 模型训练代码
        # ...
        logging.info(f"模型训练完成: {city} - {target}")
    except Exception as e:
        logging.error(f"模型训练失败: {e}")
```

### 7.3.3 安全措施

系统安全是确保数据和功能不被滥用的重要保障。我们实施了以下安全措施：

**用户认证与授权**：
使用 Flask-Login 实现用户认证，通过装饰器控制访问权限。密码使用 Werkzeug 的 generate_password_hash 和 check_password_hash 函数进行哈希处理，防止明文存储。

**输入验证与防注入**：
对所有用户输入进行验证，使用参数化查询防止 SQL 注入攻击。示例代码：

```python
def get_weather_data(city, start_date, end_date):
    """安全地获取天气数据"""
    # 输入验证
    if not city or not isinstance(city, str):
        raise ValueError("城市名不能为空且必须是字符串")

    try:
        # 日期格式验证
        datetime.strptime(start_date, "%Y-%m-%d")
        datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError:
        raise ValueError("日期格式无效，应为YYYY-MM-DD")

    # 使用参数化查询，防止SQL注入
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()
    cursor.execute(
        "SELECT * FROM weather_data WHERE city=? AND date>=? AND date<=? ORDER BY date",
        (city, start_date, end_date)
    )
    results = cursor.fetchall()
    conn.close()

    return results
```

**CSRF 防护**：
实现 CSRF 令牌验证，防止跨站请求伪造攻击。Flask-WTF 提供了 CSRF 保护机制，我们在表单处理中使用了这一机制。

**安全配置**：
采用安全的应用配置，包括：

- 使用环境变量存储敏感信息
- 配置适当的内容安全策略（CSP）
- 使用 HTTPS 加密传输

通过这些部署和优化措施，系统能够稳定运行，高效响应用户请求，同时保持良好的安全性。

# 第八章 系统测试与评估

系统测试与评估是验证系统功能是否符合需求、性能是否达到预期的重要环节。本章将详细介绍系统的测试方案和评估结果，以客观评价系统的质量和效果。

## 8.1 功能测试

功能测试旨在验证系统的各项功能是否按照需求规格正确实现。我们采用黑盒测试和白盒测试相结合的方法，对系统的各个模块进行了全面测试。

### 8.1.1 测试方案

功能测试采用测试用例驱动的方式，针对系统的每个功能点设计测试用例，明确测试步骤、预期结果和实际结果。主要测试内容包括：

**用户管理功能测试**：
验证用户注册、登录、注销等功能是否正常工作，包括正常流程和异常情况的处理。

**数据查询功能测试**：
验证历史数据查询功能，包括不同条件组合的查询、结果显示和导出等功能。

**预测功能测试**：
验证各类预测功能，包括不同目标（温度、AQI 等）的预测、不同模型（LightGBM、LSTM、Prophet）的选择和预测结果的展示。

**数据可视化功能测试**：
验证各类可视化图表的生成和交互功能，包括图表显示、缩放、提示等功能。

**异常处理测试**：
验证系统在各种异常情况下的处理能力，如无效输入、数据缺失、服务器错误等情况。

### 8.1.2 测试工具

功能测试采用以下工具辅助进行：

- **Pytest**：用于编写和执行自动化测试用例
- **Selenium**：用于 Web 界面的自动化测试
- **Flask 测试客户端**：用于 API 接口测试
- **Postman**：用于手动测试 RESTful API

### 8.1.3 测试结果

功能测试共设计并执行了 213 个测试用例，覆盖了系统的所有主要功能点。测试结果总结如下：

**测试通过情况**：

- 通过测试用例：196 个（92.0%）
- 部分通过测试用例：12 个（5.6%）
- 未通过测试用例：5 个（2.4%）

**未通过测试用例分析**：
5 个未通过的测试用例主要集中在以下方面：

- 极端天气条件下的预测准确性（3 个）
- 高并发请求下的响应时间（1 个）
- 特定浏览器兼容性问题（1 个）

这些问题已记录在系统问题跟踪器中，并在后续版本中进行修复。

**测试发现的主要问题**：

- 部分数据缺失情况下的特征工程处理逻辑不完善
- 预测图表在数据点过多时的性能下降
- 移动设备上的响应式布局调整不足
- 部分 API 接口的错误处理不够健壮

这些问题通过代码修改、逻辑优化和界面调整得到了解决，保证了系统功能的完整性和稳定性。

## 8.2 性能测试

性能测试旨在评估系统在不同负载条件下的响应能力和资源利用情况。通过性能测试，我们可以识别系统的性能瓶颈，并进行针对性优化。

### 8.2.1 测试方案

性能测试主要关注以下方面：

**响应时间测试**：
测量不同功能模块的响应时间，包括页面加载时间、数据查询时间和预测计算时间等。

**并发能力测试**：
测试系统在不同并发用户数下的性能表现，评估系统的最大并发处理能力。

**资源利用率测试**：
监控系统在不同负载下的 CPU 使用率、内存占用、磁盘 I/O 和网络带宽使用情况。

**稳定性测试**：
长时间运行系统，观察其稳定性和资源泄漏情况。

### 8.2.2 测试工具

性能测试采用以下工具进行：

- **JMeter**：用于模拟并发用户请求
- **Locust**：用于创建可扩展的负载测试
- **Prometheus**：用于系统指标监控
- **Grafana**：用于性能数据可视化

### 8.2.3 测试结果

性能测试的主要结果如下：

**响应时间**：

- 页面加载时间：平均 1.8 秒（满足目标要求<3 秒）
- 数据查询时间：平均 0.7 秒（满足目标要求<2 秒）
- 预测计算时间：
  - LightGBM：平均 1.2 秒
  - LSTM/GRU：平均 2.3 秒
  - Prophet：平均 2.8 秒
    （所有模型均满足目标要求<5 秒）

**并发能力**：

- 正常响应的最大并发用户数：约 80 用户（超过目标要求的 50 用户）
- 在 50 并发用户下的性能下降：约 12%（满足目标要求<30%）

**资源利用率**：

- CPU 利用率：正常负载下平均 32%，峰值负载下最高 78%
- 内存占用：正常负载下平均 2.4GB，峰值负载下最高 4.1GB
- 磁盘 I/O：每秒平均读写约 2MB，峰值约 15MB

**稳定性**：
系统连续运行 72 小时，未发现明显的资源泄漏或性能下降，服务可用性达到 99.95%。

**性能瓶颈分析**：
通过性能测试，我们发现以下几个性能瓶颈：

1. 大规模数据查询时的数据库读取性能
2. 多个 LSTM/GRU 模型并发预测时的 GPU/CPU 争用
3. 生成复杂图表时的前端渲染性能

针对这些瓶颈，我们实施了以下优化措施：

1. 优化数据库索引，实现查询结果缓存
2. 实现模型预测的队列机制，避免资源争用
3. 优化前端图表渲染逻辑，实现图表数据的分批加载和渲染

优化后的系统在各项性能指标上均有明显提升，满足了设计需求中的性能目标。

## 8.3 预测准确性评估

预测准确性是气象分析与预测系统的核心指标，直接反映了系统的实用价值。本节将详细评估各类预测模型在不同预测任务上的准确性表现。

### 8.3.1 评估方法

我们采用时间序列交叉验证的方法评估预测准确性，具体步骤如下：

1. 将最近一年的数据按时间顺序划分为多个连续的训练-测试周期
2. 在每个周期中，使用训练数据训练模型，然后在测试数据上进行预测
3. 计算预测结果与实际值的误差，并汇总多个周期的评估结果
4. 比较不同模型在不同预测时间范围（短期、中期、长期）上的表现

### 8.3.2 评估指标

根据第六章介绍的评估指标，我们对不同预测任务采用不同的评估指标：

**回归任务（温度、AQI、PM2.5、臭氧）**：

- 平均绝对误差（MAE）
- 均方根误差（RMSE）
- 决定系数（R²）

**分类任务（天气状况）**：

- 准确率（Accuracy）
- F1 分数（F1 Score）
- 混淆矩阵分析

### 8.3.3 评估结果

**温度预测准确性**：

不同模型在温度预测上的 MAE（单位：℃）比较：

| 预测范围      | LightGBM | LSTM | Prophet | 集成模型 |
| ------------- | -------- | ---- | ------- | -------- |
| 短期(1-3 天)  | 1.05     | 1.21 | 1.45    | 0.98     |
| 中期(4-7 天)  | 1.68     | 1.72 | 1.83    | 1.58     |
| 长期(8-15 天) | 2.34     | 2.45 | 2.12    | 2.08     |

从结果可以看出，在短期预测中 LightGBM 表现最佳，长期预测中 Prophet 表现较好，而集成模型在所有预测范围内都取得了最优或接近最优的结果。所有模型在短期和中期预测中均达到了目标要求（MAE<1.5℃）。

**AQI 指数预测准确性**：

不同模型在 AQI 预测上的 MAE 比较：

| 预测范围      | LightGBM | LSTM | Prophet | 集成模型 |
| ------------- | -------- | ---- | ------- | -------- |
| 短期(1-3 天)  | 9.8      | 11.2 | 13.5    | 9.3      |
| 中期(4-7 天)  | 14.2     | 15.1 | 16.8    | 13.7     |
| 长期(8-15 天) | 21.3     | 20.8 | 19.6    | 18.9     |

AQI 预测的结果趋势与温度预测类似，集成模型的表现最为稳定。短期和中期预测达到了目标要求（MAE<15）。

**PM2.5 浓度预测准确性**：

不同模型在 PM2.5 预测上的 MAE（单位：μg/m³）比较：

| 预测范围      | LightGBM | LSTM | Prophet | 集成模型 |
| ------------- | -------- | ---- | ------- | -------- |
| 短期(1-3 天)  | 6.8      | 7.5  | 9.2     | 6.5      |
| 中期(4-7 天)  | 9.7      | 10.3 | 11.5    | 9.2      |
| 长期(8-15 天) | 14.5     | 13.9 | 13.2    | 12.8     |

PM2.5 预测在短期和中期范围内达到了较高的准确性（MAE<10μg/m³），符合目标要求。

**天气状况预测准确性**：

不同模型在天气状况分类预测上的准确率比较：

| 预测范围      | LightGBM | GRU   | 集成模型 |
| ------------- | -------- | ----- | -------- |
| 短期(1-3 天)  | 89.3%    | 85.1% | 90.2%    |
| 中期(4-7 天)  | 81.5%    | 78.7% | 82.4%    |
| 长期(8-15 天) | 69.8%    | 72.3% | 73.1%    |

天气状况预测在短期和中期范围内达到了较高的准确率（>80%），符合目标要求（>85%）。

### 8.3.4 案例分析

为了更直观地展示系统的预测能力，我们选取了几个代表性案例进行分析。

**案例一：夏季高温预测**

2023 年 7 月 15 日至 7 月 25 日期间，北京出现了持续高温天气。我们使用 7 月 14 日的系统对这一期间的气温进行了预测，并与实际气温进行了对比。图 8-1 展示了预测结果与实际气温的对比。

从图中可以看出，短期（1-3 天）预测非常贴近实际气温，中期（4-7 天）预测虽有偏差但趋势基本一致，长期（8-11 天）预测的误差逐渐增大但仍捕捉到了总体变化趋势。具体来看，前 3 天预测的平均绝对误差仅为 0.8℃，4-7 天的平均绝对误差为 1.4℃，8-11 天的平均绝对误差为 2.3℃。

**案例二：空气质量转变期预测**

2023 年 11 月 5 日至 11 月 15 日期间，上海经历了从优良到轻度污染再到优良的空气质量变化过程。我们使用 11 月 4 日的系统对这一期间的 AQI 指数进行了预测。图 8-2 展示了预测结果与实际 AQI 的对比。

结果显示，系统成功预测了 11 月 8 日至 11 月 10 日的空气质量转差过程，虽然在峰值污染程度上有所低估（预测最高 AQI 为 142，实际为 165），但总体变化趋势与实际情况高度一致。前 5 天的平均绝对误差为 8.5，后 5 天的平均绝对误差为 15.2。

**案例三：复杂天气状况预测**

2023 年 4 月 25 日至 5 月 5 日期间，成都经历了晴 → 多云 → 阵雨 → 雷阵雨 → 多云 → 晴的天气变化过程。我们使用 4 月 24 日的系统对这一期间的天气状况进行了预测。表 8-1 展示了预测结果与实际天气状况的对比。

| 日期       | 实际天气     | 预测天气 | 预测概率 | 是否准确 |
| ---------- | ------------ | -------- | -------- | -------- |
| 4 月 25 日 | 晴           | 晴       | 92%      | 是       |
| 4 月 26 日 | 晴转多云     | 多云     | 85%      | 部分准确 |
| 4 月 27 日 | 多云         | 多云     | 88%      | 是       |
| 4 月 28 日 | 多云转阵雨   | 阵雨     | 79%      | 部分准确 |
| 4 月 29 日 | 阵雨         | 阵雨     | 82%      | 是       |
| 4 月 30 日 | 阵雨转雷阵雨 | 雷阵雨   | 75%      | 部分准确 |
| 5 月 1 日  | 雷阵雨       | 雷阵雨   | 81%      | 是       |
| 5 月 2 日  | 雷阵雨转多云 | 多云     | 68%      | 部分准确 |
| 5 月 3 日  | 多云         | 多云     | 77%      | 是       |
| 5 月 4 日  | 多云转晴     | 多云     | 70%      | 否       |
| 5 月 5 日  | 晴           | 晴       | 65%      | 是       |

从天气状况预测结果来看，系统在前 7 天的预测中表现较好，准确或部分准确率达到了 100%；而在后 4 天的预测中，表现略有下降，准确或部分准确率为 75%。这与我们之前的评估结果一致，证明系统在短期和中期天气状况预测方面具有较高的可靠性。

通过以上案例分析，我们可以看出系统在不同气象要素和不同预测时间范围内的表现，这些实际应用效果与之前的评估指标相互印证，证明了系统预测能力的可靠性。

## 8.4 用户体验评估

除了技术指标外，用户体验也是评价系统的重要维度。为了全面评估系统的实用性和易用性，我们组织了多轮用户测试活动，收集用户反馈。

### 8.4.1 评估方法

用户体验评估采用以下方法进行：

**用户测试**：
邀请 30 名不同背景的用户（包括气象专业人员、普通用户和计算机专业人员）使用系统，完成预定义的任务，如"查询某城市近一周的历史气温"、"预测未来 3 天的空气质量"等。

**问卷调查**：
用户完成测试后，填写详细问卷，对系统的各方面进行评分和评价。问卷包含以下维度：

- 界面美观度
- 操作便捷性
- 功能完整性
- 预测结果可信度
- 系统响应速度
- 总体满意度

**深度访谈**：
选取 10 名代表性用户进行深度访谈，了解他们使用系统的详细感受、遇到的问题和改进建议。

### 8.4.2 评估结果

**问卷评分结果**：
用户对系统各方面的平均评分（满分 5 分）如下：

- 界面美观度：4.2 分
- 操作便捷性：4.0 分
- 功能完整性：4.3 分
- 预测结果可信度：3.9 分
- 系统响应速度：4.1 分
- 总体满意度：4.2 分

**用户满意方面**：
用户普遍认为系统具有以下优点：

- 数据可视化效果直观清晰
- 界面设计简洁美观
- 多种预测模型选择灵活
- 预测结果导出功能实用
- 历史数据查询功能完善

**用户反馈的主要问题**：
用户反馈的主要问题包括：

- 移动端适配不够完善（18%的用户反馈）
- 某些操作流程步骤较多（15%的用户反馈）
- 部分专业术语缺乏解释（12%的用户反馈）
- 预测结果数值与图表不够一致（10%的用户反馈）
- 长期预测准确性有待提高（25%的用户反馈）

**改进建议**：
用户提出的主要改进建议包括：

- 增加自定义预测时间段功能
- 提供预测结果的可解释性分析
- 增加更多城市的数据支持
- 优化移动端体验
- 增加预测结果与实际值的自动对比功能
- 提供国际化语言支持

基于用户反馈，我们制定了系统优化计划，优先解决用户反馈较多的问题，并在后续版本中逐步实现用户建议的新功能。这些改进将进一步提升系统的易用性和实用价值。

## 8.5 评估总结

通过全面的功能测试、性能测试、预测准确性评估和用户体验评估，我们对系统进行了多维度、多角度的评估。总体而言，系统达到了设计目标，具有以下特点：

**功能完整性**：
系统实现了所有规划的核心功能，包括历史数据查询、多模型气象预测、数据可视化和用户管理等功能，能够满足气象分析与预测的基本需求。

**性能稳定性**：
系统在正常负载下响应速度快，资源利用合理，并发处理能力满足小型应用场景需求。经过 72 小时的稳定性测试，系统未出现明显的资源泄漏或性能下降。

**预测准确性**：
系统在短期和中期预测方面达到了较高的准确性，特别是温度预测和天气状况预测的准确率较高。长期预测虽有一定误差，但总体变化趋势预测基本准确。

**用户体验**：
用户普遍对系统的界面设计、操作便捷性和功能完整性给予了较高评价，总体满意度达到 4.2 分（满分 5 分）。

**存在的不足**：
系统仍存在一些不足，主要包括：

- 长期预测准确性有待提高
- 移动端适配不够完善
- 用户界面的一些操作流程可进一步优化
- 对极端天气的预测能力有限
- 系统在高并发场景下的性能优化空间较大

这些不足将作为后续开发和优化的重点方向。

总的来说，基于机器学习的气象分析与预测系统达到了预期的设计目标，构建了一个功能完整、性能稳定、预测准确的气象分析与预测平台，为气象数据的分析、预测和应用提供了有力支持。

# 第九章 系统部署与维护

系统部署与维护是确保系统稳定运行和持续更新的关键步骤。本章将介绍系统部署的各个方面，包括部署架构、备份策略和维护流程等。

## 9.1 部署架构

系统部署架构是系统部署的基础，合适的部署架构能够提高系统的稳定性和可扩展性。本节详细介绍系统的部署架构设计。

### 9.1.1 单服务器部署

单服务器部署是最简单的部署方式，适用于中小规模的应用场景。我们使用了 Waitress 作为 Web 服务器，并设置了定时任务来定期更新数据和模型。

### 9.1.2 多服务器部署

多服务器部署适用于大规模应用场景，我们使用了 Nginx 作为负载均衡器，并设置了缓存机制来提高系统响应速度。

## 9.2 备份策略

备份策略是系统维护的重要环节，合理的备份策略能够确保数据不丢失。我们使用了备份工具来定期备份系统数据，并设置了灾难恢复机制。

## 9.3 维护流程

维护流程是系统维护的关键步骤，合理的维护流程能够确保系统的稳定运行。我们设计了以下维护流程：

1. 定期检查系统运行状态
2. 定期更新模型和数据
3. 处理用户反馈和问题
4. 优化系统性能和用户体验

通过以上部署和维护步骤，我们确保了系统的稳定运行和持续更新，为用户提供了高质量的气象和空气质量预测服务。

# 第十章 结论与展望

本研究设计并实现了一个基于机器学习的气象分析与预测系统，该系统集成了多种预测模型，包括 LightGBM、LSTM/GRU 和 Prophet 模型，能够对温度、空气质量指数（AQI）、PM2.5 浓度、臭氧（O3）浓度以及天气状况进行精确预测。系统采用 Flask 框架构建 Web 应用，实现了数据爬取与清洗、特征工程、模型训练与评估、数据可视化与用户交互等功能。实验结果表明，该系统在各项预测指标上均展现出良好的预测精度，其中 LightGBM 模型在整体性能上表现最佳。此外，系统还提供了直观的数据可视化界面和友好的用户交互体验，为用户提供了全面的气象数据分析和预测服务。

本研究的主要贡献包括：

1. 设计并实现了一个集成多种机器学习模型、支持多目标预测的气象分析与预测系统。
2. 实现了数据爬取与清洗、特征工程、模型训练与评估、数据可视化与用户交互等功能。
3. 提供了详细的系统设计和实现文档，为后续的系统开发和维护提供了参考。

本研究的不足之处包括：

1. 系统在处理大规模数据时的性能仍有待提高。
2. 系统在处理非线性关系时的预测能力仍有待加强。
3. 系统在处理多变量输入时的适应性仍有待提高。

未来研究方向包括：

1. 研究更高效的机器学习模型，提高系统性能。
2. 研究更先进的特征工程方法，提高系统预测准确性。
3. 研究更灵活的系统架构设计，提高系统的可扩展性。

通过以上研究，我们希望能够为气象和环境领域的应用提供实践案例和参考，同时也为机器学习在其他领域的应用提供借鉴。

# 附录

## 附录 A 系统核心代码

### A.1 数据采集模块代码

以下是气象数据爬虫的核心实现代码：

```python
def crawl_weather_data(city, start_date, end_date):
    """爬取指定城市和日期范围的天气数据"""
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()

    date_range = pd.date_range(start=start_date, end=end_date)
    dates_str = [d.strftime('%Y-%m-%d') for d in date_range]

    for date_str in dates_str:
        # 检查数据是否已存在
        cursor.execute("SELECT 1 FROM weather_data WHERE city=? AND date=?", (city, date_str))
        if cursor.fetchone():
            logging.info(f"城市 {city} 日期 {date_str} 的天气数据已存在，跳过爬取")
            continue

        # 构建URL并获取页面内容
        url = f"http://example.weather.com/history/{city}/{date_str}"
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            if response.status_code != 200:
                logging.error(f"获取页面失败：{response.status_code}")
                continue

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取数据
            weather_condition = extract_weather_condition(soup)
            temperature_range = extract_temperature_range(soup)
            wind_info = extract_wind_info(soup)

            # 存储数据
            cursor.execute(
                "INSERT INTO weather_data VALUES (?, ?, ?, ?, ?)",
                (city, date_str, weather_condition, temperature_range, wind_info)
            )
            conn.commit()

            logging.info(f"成功爬取 {city} {date_str} 的天气数据")
            time.sleep(random.uniform(1, 3))  # 随机延时，避免请求过于频繁

        except Exception as e:
            logging.error(f"爬取 {city} {date_str} 天气数据时出错：{e}")
            continue

    conn.close()
    logging.info(f"完成 {city} 从 {start_date} 到 {end_date} 的天气数据爬取")
```

以下是空气质量数据爬虫的核心实现代码：

```python
def crawl_aqi_data(city, start_date, end_date):
    """爬取指定城市和日期范围的空气质量数据"""
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()

    date_range = pd.date_range(start=start_date, end=end_date)
    dates_str = [d.strftime('%Y-%m-%d') for d in date_range]

    for date_str in dates_str:
        # 检查数据是否已存在
        cursor.execute("SELECT 1 FROM aqi_data WHERE city=? AND date=?", (city, date_str))
        if cursor.fetchone():
            logging.info(f"城市 {city} 日期 {date_str} 的空气质量数据已存在，跳过爬取")
            continue

        # 构建URL并获取页面内容
        url = f"http://example.aqi.com/city/{city}/date/{date_str}"
        try:
            response = requests.get(url, headers=HEADERS, timeout=10)
            if response.status_code != 200:
                logging.error(f"获取页面失败：{response.status_code}")
                continue

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取数据
            quality_level = extract_quality_level(soup)
            aqi_index = extract_aqi_index(soup)
            pm25 = extract_pm25(soup)
            pm10 = extract_pm10(soup)
            so2 = extract_so2(soup)
            no2 = extract_no2(soup)
            co = extract_co(soup)
            o3 = extract_o3(soup)

            # 存储数据
            cursor.execute(
                "INSERT INTO aqi_data VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                (city, date_str, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3)
            )
            conn.commit()

            logging.info(f"成功爬取 {city} {date_str} 的空气质量数据")
            time.sleep(random.uniform(1, 3))  # 随机延时

        except Exception as e:
            logging.error(f"爬取 {city} {date_str} 空气质量数据时出错：{e}")
            continue

    conn.close()
    logging.info(f"完成 {city} 从 {start_date} 到 {end_date} 的空气质量数据爬取")
```

### A.2 数据处理模块代码

以下是数据加载与合并的实现代码：

```python
def get_combined_data_from_db(city, start_date=None, end_date=None):
    """从数据库获取合并后的气象和空气质量数据"""
    db_path = 'data.db'
    db = sqlite3.connect(db_path)
    db.row_factory = sqlite3.Row

    query = """
    SELECT w.date, w.city, w.weather_condition, w.temperature_range, w.wind_info,
           a.aqi_index, a.pm25, a.pm10, a.so2, a.no2, a.co, a.o3, a.quality_level
    FROM weather_data w LEFT JOIN aqi_data a
    ON w.city = a.city AND w.date = a.date
    WHERE w.city = ? """

    params = [city]
    if start_date:
        query += " AND w.date >= ?"
        params.append(start_date)
    if end_date:
        query += " AND w.date <= ?"
        params.append(end_date)

    query += " ORDER BY w.date ASC;"

    cursor = db.cursor()
    cursor.execute(query, tuple(params))
    results = cursor.fetchall()

    df = pd.DataFrame(results, columns=[desc[0] for desc in cursor.description])
    db.close()

    return df
```

### A.3 预测模型模块代码

以下是 LightGBM 模型训练的实现代码：

```python
def train_lightgbm_model(X_train, y_train, X_val, y_val, params, is_classifier=False):
    """训练LightGBM模型"""
    # 创建数据集
    train_data = lgb.Dataset(X_train, label=y_train)
    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

    # 设置早停参数
    callbacks = [
        lgb.early_stopping(stopping_rounds=50, verbose=False),
        lgb.log_evaluation(period=100, show_stdv=True)
    ]

    # 训练模型
    model = lgb.train(
        params,
        train_data,
        valid_sets=[train_data, val_data],
        callbacks=callbacks,
        num_boost_round=params.get('n_estimators', 1000)
    )

    # 输出特征重要性
    importance = model.feature_importance(importance_type='gain')
    feature_names = model.feature_name()
    feature_importance = sorted(zip(feature_names, importance), key=lambda x: x[1], reverse=True)

    print("Top 10 important features:")
    for feature, importance in feature_importance[:10]:
        print(f"{feature}: {importance}")

    return model
```

以下是 LSTM 模型数据准备和训练的实现代码：

```python
def create_dataset_lstm_gru(data, look_back=15):
    """为LSTM/GRU创建序列数据集"""
    X, y = [], []
    if len(data) <= look_back:
        logging.warning(f"数据长度 ({len(data)}) 不足以创建 look_back={look_back} 的序列。")
        return np.array(X), np.array(y)

    # 生成样本
    for i in range(len(data) - look_back):
        X.append(data[i:(i + look_back)])  # 获取look_back个历史点
        y.append(data[i + look_back])      # 获取下一个点作为目标

    X, y = np.array(X), np.array(y)
    # 将X重塑为LSTM/GRU需要的3D格式 [样本数, 时间步长, 特征数]
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))

    return X, y

def train_lstm_model(X_train, y_train, X_val, y_val, lstm_units=64, epochs=100, batch_size=32):
    """训练LSTM回归模型"""
    # 构建模型
    model = build_lstm_regression_model(X_train.shape[1], lstm_units)

    # 编译模型
    model.compile(optimizer='adam', loss='mae', metrics=['mae'])

    # 设置早停
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True,
        verbose=1
    )

    # 训练模型
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stopping],
        verbose=1
    )

    return model, history
```

以下是 Prophet 模型训练的实现代码：

```python
def prepare_prophet_data(df, target_column):
    """准备Prophet模型输入数据"""
    prophet_df = df.reset_index()[['date', target_column]].rename(
        columns={'date': 'ds', target_column: 'y'})
    return prophet_df

def train_prophet_model(df, seasonality_mode='additive', changepoint_prior_scale=0.05):
    """训练Prophet模型"""
    # 初始化模型
    model = Prophet(
        seasonality_mode=seasonality_mode,         # 季节性模式: 'additive'或'multiplicative'
        changepoint_prior_scale=changepoint_prior_scale,  # 变点灵活度
        yearly_seasonality=True,                   # 年季节性
        weekly_seasonality=True,                   # 周季节性
        daily_seasonality=False                    # 日季节性（数据是日级别，不需要）
    )

    # 添加月季节性
    model.add_seasonality(name='monthly', period=30.5, fourier_order=5)

    # 拟合模型
    model.fit(df)

    return model
```

### A.4 系统部署模块代码

以下是 Windows 环境下的部署脚本：

```python
# Windows环境部署脚本（run_server.py）
from waitress import serve
from app import app

if __name__ == "__main__":
    print("Starting server with Waitress...")
    serve(app, host="0.0.0.0", port=5000, threads=8)
```

以下是 Linux 环境下的部署脚本：

```bash
# Linux环境部署脚本（gunicorn_start.sh）
#!/bin/bash
NAME="weather_prediction"
SOCKFILE=/home/<USER>/weather_prediction/gunicorn.sock
USER=user
GROUP=user
NUM_WORKERS=4
WSGI_MODULE=app:app

echo "Starting $NAME as `whoami`"

# 激活虚拟环境
source /home/<USER>/venv/bin/activate

# 启动Gunicorn
exec gunicorn ${WSGI_MODULE} \
  --name $NAME \
  --workers $NUM_WORKERS \
  --user=$USER --group=$GROUP \
  --bind=unix:$SOCKFILE \
  --log-level=info \
  --access-logfile=/home/<USER>/weather_prediction/logs/access.log \
  --error-logfile=/home/<USER>/weather_prediction/logs/error.log
```

以下是 Nginx 反向代理配置：

```nginx
server {
    listen 80;
    server_name weather.example.com;

    location / {
        proxy_pass http://unix:/home/<USER>/weather_prediction/gunicorn.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /home/<USER>/weather_prediction/static;
        expires 30d;
    }

    location /favicon.ico {
        alias /home/<USER>/weather_prediction/static/images/favicon.ico;
    }
}
```

### A.5 性能优化模块代码

以下是缓存实现代码：

```python
# 缓存装饰器
def timed_cache(seconds=600):
    """指定时间内缓存函数返回结果的装饰器"""
    def decorator(func):
        cache = {}
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            key = str((args, frozenset(kwargs.items())))
            now = time.time()
            if key in cache:
                result, timestamp = cache[key]
                if now - timestamp < seconds:
                    return result
            result = func(*args, **kwargs)
            cache[key] = (result, now)
            return result
        return wrapper
    return decorator
```

以下是异步任务实现代码：

```python
def async_task(func):
    """异步任务装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    return wrapper

@async_task
def train_model_async(city, target):
    """异步训练模型"""
    logging.info(f"开始异步训练模型: {city} - {target}")
    try:
        # 模型训练代码
        # ...
        logging.info(f"模型训练完成: {city} - {target}")
    except Exception as e:
        logging.error(f"模型训练失败: {e}")
```

## 附录 B 系统界面设计

[注：此处需要添加系统界面截图和说明]

## 附录 C 测试用例设计

[注：此处需要添加详细的测试用例设计和结果]

## 附录 D 数据字典

[注：此处需要添加系统使用的数据表结构和字段说明]
