# 5.2 爬虫数据的清洗

从网络爬虫获取的原始数据往往包含格式不一致、缺失、异常等问题，不能直接用于后续的分析和模型训练。因此，数据清洗是数据预处理流程中至关重要的一环。本系统的数据清洗逻辑主要体现在数据爬取脚本（`weather_spider.py`和`aqi_spider.py`）中的解析部分以及`utils.py`中的数据处理函数中。

### 缺失值处理

在数据爬取和解析过程中，可能会遇到某些字段数据缺失的情况。例如，部分日期的污染物浓度可能未记录完整。

- **天气数据**：`weather_spider.py`在解析温度（`temperature_range`）和风力信息（`wind_info`）时，如果遇到无法解析或数据不完整的情况，会赋予一个默认值或留空，后续在`utils.py`的`parse_temperature`和`parse_wind_details`函数中进一步处理。例如，`parse_temperature`函数在仅有最高温或最低温时，会尝试用已有温度填充另一缺失温度，如果两者都存在则计算平均温。若完全无法解析，则返回`np.nan`。
- **AQI 数据**：`aqi_spider.py`中的`safe_float`和`safe_int`函数用于安全地转换数值，如果原始数据为空或为'-'，则返回`None`或指定的默认值，这有助于后续识别和处理缺失值。在数据入库时，这些字段可能为 NULL。图 5.2-1 展示了 AQI 数据中 PM2.5 缺失值的处理前后对比。

在`utils.py`的`get_combined_data_from_db`函数加载数据后，会进行更全面的缺失值检查。对于数值型特征，在模型训练前，一般采用均值、中位数填充，或使用更复杂的插值方法。对于分类特征，则可能用众数填充。

图 5.2-1：PM2.5 缺失值处理前后对比示例

### 异常值检测与修正

异常值可能由数据源错误、爬虫解析错误或传感器故障等原因造成。

- **解析阶段的初步处理**：在`weather_spider.py`和`aqi_spider.py`中，通过正则表达式和严格的格式校验，可以过滤掉一部分明显格式错误的异常数据。
- **`utils.py`中的处理**：`parse_temperature`函数对温度字符串进行解析时，会尝试处理多种格式，对于无法识别的格式会标记为`np.nan`，这间接起到了异常值过滤的作用。例如，如果温度范围出现非数字字符，会被视为解析失败。
- **后续统计检测**：在数据加载到 Pandas DataFrame 后，可以通过统计方法（如 IQR、Z-score）检测数值型特征的异常值。例如，某天的 PM2.5 浓度远超正常范围，可能被识别为异常。图 5.2-2 展示了温度数据异常值检测的示例。

对于检测到的异常值，通常采用替换（如用均值、中位数替换）或删除（如果异常值占比很小）等策略进行修正。

图 5.2-2：温度数据异常值检测示例

### 数据标准化

数据标准化主要包括统一数据格式和数据类型，确保数据的一致性。

- **日期格式标准化**：`weather_spider.py`中的`parse_date_weather`函数和`aqi_spider.py`中的`parse_date_aqi`函数负责将爬取到的各种日期字符串（如"2023 年 10 月 1 日"、"20231001"）统一转换为`YYYY-MM-DD`的标准格式。
- **文本数据规范化**：`utils.py`中的`map_weather_condition`函数用于将复杂多样的天气状况描述（如"晴转多云"、"小雨/中雨"）映射为预定义的、更简洁的天气类别（如"晴"、"多云"、"小雨"）。如图 5.2-3 所示为天气状况映射表的一部分。
- **数值类型转换**：`aqi_spider.py`中的`safe_float`和`safe_int`函数确保 AQI 相关数值（如 PM2.5, PM10 等）被正确转换为浮点型或整型。`utils.py`中的`parse_temperature`和`parse_wind_details`也进行了相应的数值转换和计算（如平均温度）。
- **单位统一**：爬取的数据在源网站上单位较为统一（如温度为摄氏度，污染物浓度单位固定），系统在解析时保留了这些单位，或在计算中隐式处理了单位问题（如平均温度的计算）。

图 5.2-3：天气状况描述映射表示例

### 数据库存储实现

清洗和标准化后的数据最终会存储到 SQLite 数据库（默认为`data.db`）中，便于后续的查询和分析。`database.py`文件定义了数据库连接的获取和关闭逻辑。

- **表结构设计**：`weather_spider.py`和`aqi_spider.py`中的`check_tables_exist`函数会检查`weather_data`和`aqi_data`表是否存在，如果不存在则会创建。表结构设计考虑了数据类型、非空约束以及唯一性约束（例如`UNIQUE(city, date)`确保同一城市同一天的数据不重复）。图 5.2-4 展示了`weather_data`表和`aqi_data`表的结构。
- **数据插入**：爬虫脚本使用`INSERT OR IGNORE INTO`或先查询后插入/更新的逻辑将数据写入数据库。`OR IGNORE`确保了当违反唯一性约束（即数据已存在）时，不会抛出错误而是忽略该条插入操作。部分脚本在`FORCE_REPARSE`模式下会先执行`DELETE`操作清空旧数据。
- **数据一致性**：通过唯一约束保证了核心数据（天气和 AQI）的记录唯一性，为后续数据合并和分析打下了基础。

图 5.2-4：数据库表结构（weather_data 和 aqi_data）示意图
