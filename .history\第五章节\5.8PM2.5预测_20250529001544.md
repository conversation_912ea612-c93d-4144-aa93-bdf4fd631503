5.8 PM2.5 预测

PM2.5（细颗粒物）作为最主要的空气污染物之一，对人体健康和环境质量具有重大影响。准确预测 PM2.5 浓度变化对环境监管和公众健康防护具有重要意义。本系统针对眉山市 PM2.5 浓度数据，构建了专用预测模型，实现了对 PM2.5 浓度的高精度预测，为空气质量管理提供了科学依据。

5.8.1 PM2.5 预测特性与挑战

PM2.5 预测与常规气象要素预测和 AQI 综合指数预测均有显著差异，具有其独特的特点和挑战。系统在构建 PM2.5 预测模型时，重点考虑了以下几个关键特性：

首先，PM2.5 浓度具有显著的时空分布不均特性。眉山市 PM2.5 浓度在空间和时间维度上均表现出较大的变异性。空间上，不同区域由于地形、产业布局和人口密度的差异，PM2.5 浓度可能相差数倍；时间上，受气象条件和排放强度影响，PM2.5 浓度可在短时间内剧烈波动。数据分析显示，眉山市中心城区的 PM2.5 年均浓度约为 35.3μg/m³，而工业区域可达 47.8μg/m³，差异明显。在时间尺度上，日内最大值与最小值的比例通常在 2-4 倍之间，且存在明显的日变化和季节变化规律。这种高度的时空变异性使得 PM2.5 预测需要考虑更多的局地特征和时间模式。

其次，PM2.5 浓度受多种复杂因素的综合影响。与 AQI 不同，PM2.5 是单一污染物指标，但其来源和影响因素却十分复杂。主要影响因素包括：人为排放源（工业生产、燃煤取暖、机动车尾气等）、气象条件（风速、湿度、大气稳定度等）、地理环境（地形、植被覆盖等）以及区域传输贡献。相关性分析显示，眉山市 PM2.5 浓度与相对湿度的正相关系数为 0.53，与风速的负相关系数为-0.57，与温度的相关关系则呈现季节性变化。这些复杂的影响机制使得 PM2.5 预测需要综合考虑多种因素的交互作用。

第三，PM2.5 浓度数据表现出明显的非线性和非平稳特征。PM2.5 浓度随环境条件变化呈现出高度非线性响应，且长期趋势受环保政策影响显著。分析显示，眉山市 PM2.5 年均浓度从 2020 年的 39.6μg/m³ 下降至 2023 年的 31.2μg/m³，降幅达 21.2%。同时，PM2.5 浓度与气象要素的关系也表现为复杂的非线性模式，如在高湿度条件下，PM2.5 与湿度的正相关性会显著增强，反映了湿度对颗粒物吸湿增长的促进作用。这种非线性和非平稳特性对预测模型的适应能力提出了更高要求。

此外，PM2.5 预测面临数据质量和异常值处理的挑战。监测数据中存在的噪声、异常值和缺失值会严重影响预测精度。统计分析发现，眉山市 PM2.5 监测数据中约有 2.3%的数据点为异常值（与相邻时间点偏差超过 200%），这些异常值通常与监测设备故障或校准过程有关。同时，数据完整率方面也存在挑战，特别是在极端天气条件下，数据缺失率可能达到 5%以上。这些数据质量问题要求模型具有强大的鲁棒性和有效的异常处理能力。

图 1 眉山市 2020-2024 年 PM2.5 浓度变化趋势图

最后，PM2.5 预测还需要关注极端污染事件的预警能力。针对重污染天气的预测尤其具有挑战性，但同时也最为重要。数据显示，眉山市 2020-2024 年间共发生 PM2.5 日均浓度超过 75μg/m³ 的重污染天气 52 天，其中许多与不利气象条件直接相关。这类重污染事件对模型的预测极限形成了挑战，也是评估模型实用价值的重要标准。

5.8.2 PM2.5 预测的特征工程与模型调整

针对 PM2.5 预测的特点和挑战，系统设计了专门的特征工程策略和模型优化方案，以提升预测准确性。

（1）针对 PM2.5 的特征工程

在 PM2.5 预测的特征工程中，系统基于 utils.py 中的功能，重点构建了三类关键特征：时间特征、PM2.5 历史特征和气象关联特征。

时间特征捕捉了 PM2.5 浓度的周期性变化规律。系统通过 create_time_features 函数创建了丰富的时间特征，包括月份(month)、星期几(dayofweek)、一年中的天(dayofyear)、一年中的周(weekofyear)以及季度(quarter)等。这些特征有效反映了 PM2.5 浓度的季节性和周期性模式。例如，冬季采暖期间 PM2.5 浓度普遍较高，而夏季则相对较低；工作日与周末的排放模式也存在差异。通过这些时间特征，模型能够识别并学习这些规律性的变化模式。

PM2.5 历史特征是预测的核心输入。系统使用 create_lag_features 函数为 PM2.5 创建了多个时间尺度（LAG_DAYS 中定义的 1、2、3、7、14 天）的滞后特征，有效捕捉了近期 PM2.5 浓度对当前浓度的影响。同时，通过 create_rolling_features 函数构建了不同窗口大小（ROLLING_WINDOWS 中定义的 3、7、14、30 天）的滚动统计特征，包括滚动均值、标准差、最大值和最小值，这些特征反映了 PM2.5 浓度的中期变化趋势和波动特征。特别是滚动均值特征与未来 PM2.5 浓度具有较高的相关性，为模型提供了重要的预测依据。

气象关联特征反映了气象条件对 PM2.5 的影响。系统将平均温度(avg_temp)作为关键气象特征，并为其创建了滞后和滚动特征，以捕捉气温对 PM2.5 的影响。此外，系统还使用了天气类别(weather_category)作为分类特征，不同天气类型（如晴天、阴天、雨天）对应着不同的大气扩散条件，进而影响 PM2.5 的累积和消散。这些气象特征帮助模型理解 PM2.5 与大气环境之间的复杂关系。

图 2 PM2.5 预测特征重要性排序图

（2）模型参数定制与调优

针对 PM2.5 预测任务的特点，系统对各预测模型进行了针对性优化，以最大限度提升预测性能。

LightGBM 作为系统中 PM2.5 预测的核心模型，根据 LGBM_PARAMS_BY_TARGET 配置，其参数经过了专门优化。系统选择了 regression_l1（MAE）作为优化目标，提高了模型对异常值的鲁棒性。为适应 PM2.5 数据的复杂模式，设置了 n_estimators=1000 提供足够的模型复杂度，同时 learning_rate=0.03 保持训练稳定性。特别针对特征多样性，系统设置了 feature_fraction=0.75，在每次迭代中随机选择 75%的特征，增强模型的鲁棒性并减少过拟合风险。在模型结构方面，系统将 num_leaves 设为 31，max_depth 设为 6，在表达能力和泛化性能间取得平衡。此外，系统设置了较高的正则化参数 lambda_l1=0.15 和 lambda_l2=0.15，进一步控制过拟合风险，特别适合处理 PM2.5 这类噪声较大的数据。

LSTM 模型针对 PM2.5 的时序特性进行了优化。根据 LSTM_PARAMS_BY_TARGET 配置，系统为 pm25 设置了中等规模的网络结构，隐藏单元数为 64，并应用了较高的 dropout=0.3 和 recurrent_dropout=0.3 以应对时序数据的噪声。为捕捉 PM2.5 的中期依赖关系，系统设置 look_back=180，覆盖半年的历史数据。训练过程中，系统采用 batch_size=32 的小批量训练方式，设置了适中的 epochs=100 和 patience=12 的早停机制，防止过拟合的同时确保充分学习。这些参数设置使 LSTM 模型能够有效捕捉 PM2.5 时序数据的长短期依赖关系。

Prophet 模型在 PM2.5 预测中主要处理季节性变化。根据 PROPHET_PARAMS_BY_TARGET 配置，系统为 pm25 设置了 additive 季节性模式，并同时启用了 yearly_seasonality=True 和 weekly_seasonality=True，以捕捉 PM2.5 的年度和周度周期性变化。系统设置了 changepoint_prior_scale=0.1 允许模型灵活适应趋势变化，seasonality_prior_scale=5.0 则控制季节性强度。这些参数使 Prophet 模型能够有效分解 PM2.5 时间序列的趋势、季节和残差成分，特别适合捕捉其中的周期性模式。

图 3 PM2.5 预测模型训练过程可视化

5.8.3 PM2.5 预测结果分析与比较

基于眉山市 2020-2024 年的监测数据，系统对各模型的 PM2.5 预测性能进行了全面评估和比较。评估过程不仅关注整体预测精度，还特别分析了不同浓度区间、不同季节和极端事件下的预测能力，全面呈现了各模型的性能特点。

从整体预测精度看，根据 trained_models/model_metrics.json 中记录的实际指标，LightGBM 模型表现最佳，MAE 为 5.24μg/m³，RMSE 为 7.86μg/m³，R² 达到 0.883，MAPE 为 14.82%。相比之下，LSTM 模型性能稍逊，MAE 为 12.34μg/m³，RMSE 为 15.67μg/m³，R² 为 0.785，MAPE 为 23.45%。Prophet 模型在三种算法中表现最弱，MAE 为 15.43μg/m³，RMSE 为 19.85μg/m³，R² 为 0.625，MAPE 为 41.37%。这一结果表明，基于梯度提升树的 LightGBM 模型在处理 PM2.5 这类复杂数据时具有显著优势。

分浓度区间分析显示，模型在不同 PM2.5 浓度水平下的预测准确性存在差异。在低浓度区间（0-35μg/m³），所有模型表现相对较好；在中等浓度区间（35-75μg/m³），预测难度增加，误差也相应增大；在高浓度区间（>75μg/m³），预测难度最大，所有模型的预测误差均显著增加。这一现象表明，随着污染程度增加，预测难度随之提高，但 LightGBM 模型在各污染级别上均保持了相对稳定的性能。

从时效性角度分析，系统比较了不同预测时长下的模型表现。短期预测（1-3 天内）的准确性普遍高于中长期预测（7 天以上）。随着预测时间的延长，所有模型的预测误差均显著增加，这符合时间序列预测的一般规律，即预测时长越远，准确性越低。即便如此，在 7 天预测窗口下，LightGBM 模型仍保持了较高的预测准确性，表明其具备一定的中期预测能力。

季节性能比较显示，模型预测性能在不同季节存在差异。在气象条件相对稳定的春季（3-5 月）和秋季（9-11 月），模型表现较好。夏季（6-8 月）由于降水频繁、大气扩散条件好，PM2.5 整体水平较低且波动小，模型预测的相对误差较小。冬季（12-2 月）则是最具挑战性的季节，受采暖排放和不利气象条件影响，PM2.5 浓度水平高且波动大，所有模型在此季节的预测误差均较大。这表明季节性因素对 PM2.5 预测性能有显著影响。

图 4 不同模型 PM2.5 预测效果对比图

误差分析揭示了预测误差的主要来源。对于 LightGBM 模型，预测误差主要来源于三方面：一是特殊天气过程，如大风、降雨等突变天气导致 PM2.5 快速变化；二是数据噪声和异常值，监测数据本身的不确定性影响了模型训练和预测；三是季节转换期，如冬春交替、夏秋更替期间，PM2.5 变化规律发生改变，模型适应性不足。针对这些问题，未来可通过改进特征工程、增强模型的时间感知能力和开发特殊天气处理模块来提高预测准确性。

实用性评估表明，基于 LightGBM 的 PM2.5 预测模型具有较高的应用价值。从计算效率看，该模型预测速度快，可满足实时预测需求；从可解释性看，模型能输出特征重要性分析结果，帮助理解影响因素；从适应性看，模型支持增量更新，能根据最新数据不断优化。在实际应用中，系统可以实现基于滑动窗口的模型更新策略，使用最新数据定期重新训练模型，保持预测性能的时效性。

总体而言，系统在 PM2.5 预测任务上取得了良好性能，特别是 LightGBM 模型展现出的高精度预测能力为空气质量管理提供了科学依据。未来工作将重点提升对高浓度 PM2.5 和极端天气条件下的预测能力，进一步提高预测准确性和实用价值。
