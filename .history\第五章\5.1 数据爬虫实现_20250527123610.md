# 5.1 数据爬虫实现

本系统的数据爬虫模块负责从互联网上自动获取天气和空气质量历史数据，为后续的分析和预测提供原始数据支持。爬虫模块主要由针对天气数据的`weather_spider.py`和针对空气质量数据的`aqi_spider.py`两个核心脚本组成。

### 网络爬虫架构设计

系统采用了基于 Python 的`requests`库进行 HTTP 请求发送，以及`BeautifulSoup4`库进行 HTML 页面解析的经典爬虫架构。整体流程包括目标 URL 构造、网页内容获取、HTML 内容解析、数据提取与结构化，最后将数据存入 SQLite 数据库。如图 5.1-1 所示为网络爬虫的整体架构。

详细设计上，爬虫首先根据预设的城市列表（例如"眉山"）和年份、月份范围，动态生成目标数据页面的 URL。随后，模拟浏览器行为发送 GET 请求获取网页 HTML 内容。获取到 HTML 后，利用`BeautifulSoup`强大的解析能力，根据网页结构定位到包含目标数据的具体 HTML 元素，并提取所需信息。

图 5.1-1：网络爬虫整体架构图

### 数据源适配与解析

本系统选用的主要数据源为天气后报网(tianqihoubao.com)，该网站提供了较为全面的历史天气和空气质量数据。

对于天气数据（`weather_spider.py`），主要爬取每日的最高气温、最低气温以及天气状况描述。例如，温度数据通常以"25℃/15℃"的形式存在，天气状况则为文本描述如"晴/多云"。解析时需特别处理这种复合格式。

对于空气质量数据（`aqi_spider.py`），主要爬取每日的 AQI 指数、PM2.5、PM10、SO2、NO2、CO、O3 等主要污染物浓度。这些数据通常以表格形式呈现在网页中。

两个爬虫脚本中均包含了对日期格式的解析逻辑（例如`parse_date_weather`和`parse_date_aqi`函数），将网页中的日期字符串统一转换为`YYYY-MM-DD`标准格式。同时，使用`remove_space`等辅助函数清除数据中的不必要字符和空格，确保数据纯净。图 5.1-2 展示了天气数据源页面的一个示例，图 5.1-3 则为 AQI 数据源页面的示例。

图 5.1-2：天气数据源页面示例
图 5.1-3：AQI 数据源页面示例

### 爬取策略与稳定性保障

为确保数据爬取的稳定性和高效性，并尽量避免对目标网站造成过大负担，系统实施了以下策略：

1.  **遍历爬取**：根据预设的城市、年份和月份列表（例如`CITY_MAP`, `YEARS`, `MONTHS`常量）自动遍历生成所有目标月份的 URL 进行爬取。
2.  **模拟浏览器行为**：在发送 HTTP 请求时，设置了`User-Agent`等请求头信息，以模拟真实浏览器访问。
3.  **请求重试机制**：针对网络波动或请求失败的情况，设计了重试机制（例如`weather_spider.py`中的`attempt < 3`循环），在失败后会进行多次尝试。
4.  **随机延时**：在连续请求之间加入随机延时（例如`time.sleep(random.uniform(1, 3))`），以降低请求频率。
5.  **数据去重与增量更新**：在数据入库前，会检查数据库中是否已存在对应城市和日期的记录（例如通过`UNIQUE(city, date)`约束和查询`SELECT COUNT(*)`），避免重复爬取。提供了`FORCE_REPARSE`选项，允许强制重新爬取指定月份的数据，并在重新爬取前删除旧数据。
6.  **调试信息保存**：在`DEBUG_MODE`开启时，会将爬取到的原始 HTML 内容保存到本地文件（`debug_html`目录下的`weather_{city}_{year_month}.html`或`aqi_{city}_{year_month}.html`），便于问题排查和解析逻辑调试，如图 5.1-4 所示。

这些策略共同保障了爬虫模块能够长期稳定地获取所需数据。

图 5.1-4：爬虫任务执行日志与调试 HTML 文件示例
