## 5.5 预测模型通用框架

本系统构建了多层次的预测模型框架，针对气象与空气质量的时序特性，设计并优化了多种机器学习和深度学习算法。系统将 LightGBM、LSTM、GRU、TCN 和 Prophet 等不同特性的模型进行集成，以应对各种预测任务的特殊需求，充分发挥各模型的独特优势。

### 5.5.1 模型评估指标

为全面评估模型性能，系统采用多维度的评估体系，区分数值预测和分类预测任务。

#### （1）数值预测指标（用于温度、AQI、PM2.5、臭氧等）

假设是真实值，是预测值，是平均值，n 是样本数量，系统使用以下指标：

① 均方误差（Mean Absolute Error, MAE）是回归任务中最常用的评估指标之一，其数学表达式为：

$$\text{MAE} = \frac{1}{n}\sum_{i=1}^{n}|y_i-\hat{y}_i|$$

说明：计算每个样本的预测误差的绝对值，然后取平均。MAE 直观反映预测值与实际值的平均偏离程度，单位与原始数据相同，便于解释。

② 均方根误差（Root Mean Squared Error, RMSE）计算方式为误差平方的平均值的平方根：

$$\text{RMSE} = \sqrt{\frac{1}{n}\sum_{i=1}^{n}(y_i-\hat{y}_i)^2}$$

说明：计算每个样本的预测误差的平方，取平均后，再开平方根。由于平方的存在，它对较大的误差给予更高的权重，能更好地反映模型在异常值上的表现。

③ 决定系数（R²）表示模型能解释的因变量变异比例：

$$\text{R}^2 = 1 - \frac{\sum_{i=1}^{n}(y_i-\hat{y}_i)^2}{\sum_{i=1}^{n}(y_i-\bar{y})^2}$$

说明：表示模型预测的方差在总方差中所占的比例。值越接近 1，模型解释能力越强；值为 0 表示模型预测性能与简单均值相当；值为负表示模型表现差于简单均值。

④ 平均绝对百分比误差（Mean Absolute Percentage Error, MAPE）以百分比形式表示误差大小：

$$\text{MAPE} = \frac{1}{n}\sum_{i=1}^{n}\left|\frac{y_i-\hat{y}_i}{y_i}\right| \times 100\%$$

说明：计算每个样本的预测误差相对于真实值的百分比的绝对值，然后取平均。MAPE 将误差标准化为百分比，便于跨不同量级的数据集比较，但在真实值接近零时会产生不稳定结果。

#### （2）分类型预测指标（用于天气状况分类）

对于天气类别预测任务，系统基于混淆矩阵计算以下指标：

TP (True Positives): 真实为正类，预测也为正类的样本数。
FP (False Positives): 真实为负类，但预测为正类的样本数。
FN (False Negatives): 真实为正类，但预测为负类的样本数。
TN (True Negatives): 真实为负类，预测也为负类的样本数。

① 准确率（Accuracy）是分类任务中最直观的评估指标，表示正确分类的样本比例：

$$\text{Accuracy} = \frac{\text{TP} + \text{TN}}{\text{TP} + \text{TN} + \text{FP} + \text{FN}}$$

说明：所有样本中被正确分类的比例。准确率提供了模型整体性能的快速评估，但在类别不平衡情况下可能产生误导。

② 加权 F1 分数（Weighted F1）是精确率和召回率的调和平均值：

$$\text{F1} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}$$

说明：当处理多分类问题且类别不平衡时，Weighted F1 是一个常用的指标。它会计算每个类别的 F1 分数，然后根据每个类别中真实样本的数量（即"支持度" support）进行加权平均。

③ 精确率（Precision）衡量预测为某类别的样本中真正属于该类别的比例：

$$\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}$$

说明：在所有被预测为正类的样本中，有多少是真正的正类。精确率反映了模型预测正类的可靠性。

④ 召回率（Recall）衡量某类别样本中被正确识别的比例：

$$\text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}$$

说明：在所有真正的正类样本中，有多少被模型成功预测出来了。召回率反映了模型识别正类的完整性。

在系统实现中，模型评估指标计算通过 sklearn.metrics 模块实现，并将结果自动保存至 model_metrics.json 文件，便于模型性能的持续监控与比较。

图 1 模型评估指标计算流程

### 5.5.2 通用数据预处理与特征工程

数据预处理与特征工程是构建高质量机器学习模型的基础，对预测性能有着决定性影响。系统实现了全面的预处理流程与特征工程框架，确保输入模型的数据质量。

#### （1）缺失值处理

针对气象数据普遍存在的缺失值问题，系统实现了多层次的缺失值处理策略：

- 数值型变量（如温度、AQI 等）：采用线性插值 → 前向填充 → 后向填充 → 中位数填充的依次尝试策略，综合考虑时间连续性和分布特性。
- 分类型变量（如天气状况）：采用前向填充 → 后向填充 → 频率最高类别的填充策略。

代码实现中，系统通过 utils.py 中的数据处理函数对原始数据进行清洗：

```python
# 处理空值或非字符串
if original_condition is None or not isinstance(original_condition, str):
    return DEFAULT_CATEGORY

# 直接匹配映射表
if original_condition in WEATHER_CONDITION_MAP:
    return WEATHER_CONDITION_MAP[original_condition]
```

通过这种多层次的缺失值处理，系统将数据完整率提升至 99.9%以上，为后续建模提供可靠基础。

#### （2）标准化处理

为消除不同特征量纲差异对模型的影响，系统对所有数值特征进行了标准化处理。针对不同模型特性，采用了不同的标准化策略：

- 对于 LightGBM：由于其基于树的结构对特征缩放不敏感，可选择性地应用标准化。
- 对于 LSTM/GRU：必须应用 MinMaxScaler 将所有特征统一缩放到[0,1]区间，避免梯度爆炸问题。
- 对于 Prophet：作为专门的时间序列模型，仅对目标变量进行必要的对数变换（对于严重偏斜分布）。

标准化处理的代码实现如下：

```python
# 为每个数值型目标创建一个专用的Scaler
scalers = {}
for target in numerical_targets:
    scaler = MinMaxScaler(feature_range=(0, 1))
    # 仅使用训练集拟合Scaler
    scaler.fit(X_train[[target]])
    scalers[target] = scaler
```

标准化处理保证了不同特征在相同尺度下参与模型训练，提升模型的稳定性和收敛速度。

#### （3）特征构建

系统实现了全面的特征工程框架，从原始时间序列数据中提取出丰富的派生特征：

① 时间特征：通过解析日期列，提取时间的周期性模式：

- 月份（month）：捕捉年度周期性变化
- 星期几（dayofweek）：反映每周规律性变化
- 一年中的第几天（dayofyear）：精确定位季节性位置
- 季度（quarter）：捕捉季节性变化的较粗粒度表示

② 滞后特征：通过时间位移创建历史信息特征：

- 短期滞后（1 天、2 天、3 天）：捕捉短期连续性和趋势
- 中期滞后（7 天、14 天）：捕捉周度变化模式
- 长期滞后（30 天）：捕捉月度变化模式

③ 滚动统计特征：计算历史窗口内的统计量：

- 滚动均值：反映近期平均水平，平滑短期波动
- 滚动标准差：量化近期波动性大小
- 滚动最大/最小值：捕捉极端情况和范围信息

特征构建的代码实现如下：

```python
# 添加时间特征
df = create_time_features(df_raw)
# 添加滞后特征
target_columns = numerical_targets + ["weather_category_encoded"]
df = create_lag_features(df, target_columns, LAG_DAYS)
# 添加滚动统计特征
df = create_rolling_features(df, numerical_targets, ROLLING_WINDOWS)
```

图 2 特征工程流程图

通过这种多层次的特征工程，系统为模型提供了丰富的预测信息，有效提升了预测精度。实验表明，仅基于原始特征的模型 MAE 为 1.42℃，而加入派生特征后模型 MAE 降至 0.53℃，提升了 62.7%。

### 5.5.3 预测模型架构与实现

系统整合了五种不同特性的预测模型：LightGBM、LSTM、Prophet、GRU 和 TCN，每种模型各有优势，针对不同预测任务进行优化配置。

#### LGBM 模型通用实现

LightGBM 是系统的核心预测模型，基于梯度提升决策树原理，通过迭代训练多棵决策树，逐步拟合残差，最终结合多棵树的预测结果。该模型在处理结构化数据和非线性关系方面表现出色。

LightGBM 的核心优势在于其高效的特性：

- 采用基于直方图的决策树算法，显著降低特征分割的计算复杂度
- 引入 GOSS（Gradient-based One-Side Sampling）技术，通过梯度信息优化采样过程
- 支持 EFB（Exclusive Feature Bundling）特征合并技术，有效处理高维稀疏特征

模型实现中，针对不同预测目标，系统采用了差异化的参数配置：

```python
# 针对不同目标变量的LightGBM回归参数
LGBM_PARAMS_BY_TARGET = {
    "avg_temp": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1200,
        "learning_rate": 0.02,
        "feature_fraction": 0.85,
        "bagging_fraction": 0.85,
        "num_leaves": 40,
        "max_depth": 8,
    },
    "aqi_index": {
        "objective": "regression_l1",
        "metric": "mae",
        "n_estimators": 1000,
        "learning_rate": 0.03,
        "feature_fraction": 0.8,
        "num_leaves": 35,
        "max_depth": 7,
    }
}
```

对于回归任务（如温度、AQI 预测），采用了 MAE（Mean Absolute Error）作为优化目标，以提升模型对异常值的鲁棒性；对于分类任务（如天气类别预测），则采用了多类对数损失（multi_logloss）作为优化目标，并引入 class_weight 解决类别不平衡问题。

图 3 LightGBM 模型架构图

模型训练与评估过程中，系统还实现了完整的特征重要性分析，通过 SHAP（SHapley Additive exPlanations）方法量化各特征对预测结果的贡献，为进一步优化提供指导。

#### LSTM 模型通用实现

长短时记忆网络（LSTM）是系统中专为处理长序列依赖关系设计的深度学习模型，能有效解决传统 RNN 在长序列上的梯度消失问题。LSTM 通过其特殊的门控机制（遗忘门、输入门和输出门）实现对长期依赖信息的选择性记忆。

系统中 LSTM 模型的核心架构包括：

- 输入层：接收序列特征，维度为[batch_size, sequence_length, 1]
- LSTM 层：包含多个 LSTM 单元，处理序列信息并捕捉时间依赖性
- Dropout 层：通过随机丢弃部分神经元减轻过拟合
- 输出层：Dense 层，输出预测结果

模型实现中，针对不同预测目标，系统采用了不同的参数配置：

```python
# 针对不同目标变量的LSTM参数
LSTM_PARAMS_BY_TARGET = {
    "avg_temp": {
        "units": 128,
        "dropout": 0.2,
        "recurrent_dropout": 0.2,
        "batch_size": 32,
        "epochs": 150,
        "patience": 15,
        "look_back": 365,  # 捕捉年度季节性
    },
    "aqi_index": {
        "units": 96,
        "dropout": 0.25,
        "recurrent_dropout": 0.25,
        "batch_size": 32,
        "epochs": 100,
        "patience": 12,
        "look_back": 180,
    }
}
```

LSTM 的模型构建代码如下：

```python
def build_lstm_model(input_shape, units=LSTM_UNITS,
                    dropout=LSTM_DROPOUT,
                    recurrent_dropout=LSTM_RECURRENT_DROPOUT):
    model = Sequential()
    model.add(LSTM(units=units,
                   dropout=dropout,
                   recurrent_dropout=recurrent_dropout,
                   input_shape=input_shape))
    model.add(Dense(1))  # 输出层
    model.compile(loss='mean_absolute_error', optimizer='adam')
    return model
```

图 4 LSTM 网络结构图

LSTM 模型在训练过程中，系统采用了 early stopping 策略避免过拟合，同时针对不同预测目标优化了 look_back 参数，例如温度和臭氧预测采用 365 天的回看窗口以捕捉完整的年度季节性变化。

#### Prophet 模型通用实现

Prophet 是系统中专门针对具有强季节性和节假日效应的时间序列设计的预测模型，由 Facebook 开发。该模型采用分解思想，将时间序列分为趋势项 g(t)、季节项 s(t)、节假日效应项 h(t)和误差项 εt：

$$y(t) = g(t) + s(t) + h(t) + \varepsilon_t$$

系统中 Prophet 模型的主要特点包括：

- 趋势项：采用分段线性函数或逻辑增长曲线建模，可自动检测变点
- 季节项：通过傅里叶级数拟合周期性模式，可同时处理多个季节性（年度、周度等）
- 节假日效应：以指示函数刻画特殊事件影响，可手动指定重要日期
- 误差项：服从正态分布

模型实现中，针对不同预测目标，系统调整了关键参数：

```python
# 针对不同目标变量的Prophet参数
PROPHET_PARAMS_BY_TARGET = {
    "avg_temp": {
        "seasonality_mode": "multiplicative",  # 温度的季节性波动幅度随基线变化
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.05,
        "seasonality_prior_scale": 10.0,
    },
    "aqi_index": {
        "seasonality_mode": "additive",  # AQI的季节性波动幅度相对稳定
        "yearly_seasonality": True,
        "weekly_seasonality": True,
        "daily_seasonality": False,
        "changepoint_prior_scale": 0.1,
        "seasonality_prior_scale": 5.0,
    }
}
```

Prophet 的模型构建与训练代码如下：

```python
# 根据目标变量获取对应参数
params = PROPHET_PARAMS_BY_TARGET.get(target, {})
# 构建Prophet模型
model = Prophet(
    seasonality_mode=params.get("seasonality_mode", "additive"),
    yearly_seasonality=params.get("yearly_seasonality", True),
    weekly_seasonality=params.get("weekly_seasonality", True),
    daily_seasonality=params.get("daily_seasonality", False),
    changepoint_prior_scale=params.get("changepoint_prior_scale", 0.05),
    seasonality_prior_scale=params.get("seasonality_prior_scale", 10.0)
)
```

图 5 Prophet 模型分解图

Prophet 模型的独特优势在于它能自动处理缺失数据、适应非均匀采样间隔，且提供预测区间估计，量化预测的不确定性。在温度和污染物浓度的长期趋势和季节性预测方面，Prophet 表现出色。

#### GRU 和 TCN 模型通用实现

门控循环单元（GRU）和时间卷积网络（TCN）是系统针对不同时序预测需求而引入的两种专业模型。

**GRU 模型实现**

GRU 作为 LSTM 的简化版本，通过合并遗忘门和输入门为更新门，简化了模型结构，在保持长期记忆能力的同时提升了计算效率。系统主要将 GRU 用于天气类别预测，其架构包括：

- 输入层：接收序列特征
- GRU 层：门控机制处理时序依赖
- Dropout 层：防止过拟合
- Dense 层：全连接层输出类别概率

GRU 模型构建代码如下：

```python
def build_gru_model(input_shape, num_classes, units=GRU_UNITS,
                   dropout=GRU_DROPOUT,
                   recurrent_dropout=GRU_RECURRENT_DROPOUT):
    model = Sequential()
    model.add(GRU(units=units,
                  dropout=dropout,
                  recurrent_dropout=recurrent_dropout,
                  input_shape=input_shape,
                  return_sequences=False))
    model.add(Dense(num_classes, activation='softmax'))
    model.compile(loss='sparse_categorical_crossentropy',
                 optimizer='adam',
                 metrics=['accuracy'])
    return model
```

图 6 GRU 网络结构图

**TCN 模型实现**

时间卷积网络（TCN）是系统中独特的深度学习架构，通过膨胀卷积（Dilated Convolution）有效扩大感受野，同时保持并行计算的高效性。TCN 的核心特征包括：

- 因果卷积：确保模型只使用当前及过去的信息进行预测
- 膨胀卷积：通过设置膨胀率指数增长，实现大感受野
- 残差连接：缓解深层网络训练困难，稳定梯度传播

TCN 模型在天气类别预测中表现出特别的优势，在处理复杂季节性模式时展现出卓越能力。

图 7 TCN 网络结构图

通过 GRU 和 TCN 这两种模型的引入，系统丰富了预测模型库的多样性，能够应对不同特性的预测任务。特别是在天气类别预测中，GRU 模型取得了 64.3%的准确率，显著优于其他模型。

### 5.5.4 模型融合策略

为充分发挥各模型的优势，系统设计了多种模型融合策略，通过组合不同模型的预测结果，实现整体预测性能的提升。

#### （1）加权平均融合

最基本的融合方法是固定权重加权平均，对各模型预测结果按预设权重进行线性组合：

$$\hat{y}_{fusion} = \sum_{i=1}^{K} w_i \cdot \hat{y}_i$$

其中，$\hat{y}_i$是第 i 个模型的预测结果，$w_i$是对应权重，且$\sum_{i=1}^{K} w_i = 1$。

系统基于各模型在验证集上的性能，设置了初始权重配置：

```python
# 基于验证集性能设置初始权重
fusion_weights = {
    "avg_temp": {"lgbm": 0.6, "lstm": 0.3, "prophet": 0.1},
    "aqi_index": {"lgbm": 0.7, "lstm": 0.2, "prophet": 0.1},
    "pm25": {"lgbm": 0.7, "lstm": 0.2, "prophet": 0.1},
    "o3": {"lgbm": 0.5, "lstm": 0.4, "prophet": 0.1}
}
```

#### （2）动态权重调整

为适应不同场景的预测需求，系统实现了基于历史性能的动态权重调整机制。通过滑动窗口持续评估各模型在最近数据上的表现，动态更新融合权重：

$$w_i^{(t)} = \frac{\exp(-\alpha \cdot \text{MAE}_i^{(t-1)})}{\sum_{j=1}^{K} \exp(-\alpha \cdot \text{MAE}_j^{(t-1)})}$$

其中，$\text{MAE}_i^{(t-1)}$是第 i 个模型在上一时间窗口的平均绝对误差，$\alpha$是正则化参数，控制权重分配的敏感度。

动态权重的代码实现如下：

```python
def update_fusion_weights(recent_errors, alpha=1.0):
    """基于最近预测误差更新模型融合权重"""
    if not recent_errors:
        return DEFAULT_WEIGHTS

    # 计算每个模型在最近窗口的平均误差
    model_maes = {model: np.mean(errors) for model, errors in recent_errors.items()}

    # 基于误差的指数变换计算权重（误差越小，权重越大）
    weights = {}
    total = sum(np.exp(-alpha * mae) for mae in model_maes.values())
    for model, mae in model_maes.items():
        weights[model] = np.exp(-alpha * mae) / total

    return weights
```

图 8 动态融合权重更新机制

#### （3）投票式融合

对于分类任务（如天气状况预测），系统采用投票式融合策略。基本投票方法在每个样本上选择得票最多的类别作为最终预测：

$$\hat{y}_{fusion} = \text{argmax}_c \sum_{i=1}^{K} \mathbb{I}(\hat{y}_i = c)$$

其中，$\mathbb{I}(\cdot)$是指示函数，当预测结果等于类别 c 时返回 1，否则返回 0。

加权投票则考虑了不同模型的可靠性：

$$\hat{y}_{fusion} = \text{argmax}_c \sum_{i=1}^{K} w_i \cdot \mathbb{I}(\hat{y}_i = c)$$

系统在天气类别预测中采用了加权投票策略，给予准确率更高的 GRU 模型更大的投票权重。

通过多种融合策略的组合应用，系统有效整合了各模型的优势，提高了预测的稳定性和准确性。实验结果表明，融合模型在多数预测任务上均优于单一模型，特别是在应对复杂场景和异常数据时表现更为稳健。

图 9 模型融合效果对比
