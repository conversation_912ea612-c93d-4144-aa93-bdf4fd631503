# 温度预测图表生成代码

## 生成温度预测相关的所有图表

```python
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import lightgbm as lgb
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from tensorflow.keras.models import load_model
import sqlite3
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

# 创建温度图表保存目录
os.makedirs('temperature_charts', exist_ok=True)

def load_temperature_data():
    """加载温度相关数据"""
    try:
        conn = sqlite3.connect('data.db')
        query = """
        SELECT date, avg_temp, humidity, pressure, wind_speed, wind_direction
        FROM weather_data 
        WHERE avg_temp IS NOT NULL
        ORDER BY date
        """
        df = pd.read_sql_query(query, conn)
        df['date'] = pd.to_datetime(df['date'])
        conn.close()
        return df
    except Exception as e:
        print(f"数据库连接错误: {e}")
        # 生成示例数据
        dates = pd.date_range('2020-01-01', '2023-12-31', freq='D')
        np.random.seed(42)
        df = pd.DataFrame({
            'date': dates,
            'avg_temp': np.random.normal(15, 10, len(dates)),
            'humidity': np.random.randint(30, 90, len(dates)),
            'pressure': np.random.normal(1013, 20, len(dates)),
            'wind_speed': np.random.exponential(3, len(dates)),
            'wind_direction': np.random.randint(0, 360, len(dates))
        })
        return df

def generate_temp_lgbm_charts():
    """生成温度预测的LightGBM图表"""
    print("生成温度预测LightGBM图表...")
    
    # 加载数据
    df = load_temperature_data()
    
    # 加载模型
    try:
        lgbm_model = joblib.load('trained_models/lgbm_avg_temp_model.pkl')
        scaler = joblib.load('trained_models/scaler_avg_temp.pkl')
    except Exception as e:
        print(f"未找到温度LightGBM模型文件: {e}")
        print("跳过LightGBM图表生成")
        return
    
    # 准备特征数据
    from utils import create_time_features, create_lag_features, create_rolling_features
    
    df_features = create_time_features(df)
    df_features = create_lag_features(df_features, ['avg_temp'], [1, 2, 3, 7, 14])
    df_features = create_rolling_features(df_features, ['avg_temp'], [3, 7, 14, 30])
    df_clean = df_features.dropna()
    
    if len(df_clean) < 100:
        print("数据不足，跳过LightGBM图表生成")
        return
    
    # 准备特征和目标
    feature_cols = [col for col in df_clean.columns if col not in ['date', 'avg_temp']]
    X = df_clean[feature_cols]
    y = df_clean['avg_temp']
    
    # 划分训练测试集
    split_idx = int(len(df_clean) * 0.8)
    X_test = X[split_idx:]
    y_test = y[split_idx:]
    dates_test = df_clean['date'].iloc[split_idx:]
    
    # 预测
    y_pred = lgbm_model.predict(X_test)
    
    # 图1: 时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates_test, y_test, label='实际温度', color='blue', alpha=0.7)
    plt.plot(dates_test, y_pred, label='预测温度', color='red', alpha=0.7)
    plt.title('LightGBM模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('temperature_charts/lgbm_time_series.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图2: 特征重要性图
    plt.figure(figsize=(12, 8))
    feature_importance = lgbm_model.feature_importances_
    feature_names = X.columns
    
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False).head(20)
    
    sns.barplot(data=importance_df, y='feature', x='importance', palette='viridis')
    plt.title('LightGBM模型温度预测特征重要性图', fontsize=16, fontweight='bold')
    plt.xlabel('重要性分数', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    plt.tight_layout()
    plt.savefig('temperature_charts/lgbm_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图3: 实际vs预测散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred, alpha=0.6, color='blue')
    
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
    
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    r2 = r2_score(y_test, y_pred)
    
    plt.title('LightGBM模型实际温度与预测温度对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际温度 (℃)', fontsize=12)
    plt.ylabel('预测温度 (℃)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}', 
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('temperature_charts/lgbm_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图4: 残差分布图
    residuals = y_test - y_pred
    
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('残差分布直方图', fontsize=14, fontweight='bold')
    plt.xlabel('残差 (℃)', fontsize=12)
    plt.ylabel('频次', fontsize=12)
    
    plt.subplot(1, 2, 2)
    plt.scatter(y_pred, residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('残差vs预测值', fontsize=14, fontweight='bold')
    plt.xlabel('预测温度 (℃)', fontsize=12)
    plt.ylabel('残差 (℃)', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('temperature_charts/lgbm_residual_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("LightGBM温度预测图表生成完成")

def generate_temp_lstm_charts():
    """生成温度预测的LSTM图表"""
    print("生成温度预测LSTM图表...")
    
    # 加载数据
    df = load_temperature_data()
    
    # 加载模型
    try:
        lstm_model = load_model('trained_models/lstm_avg_temp_model.keras')
        scaler = joblib.load('trained_models/scaler_avg_temp.pkl')
    except Exception as e:
        print(f"未找到温度LSTM模型文件: {e}")
        print("跳过LSTM图表生成")
        return
    
    # 准备LSTM数据
    temp_data = df['avg_temp'].dropna().values.reshape(-1, 1)
    scaled_data = scaler.transform(temp_data)
    
    def create_sequences(data, seq_length=365):
        X, y = [], []
        for i in range(len(data) - seq_length):
            X.append(data[i:(i + seq_length)])
            y.append(data[i + seq_length])
        return np.array(X), np.array(y)
    
    X, y = create_sequences(scaled_data)
    
    if len(X) < 100:
        print("LSTM数据不足，跳过LSTM图表生成")
        return
    
    # 划分训练测试集
    split_idx = int(len(X) * 0.8)
    X_test, y_test = X[split_idx:], y[split_idx:]
    
    # 预测
    y_pred_scaled = lstm_model.predict(X_test)
    y_pred = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
    
    # 创建日期索引
    dates = df['date'].dropna().iloc[365+split_idx:365+split_idx+len(y_test_actual)]
    
    # 图5: LSTM时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(dates, y_test_actual, label='实际温度', color='blue', alpha=0.7)
    plt.plot(dates, y_pred, label='预测温度', color='red', alpha=0.7)
    plt.title('LSTM模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('temperature_charts/lstm_time_series.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图6: LSTM散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test_actual, y_pred, alpha=0.6, color='blue')
    
    min_val = min(y_test_actual.min(), y_pred.min())
    max_val = max(y_test_actual.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
    
    mae = mean_absolute_error(y_test_actual, y_pred)
    rmse = np.sqrt(mean_squared_error(y_test_actual, y_pred))
    r2 = r2_score(y_test_actual, y_pred)
    
    plt.title('LSTM模型实际温度与预测温度对比散点图', fontsize=16, fontweight='bold')
    plt.xlabel('实际温度 (℃)', fontsize=12)
    plt.ylabel('预测温度 (℃)', fontsize=12)
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nRMSE: {rmse:.2f}\nR²: {r2:.3f}', 
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    plt.tight_layout()
    plt.savefig('temperature_charts/lstm_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图7: LSTM评估指标图
    metrics = ['MAE', 'RMSE', 'R²', 'MAPE']
    mape = np.mean(np.abs((y_test_actual - y_pred) / y_test_actual)) * 100
    values = [mae, rmse, r2, mape]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(metrics, values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
    plt.title('LSTM模型评估指标图', fontsize=16, fontweight='bold')
    plt.ylabel('指标值', fontsize=12)
    
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('temperature_charts/lstm_evaluation_metrics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("LSTM温度预测图表生成完成")

def generate_temp_prophet_charts():
    """生成温度预测的Prophet图表"""
    print("生成温度预测Prophet图表...")
    
    # 加载数据
    df = load_temperature_data()
    
    # 加载模型
    try:
        from prophet import Prophet
        with open('trained_models/prophet_avg_temp_model.json', 'r') as f:
            prophet_model = Prophet()
            prophet_model = prophet_model.from_json(f.read())
    except Exception as e:
        print(f"未找到温度Prophet模型文件: {e}")
        print("跳过Prophet图表生成")
        return
    
    # 准备Prophet数据
    prophet_df = df[['date', 'avg_temp']].dropna().rename(columns={'date': 'ds', 'avg_temp': 'y'})
    
    if len(prophet_df) < 100:
        print("Prophet数据不足，跳过Prophet图表生成")
        return
    
    # 划分训练测试集
    split_idx = int(len(prophet_df) * 0.8)
    test_df = prophet_df[split_idx:]
    
    # 创建未来数据框架
    future = prophet_model.make_future_dataframe(periods=len(test_df))
    forecast = prophet_model.predict(future)
    forecast_test = forecast.iloc[split_idx:]
    
    # 图8: Prophet时间序列预测对比图
    plt.figure(figsize=(15, 8))
    plt.plot(test_df['ds'], test_df['y'], label='实际温度', color='blue', alpha=0.7)
    plt.plot(forecast_test['ds'], forecast_test['yhat'], label='预测温度', color='red', alpha=0.7)
    plt.fill_between(forecast_test['ds'], forecast_test['yhat_lower'], forecast_test['yhat_upper'], 
                     alpha=0.3, color='red', label='预测区间')
    plt.title('Prophet模型温度预测时间序列对比图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('temperature_charts/prophet_time_series.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 图9: Prophet组件分解图
    fig = prophet_model.plot_components(forecast)
    fig.suptitle('Prophet模型温度预测组件分解图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('temperature_charts/prophet_components.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Prophet温度预测图表生成完成")

def generate_basic_temp_charts():
    """生成基本温度数据可视化图表"""
    print("生成基本温度数据可视化图表...")
    
    df = load_temperature_data()
    
    # 图10: 温度时间序列图
    plt.figure(figsize=(15, 8))
    plt.plot(df['date'], df['avg_temp'], label='温度', color='blue', alpha=0.7)
    plt.title('温度时间序列图', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('温度 (℃)', fontsize=12)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('temperature_charts/basic_temperature_series.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("基本温度图表生成完成")

# 主函数
if __name__ == "__main__":
    print("开始生成温度预测图表...")
    
    # 生成所有温度相关图表
    generate_temp_lgbm_charts()
    generate_temp_lstm_charts() 
    generate_temp_prophet_charts()
    generate_basic_temp_charts()
    
    print("温度预测图表生成完成！")
    print("图表保存在 temperature_charts/ 目录中")
    
    # 检查生成的文件
    import os
    chart_files = [f for f in os.listdir('temperature_charts') if f.endswith('.png')]
    print(f"成功生成了 {len(chart_files)} 个温度图表文件:")
    for file in sorted(chart_files):
        print(f"  - temperature_charts/{file}")
```

## 运行说明

1. 将代码保存为 `generate_temperature_charts.py`
2. 运行：`python generate_temperature_charts.py`
3. 图表将保存在 `temperature_charts/` 目录中

## 生成的图表文件

- `lgbm_time_series.png` - LightGBM时间序列对比图
- `lgbm_feature_importance.png` - LightGBM特征重要性图
- `lgbm_actual_vs_predicted.png` - LightGBM散点图
- `lgbm_residual_analysis.png` - LightGBM残差分析图
- `lstm_time_series.png` - LSTM时间序列对比图
- `lstm_actual_vs_predicted.png` - LSTM散点图
- `lstm_evaluation_metrics.png` - LSTM评估指标图
- `prophet_time_series.png` - Prophet时间序列对比图
- `prophet_components.png` - Prophet组件分解图
- `basic_temperature_series.png` - 基本温度时间序列图
