5.5 预测模型通用框架

本系统实现了多种气象和空气质量预测模型，包括平均温度、AQI 指数、PM2.5 浓度、O3 浓度以及天气类别预测。虽然预测目标不同，但这些模型共享相似的数据处理流程、模型构建方法和评估标准。本节详细介绍系统预测模型的通用框架，为后续各具体预测模型的实现奠定基础。

5.5.1 数据预处理与特征工程

所有预测模型的数据预处理遵循统一的流程，主要包括数据清洗、缺失值处理、时间特征提取和特征变换等步骤。系统通过 utils.py 中的数据处理函数实现这些功能，确保所有模型使用一致的数据准备方法。

首先，针对原始数据进行清洗和标准化，包括去除异常值、统一数值单位和格式。系统使用 3σ 原则识别数值型特征的异常点，对于超出合理范围的数据进行修正或移除。对于时间序列数据中的缺失值，系统根据数据特性采用前向填充、后向填充或插值等方法进行补全，确保数据的连续性。

时间特征提取是预测模型的关键环节，系统从日期中提取多维时间特征，包括年份、月份、日期、星期几、季节、是否节假日等。这些时间特征能够捕捉数据的周期性和季节性变化，显著提升模型预测能力。特征提取的核心代码如下所示：

```python
def extract_time_features(df, date_column='date'):
    """从日期列提取时间特征"""
    df['year'] = df[date_column].dt.year
    df['month'] = df[date_column].dt.month
    df['day'] = df[date_column].dt.day
    df['dayofweek'] = df[date_column].dt.dayofweek
    df['quarter'] = df[date_column].dt.quarter
    df['season'] = df['month'].apply(lambda x: (x%12 + 3)//3)
    # 添加节假日标记（简化版）
    holidays = get_holiday_list()
    df['is_holiday'] = df[date_column].isin(holidays).astype(int)
    return df
```

对于类别型特征，系统使用 One-Hot 编码转换为数值特征，便于模型处理。对于数值型特征，系统根据数据分布特性和模型要求，采用标准化(StandardScaler)或最小-最大缩放(MinMaxScaler)等方法进行特征变换，使数据分布更适合模型训练。

特征选择方面，系统采用多种方法识别重要特征，包括相关性分析、特征重要性评估和递归特征消除等。这些方法帮助系统筛选出对预测目标影响最大的特征子集，提高模型效率并降低过拟合风险。

5.5.2 模型选择与架构设计

系统根据预测任务的特性和数据特点，选择了三类主要模型：基于梯度提升的 LightGBM 模型、基于深度学习的 LSTM 模型和基于分解的 Prophet 模型。这三类模型各有优势，适用于不同的预测场景。

LightGBM 模型是系统的主要预测模型，具有训练速度快、内存占用低、预测精度高等优点，特别适合处理包含多种特征的复杂数据集。系统对 LightGBM 模型进行了针对性优化，包括特征工程、参数调优和交叉验证等，以提高预测准确性和稳定性。

LSTM(长短期记忆网络)模型主要用于捕捉时间序列数据的长期依赖关系和复杂模式，尤其适合预测具有明显时间依赖性的目标，如温度和空气质量指标。系统基于 TensorFlow/Keras 实现 LSTM 模型，并通过调整网络层数、神经元数量和激活函数等参数优化模型性能。

Prophet 模型是 Facebook 开发的时间序列预测模型，专门用于捕捉数据的季节性、周期性和趋势性特征，特别适合预测具有明显季节性和趋势性的气象数据。系统利用 Prophet 模型的自动变点检测和季节性分解功能，提高预测的准确性和可解释性。

模型选择遵循以下原则：对于结构化程度高、特征丰富的预测任务，优先选用 LightGBM 模型；对于强时序依赖性的预测任务，优先选用 LSTM 模型；对于具有明显季节性和趋势性的预测任务，优先选用 Prophet 模型。系统还支持多模型融合，结合不同模型的优势，进一步提高预测准确性。

5.5.3 训练流程与参数调优

所有预测模型共享统一的训练流程，包括数据划分、参数调优、模型训练和性能评估等环节。系统通过 train_models.py 脚本实现模型的训练和管理，确保各模型遵循一致的训练标准。

数据划分采用时间序列交叉验证方法，避免未来数据泄露问题。系统根据时间顺序将数据集划分为训练集(80%)和测试集(20%)，并在训练集内使用滚动窗口法进行交叉验证，即使用历史数据预测未来一段时间的数值，然后滚动时间窗口重复此过程。这种方法确保模型能够在真实预测场景下表现良好。

参数调优是模型训练的关键环节，系统采用网格搜索(Grid Search)和贝叶斯优化(Bayesian Optimization)等方法自动寻找最优参数组合。对于 LightGBM 模型，系统主要优化学习率、树的深度、叶子节点数、特征抽样比例等参数；对于 LSTM 模型，系统主要优化层数、神经元数量、dropout 比例、批大小等参数；对于 Prophet 模型，系统主要优化变点检测、季节性组件和趋势组件等参数。

```python
# LightGBM参数调优示例
param_grid = {
    'learning_rate': [0.01, 0.05, 0.1],
    'max_depth': [3, 5, 7, 9],
    'num_leaves': [31, 63, 127],
    'feature_fraction': [0.7, 0.8, 0.9],
    'bagging_fraction': [0.7, 0.8, 0.9],
    'bagging_freq': [5, 10, 20]
}
```

模型训练过程中，系统采用早停(Early Stopping)策略避免过拟合，即当验证集性能连续多轮不再提升时自动停止训练。系统还实现了模型检查点(Checkpoint)功能，定期保存训练过程中的最佳模型状态，防止因意外中断导致训练成果丢失。

训练完成后，系统将模型文件、参数配置和性能指标保存到 trained_models 目录，并生成训练报告，记录训练过程中的关键信息，为后续模型优化和比较提供依据。

5.5.4 模型评估与结果可视化

系统采用多种评估指标对预测模型性能进行全面评估，包括均方误差(MSE)、均方根误差(RMSE)、平均绝对误差(MAE)和决定系数(R²)等。对于分类任务，系统还使用准确率(Accuracy)、精确率(Precision)、召回率(Recall)和 F1 分数等指标。这些指标从不同角度衡量模型性能，提供全面的评估结果。

```python
def evaluate_model(y_true, y_pred, task_type='regression'):
    """评估模型性能"""
    if task_type == 'regression':
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}
    else:  # classification
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')
        return {'Accuracy': accuracy, 'Precision': precision, 'Recall': recall, 'F1': f1}
```

除了数值指标，系统还通过 generate_model_evaluation.py 脚本生成各种可视化图表，直观展示模型性能和预测结果。预测结果可视化主要包括预测值与实际值对比图、误差分布图、特征重要性图和学习曲线图等。这些图表保存在 trained_models/evaluation_plots 目录下，并通过 Web 界面展示给用户。

对比图直观展示预测值与实际值的差异，帮助用户理解模型预测的准确性和局限性。误差分布图显示预测误差的分布特征，帮助识别模型的系统性偏差。特征重要性图展示各特征对预测结果的影响程度，提供模型解释性并指导特征选择和优化。学习曲线图展示模型训练过程中损失函数的变化趋势，帮助判断模型是否存在过拟合或欠拟合问题。

系统还支持模型比较功能，将不同模型(如 LGBM、LSTM、Prophet)的预测结果在同一图表中展示，便于用户比较不同模型的优劣，如图 5.5-1 所示。

图 5.5-1 不同模型预测结果比较

5.5.5 模型部署与应用集成

训练完成的预测模型通过模型部署机制集成到系统中，为 Web 应用提供实时预测服务。系统采用模型序列化方法(如 Pickle 或 Joblib)保存训练好的模型，并通过预测 API 加载模型并执行预测。

```python
def load_model(model_path):
    """加载预训练模型"""
    with open(model_path, 'rb') as f:
        model = joblib.load(f)
    return model

def predict(model, features):
    """使用模型进行预测"""
    return model.predict(features)
```

系统实现了模型版本管理机制，支持多个模型版本并行存在，便于模型升级和回滚。当新模型训练完成且性能超过当前模型时，系统会自动将其设为活动模型，用于实时预测；同时保留旧版本模型，以备回滚需要。

为提高预测服务的可靠性和性能，系统实现了模型缓存机制，避免频繁加载模型文件。系统还实现了批量预测功能，支持一次性预测多个时间点的数据，提高预测效率。

通过以上通用框架，系统实现了高效、准确、可靠的预测服务，为后续各具体预测模型提供了统一的实现基础。后续章节将详细介绍各预测模型在此框架基础上的特定实现和优化。
