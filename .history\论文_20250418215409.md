# 基于机器学习的气象分析与预测系统的设计和实现

## 摘要

本文设计并实现了一个基于机器学习的气象分析与预测系统，该系统能够有效处理和分析气象数据与空气质量数据，并利用多种机器学习算法进行准确的天气和空气质量预测。系统采用了 LightGBM、LSTM、GRU 以及 Prophet 等多种算法模型，实现了对温度、AQI 指数、PM2.5、O3 浓度以及天气状况的预测。同时，系统通过基于 Flask 的 Web 应用提供了用户友好的界面，支持数据可视化和交互式预测分析。实验结果表明，该系统在多个预测任务上均取得了良好的性能，特别是 LightGBM 模型在大多数预测任务中表现最佳。系统采用 SQLite 作为数据存储引擎，减轻了部署负担，提高了系统的便携性。本系统为气象分析和环境监测提供了有效的技术支持，具有实用价值和推广意义。

## 关键词

气象预测、空气质量预测、机器学习、深度学习、LightGBM、LSTM、GRU、Prophet、Web 应用、Flask

## 第一章 绪论

### 1.1 研究背景与意义

气象预测和空气质量分析对人们的日常生活和工业生产具有重要影响。气象状况影响着人们的出行决策、农业生产、交通运输等诸多方面；而空气质量则直接关系到公众健康和生活质量。随着气候变化和环境污染问题日益突出，准确的气象和空气质量预测变得尤为重要。

传统的气象预测方法主要依赖于物理模型，需要大量的计算资源和专业知识。而机器学习方法的发展为气象预测提供了新的技术路径，能够从历史数据中学习复杂的模式和关系，提高预测的准确性和效率。特别是在当前大数据时代，各类气象和空气质量数据的积累为基于数据驱动的预测方法创造了条件。

本研究针对眉山市开发的气象分析与预测系统，旨在整合多种机器学习模型，提供精准的气象和空气质量预测服务。通过 Web 界面，系统可以为公众提供直观易懂的预测结果和建议，帮助人们更好地安排日常活动，减少环境污染对健康的影响。此外，系统的设计思路和实现方法也可为其他城市的气象预测系统开发提供借鉴。

### 1.2 国内外研究现状

国内外对气象预测的研究已经从传统的物理模型逐渐转向机器学习和深度学习方法。目前，时间序列分析、集成学习和深度学习在气象预测领域取得了显著成果。

在传统时间序列模型方面，ARIMA（自回归积分移动平均模型）长期以来被广泛应用于气象预测。随着计算能力的提升，更复杂的模型如 Prophet 被开发出来。Facebook 开发的 Prophet 模型在处理具有季节性和多种趋势的时间序列数据方面表现出色，适合气象数据这类具有明显周期性的数据。

在集成学习领域，随机森林和梯度提升树等算法因其出色的性能和抗噪能力，被广泛应用于气象预测。特别是 LightGBM 作为梯度提升框架的高效实现，因其训练速度快、预测精度高，近年来在气象和环境预测领域获得了广泛应用。

深度学习方法，特别是循环神经网络（RNN）及其变体如 LSTM（长短期记忆网络）和 GRU（门控循环单元），因其能够捕捉时间序列数据中的长期依赖关系，在气象预测中展现出强大的潜力。这些模型能够学习复杂的非线性关系，适合处理气象这类复杂系统的预测问题。

国内在气象和空气质量预测领域的研究也取得了显著进展。多个研究团队将机器学习方法应用于区域气象预测和空气质量评估，开发了各具特色的预测系统。但目前大多数系统往往专注于单一预测目标或单一算法模型，缺乏对多目标、多模型的综合集成，用户交互体验也有待提升。

### 1.3 研究内容与目标

本研究的主要内容包括：

1. 设计和实现气象数据和空气质量数据的采集和预处理系统：开发专门的爬虫程序从公开网站获取历史气象和空气质量数据，并进行数据清洗、标准化和特征工程，为后续模型训练提供高质量的数据集。

2. 开发基于多种机器学习算法的预测模型：实现 LightGBM、LSTM、GRU 和 Prophet 等多种算法模型，针对温度、AQI 指数、PM2.5 浓度、O3 浓度和天气状况等多个预测目标进行模型训练和优化，比较不同模型在不同预测任务上的性能表现。

3. 构建 Web 应用展示分析结果和预测结果：基于 Flask 框架开发 Web 应用，提供用户友好的界面，支持历史数据查询、多模型预测和结果可视化，并根据预测结果生成实用的出行建议。

研究目标：

1. 构建一个准确、高效的气象分析与预测系统，能够提供温度、空气质量指数、PM2.5 浓度、O3 浓度和天气状况的预测。

2. 实现多模型集成预测框架，允许用户选择不同的模型进行预测，并提供模型性能评估指标，帮助用户理解预测结果的可靠性。

3. 通过 Web 界面提供用户友好的交互体验，使专业的机器学习技术变得易于使用，为普通用户提供实用的气象预测服务。

4. 验证不同机器学习算法在气象预测领域的应用效果，为相关研究提供实践参考。

## 第二章 相关技术介绍

### 2.1 机器学习基础理论

#### 2.1.1 监督学习

监督学习是一种通过标记数据训练模型的机器学习方法。在监督学习中，算法通过分析训练数据（包括输入特征和对应的标签）学习输入和输出之间的映射关系，然后利用学到的关系预测新数据的标签。

在气象预测中，监督学习尤为适用，因为我们可以使用历史气象数据作为特征，未来气象数据作为标签来训练模型。例如，可以使用过去几天的温度、湿度、风速等气象参数作为输入特征，预测次日的平均温度或天气状况。

本系统采用的 LightGBM、LSTM、GRU 等模型都属于监督学习范畴，它们通过学习历史气象数据和空气质量数据中的模式，预测未来的气象状况和空气质量指标。

#### 2.1.2 集成学习

集成学习通过组合多个基础模型来提高预测性能。该方法基于这样一个观点：多个相对较弱的学习器组合在一起，可以形成一个更强大的学习器。集成学习主要分为三类：bagging（如随机森林）、boosting（如 AdaBoost、Gradient Boosting）和 stacking。

LightGBM 是一种高效的梯度提升框架，属于 boosting 类型的集成学习方法。它使用了基于树的学习算法，并且具有高效的特征并行和数据并行能力，特别适合处理大规模气象数据。LightGBM 采用叶子优先的生长策略，与传统的层优先生长策略相比，能更快地收敛到最优解，同时保持高精度。

在本系统中，LightGBM 作为主要的预测模型应用于温度、AQI 指数、PM2.5、O3 浓度的回归预测，以及天气状况的分类预测。通过分析源代码，可以看到系统针对不同的预测任务配置了不同的 LightGBM 参数，如回归任务使用'regression_l1'目标函数和 MAE 评估指标，分类任务使用'multiclass'目标函数和'multi_logloss'评估指标。

#### 2.1.3 深度学习

深度学习是机器学习的一个分支，利用多层神经网络进行特征学习和模式识别。深度学习模型能够自动从数据中学习层次化的特征表示，适合处理复杂的非线性关系。

在气象预测中，循环神经网络（RNN）及其变体如 LSTM 和 GRU 因其能够有效处理序列数据而被广泛应用。这些模型能够捕捉时间序列数据中的长期依赖关系，非常适合预测气象变化。

LSTM（长短期记忆网络）通过引入门控机制（输入门、遗忘门和输出门）解决了传统 RNN 的梯度消失问题，能够学习长期依赖关系。GRU（门控循环单元）是 LSTM 的简化版本，只有两个门（更新门和重置门），但在许多任务上表现与 LSTM 相当，同时计算效率更高。

本系统中，LSTM 模型主要用于温度、AQI 指数、PM2.5 和 O3 浓度的回归预测，而 GRU 模型则专注于天气状况的分类预测。系统采用了固定的回看窗口（LOOK_BACK=15），即使用过去 15 天的数据预测未来的值。

### 2.2 时间序列预测方法

#### 2.2.1 ARIMA 模型

ARIMA（自回归综合移动平均模型）是一种经典的时间序列预测方法，它结合了自回归(AR)、差分(I)和移动平均(MA)三种模型。ARIMA 假设时间序列数据具有一定的稳定性或可以通过差分变换达到稳定。

ARIMA 模型的基本形式为 ARIMA(p,d,q)，其中：

- p 是自回归项的阶数，表示当前值与之前 p 个值的关系
- d 是差分阶数，用于使时间序列平稳化
- q 是移动平均项的阶数，表示预测误差与之前 q 个误差项的关系

虽然本系统核心实现中没有直接使用 ARIMA 模型，但在`trained_models`目录中发现了`arima_temp_model.pkl`和`arima_aqi_model.pkl`文件，表明系统在早期开发阶段可能尝试过 ARIMA 模型进行温度和 AQI 预测。

#### 2.2.2 Prophet 模型

Prophet 是 Facebook 开发的时间序列预测工具，特别适合具有较强季节性和多个季节性的时间序列数据，能够自动处理缺失值和异常值。Prophet 模型的核心是一个加性模型，将时间序列分解为趋势、季节性和假日效应三个组成部分。

Prophet 模型的优势在于：

- 能够处理季节性强的数据，包括年度、周度和日度季节性
- 对异常值和数据缺失具有较强的鲁棒性
- 允许纳入额外的回归因子和假日效应
- 提供预测的不确定性区间

在本系统中，Prophet 模型被用于温度、AQI 指数、PM2.5 和 O3 浓度的回归预测。源码中显示，系统使用`model_to_json`和`model_from_json`函数保存和加载 Prophet 模型，并在预测 API 中提供了置信区间的展示功能，这是 Prophet 模型的一个特色。

#### 2.2.3 深度学习时间序列模型

深度学习时间序列模型，如 LSTM 和 GRU，通过特殊的网络结构捕捉时间序列数据中的长期依赖关系。与传统的统计模型相比，深度学习模型能够学习更复杂的非线性关系，适合处理高维特征和长序列数据。

本系统中的 LSTM 和 GRU 模型实现了序列到序列的学习，首先将历史数据转换为适合序列模型的输入形式（时间步长为 15 的滑动窗口），然后通过训练学习时间序列的内在规律。系统使用了 Early Stopping 策略避免过拟合，并使用 Adam 优化器进行模型训练。

深度学习模型在系统中的实现细节包括：

- LSTM 模型采用单隐藏层结构，隐藏单元数为 64
- GRU 模型同样采用单隐藏层，隐藏单元数也为 64
- 两种模型都使用 MinMaxScaler 将数据缩放到[0,1]区间
- 使用 EarlyStopping 回调函数监控验证集性能，避免过拟合
- 训练轮数设为 100，但通常会因 EarlyStopping 在达到最佳性能后提前停止

### 2.3 Web 应用开发技术

#### 2.3.1 Flask 框架

Flask 是一个轻量级的 Python Web 框架，提供了创建 Web 应用的核心功能，同时保持了较高的灵活性和扩展性。Flask 采用"微框架"的设计理念，核心简洁，但可以通过扩展添加所需功能。

本系统使用 Flask 作为后端框架，实现了路由控制、用户认证和数据 API。系统的核心组件包括：

- `app.py`：应用主入口，负责初始化 Flask 应用、注册蓝图和加载模型
- `blueprints`目录：使用 Flask 的 Blueprint 功能组织代码，提高模块化程度
  - `auth.py`：处理用户认证，包括登录、注册和登出功能
  - `pages.py`：处理页面路由，渲染 HTML 模板
  - `data_api.py`：提供历史数据查询和分析的 API
  - `predict_api.py`：提供模型预测功能的 API

此外，系统还使用了 Flask 的扩展：

- Flask-Login：管理用户会话和认证状态
- Flask-CORS：处理跨域资源共享，使前端能安全地调用 API

Flask 的轻量级特性与 SQLite 数据库相得益彰，共同构成了一个易于部署和维护的 Web 应用。

#### 2.3.2 前端技术栈

系统前端使用了现代 Web 开发的主流技术栈：

- HTML5：提供页面结构
- CSS3：负责页面样式，包括响应式布局
- JavaScript：实现客户端交互逻辑

系统还集成了多个前端库和框架：

- Bootstrap：用于响应式布局和 UI 组件，确保系统在不同设备上都有良好的显示效果
- jQuery：简化 DOM 操作和事件处理
- Font Awesome：提供丰富的图标，特别用于天气状况的可视化展示
- Ajax：实现前后端异步数据交互，提升用户体验

前端代码组织清晰，主要分布在`static`和`templates`目录中：

- `static/js`：包含 JavaScript 文件，如`predict_dashboard.js`实现预测仪表盘的交互逻辑
- `static/css`：包含 CSS 样式文件
- `templates`：包含 HTML 模板文件，如`predict_dashboard.html`、`home.html`等

### 2.4 数据可视化技术

#### 2.4.1 ECharts 库

ECharts 是百度开发的功能强大的 JavaScript 可视化库，支持多种图表类型，包括折线图、柱状图、饼图等。ECharts 的特点包括：

- 丰富的图表类型和定制化选项
- 强大的交互性能，支持缩放、拖拽、数据筛选等操作
- 良好的移动端适配
- 大数据量的渲染能力

本系统使用 ECharts 实现了气象数据和预测结果的可视化。根据源码中的`predict_dashboard.js`文件，系统实现了以下可视化效果：

- 历史数据和预测结果的折线图对比
- AQI 指数的水球图展示
- 天气状况的图标化展示
- 置信区间的可视化

系统还定义了一系列颜色常量和配置项，确保可视化效果的统一性和美观性。例如，历史数据使用蓝色系列（#5470C6），预测数据使用红色系列（#EE6666），置信区间使用灰色（#CCCCCC）。

## 第六章 预测模型设计与实现

### 6.1 模型选择与原理

#### 6.1.1 LightGBM 模型

LightGBM 是微软开发的一种高效梯度提升框架，使用基于树的学习算法，适用于大规模数据集的分类和回归任务。在本系统中，LightGBM 被用于预测数值目标（温度、AQI、PM2.5 和 O3）和分类目标（天气状况）。

LightGBM 的核心原理包括：

1. **梯度提升**：通过构建一系列弱学习器（决策树），每个新的学习器修正前面学习器的错误，从而提高整体性能。

2. **叶子优先生长**：与传统的层优先生长策略不同，LightGBM 采用叶子优先的方式，每次找到最大增益的叶子进行分裂，而不是逐层生长，这大大提高了训练效率。

3. **直方图优化**：通过将连续特征离散化为箱（bin），将特征值替换为 bin 索引，减少了内存消耗和计算量。

4. **互斥特征捆绑**：通过识别互斥特征（很少同时取非零值的特征）并将它们捆绑，减少了特征数量。

在系统中，LightGBM 的配置根据任务不同而有所区别：

```python
# 回归任务配置
LGBM_PARAMS_REGRESSION = {
    'objective': 'regression_l1',  # 使用L1损失（MAE）
    'metric': 'mae',               # 评估指标
    'n_estimators': 1000,          # 最大树数量
    'learning_rate': 0.05,         # 学习率
    'feature_fraction': 0.8,       # 特征采样比例
    'bagging_fraction': 0.8,       # 样本采样比例
    'bagging_freq': 1,             # 采样频率
    'lambda_l1': 0.1,              # L1正则化
    'lambda_l2': 0.1,              # L2正则化
    'num_leaves': 31,              # 叶子数量
    'verbose': -1,                 # 静默模式
    'n_jobs': -1,                  # 使用所有CPU
    'seed': 42,                    # 随机种子
}

# 分类任务配置
LGBM_PARAMS_CLASSIFICATION = {
    'objective': 'multiclass',     # 多分类任务
    'metric': 'multi_logloss',     # 多分类对数损失
    'num_class': 10,               # 类别数量（根据实际动态调整）
    # 其他参数与回归任务类似
}
```

LightGBM 的优势在于：

- 训练速度快，资源消耗低
- 能够处理高维特征
- 支持类别特征
- 对过拟合有较好的抵抗力
- 在多种预测任务上表现优异

系统中，LightGBM 模型展现出了最好的预测性能，尤其是在数值预测任务上。

#### 6.1.2 LSTM/GRU 模型

LSTM（长短期记忆网络）和 GRU（门控循环单元）是两种专门设计用于处理序列数据的递归神经网络变体。在本系统中，LSTM 用于数值目标（温度、AQI、PM2.5 和 O3）的预测，而 GRU 用于天气状况的分类预测。

**LSTM 的核心原理**：
LSTM 通过引入门控机制解决了传统 RNN 的梯度消失问题。LSTM 单元包含三个门：

1. **输入门**：控制新信息进入单元状态的程度
2. **遗忘门**：控制丢弃单元状态中信息的程度
3. **输出门**：控制单元状态影响当前输出的程度

这些门使 LSTM 能够学习长期依赖关系，决定何时记住信息，何时忘记信息，何时使用信息。

**GRU 的核心原理**：
GRU 是 LSTM 的简化版本，只有两个门：

1. **更新门**：控制前一时刻隐藏状态的信息保留程度
2. **重置门**：控制前一时刻隐藏状态对当前候选隐藏状态的影响

GRU 的简化设计减少了参数数量，降低了计算复杂度，同时在多数任务上保持与 LSTM 相当的性能。

在系统中，LSTM 和 GRU 的实现如下：

```python
# LSTM模型定义（用于回归）
model_lstm = Sequential([
    LSTM(LSTM_UNITS, input_shape=(LOOK_BACK, 1)),  # LSTM层，64个单元
    Dense(1)                                       # 输出层，预测一个值
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# GRU模型定义（用于分类）
model_gru = Sequential([
    GRU(GRU_UNITS, input_shape=(LOOK_BACK, 1)),    # GRU层，64个单元
    Dense(num_classes, activation='softmax')       # 输出层，预测多个类别概率
])
model_gru.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])
```

这两种模型的关键配置参数包括：

- **回看窗口（LOOK_BACK）**：设为 15，即使用过去 15 天的数据预测下一天
- **隐藏单元数**：LSTM 和 GRU 都设为 64
- **训练轮数（EPOCHS）**：设为 100，但使用 Early Stopping 可能提前停止
- **批大小（BATCH_SIZE）**：设为 32
- **优化器**：使用 Adam 优化器

LSTM 和 GRU 的优势在于能够捕捉时间序列数据中的长期依赖关系，特别适合气象预测这类具有明显时序特性的任务。

#### 6.1.3 Prophet 模型

Prophet 是 Facebook 开发的时间序列预测工具，专为具有季节性的业务时间序列设计。在本系统中，Prophet 被用于预测数值目标（温度、AQI、PM2.5 和 O3）。

Prophet 的核心原理是基于分解模型，将时间序列分解为三个主要组成部分：

1. **趋势（Trend）**：捕捉数据的长期变化趋势，使用分段逻辑增长模型
2. **季节性（Seasonality）**：捕捉周期性模式，如每周、每年的变化，使用傅里叶级数
3. **假日效应（Holiday）**：捕捉特殊日期（如节假日）的影响

Prophet 的数学表达式为：y(t) = g(t) + s(t) + h(t) + ε_t，其中 g(t)是趋势函数，s(t)是季节性函数，h(t)是假日效应，ε_t 是误差项。

在系统中，Prophet 模型的实现相对简单：

```python
# 初始化并训练Prophet模型
model_prophet = Prophet()
model_prophet.fit(prophet_train_df)

# 预测
future_ds = prophet_test_df[['ds']]
forecast = model_prophet.predict(future_ds)
```

Prophet 的一个独特优势是能够提供预测的不确定性估计（置信区间），这在系统中得到了应用：

```python
# 使用Prophet模型预测，包括置信区间
prophet_preds_list = predict_prophet_sequence(model, N_FUTURE_DAYS)
future_predictions = [round(p['yhat'], 1) if p and pd.notna(p.get('yhat')) else None for p in prophet_preds_list]
lower = [round(p['yhat_lower'], 1) if p and pd.notna(p.get('yhat_lower')) else None for p in prophet_preds_list]
upper = [round(p['yhat_upper'], 1) if p and pd.notna(p.get('yhat_upper')) else None for p in prophet_preds_list]
confidence_interval = {"lower": lower, "upper": upper}
```

Prophet 的优势在于：

- 对缺失值和异常值具有较强的鲁棒性
- 能够自动处理季节性
- 提供预测的不确定性估计
- 参数较少，易于使用

在系统中，Prophet 在预测置信区间方面提供了独特价值，虽然其预测准确性整体不如 LightGBM。

### 6.2 模型训练流程

#### 6.2.1 LightGBM 模型训练

LightGBM 模型的训练流程在`train_models.py`中实现。对于回归任务（如温度、AQI 预测）和分类任务（天气状况预测），训练流程有所不同。

**回归模型训练流程**：

```python
# 1. 准备数据
y_train_target = y_train_final[target]  # 缩放后的训练标签
y_test_target = y_test_final[target]    # 缩放后的测试标签
y_test_target_original = y_test_raw[target]  # 原始未缩放的测试标签

# 2. 初始化模型
lgbm_reg = lgb.LGBMRegressor(**LGBM_PARAMS_REGRESSION)

# 3. 训练模型，使用Early Stopping
eval_set = [(X_test_lgbm, y_test_target)]
callbacks = [lgb.early_stopping(stopping_rounds=50, verbose=False)]
lgbm_reg.fit(X_train_lgbm, y_train_target, eval_set=eval_set, eval_metric='mae', callbacks=callbacks)

# 4. 预测与评估
y_pred_scaled = lgbm_reg.predict(X_test_lgbm)
y_pred_original = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
mae = mean_absolute_error(y_test_target_original.values, y_pred_original)
```

**分类模型训练流程**：

```python
# 1. 准备数据
y_train_target_clf = y_train_final[target_clf]
y_test_target_clf = y_test_final[target_clf]

# 2. 初始化模型
lgbm_clf = lgb.LGBMClassifier(**LGBM_PARAMS_CLASSIFICATION)

# 3. 训练模型，使用Early Stopping
eval_set_clf = [(X_test_lgbm, y_test_target_clf)]
callbacks_clf = [lgb.early_stopping(stopping_rounds=50, verbose=False)]
lgbm_clf.fit(X_train_lgbm, y_train_target_clf, eval_set=eval_set_clf, eval_metric='multi_logloss', callbacks=callbacks_clf)

# 4. 预测与评估
y_pred_clf = lgbm_clf.predict(X_test_lgbm)
accuracy = accuracy_score(y_test_target_clf, y_pred_clf)
f1_w = f1_score(y_test_target_clf, y_pred_clf, average='weighted')
```

LightGBM 训练的关键点包括：

- 使用 Early Stopping 防止过拟合
- 对回归任务使用 MAE 作为评估指标
- 对分类任务使用 multi_logloss 和 weighted F1 分数评估
- 保存模型和评估指标，便于后续使用

#### 6.2.2 LSTM/GRU 模型训练

LSTM 和 GRU 模型的训练流程更加复杂，因为需要将数据转换为适合序列模型的格式。

**序列数据创建函数**：

```python
def create_dataset_lstm_gru(data, look_back=LOOK_BACK):
    X, y = [], []
    if len(data) <= look_back:
        logging.warning(f"数据长度 ({len(data)}) 不足以创建 look_back={look_back} 的序列。")
        return np.array(X), np.array(y)
    # 生成 N - look_back 个样本
    for i in range(len(data) - look_back):
        X.append(data[i:(i + look_back)])  # 获取 look_back 个历史点
        y.append(data[i + look_back])      # 获取下一个点作为目标
    X, y = np.array(X), np.array(y)
    # 将 X 重塑为 LSTM/GRU 需要的 3D 格式 [样本数, 时间步长, 特征数]
    X = np.reshape(X, (X.shape[0], X.shape[1], 1))
    return X, y
```

**LSTM 训练流程（回归任务）**：

```python
# 1. 准备序列数据
train_data_scaled = y_train_final[target].values
test_data_scaled = y_test_final[target].values
X_train_lstm, y_train_lstm = create_dataset_lstm_gru(train_data_scaled, LOOK_BACK)
X_test_lstm, y_test_lstm = create_dataset_lstm_gru(test_data_scaled, LOOK_BACK)

# 2. 定义模型
model_lstm = Sequential([
    LSTM(LSTM_UNITS, input_shape=(LOOK_BACK, 1)),
    Dense(1)
])
model_lstm.compile(optimizer='adam', loss='mean_squared_error')

# 3. 训练模型，使用Early Stopping
early_stopping = EarlyStopping(monitor='val_loss', patience=DL_EARLY_STOPPING_PATIENCE, restore_best_weights=True, verbose=0)
history = model_lstm.fit(X_train_lstm, y_train_lstm,
                         epochs=DL_EPOCHS,
                         batch_size=DL_BATCH_SIZE,
                         validation_data=(X_test_lstm, y_test_lstm),
                         callbacks=[early_stopping],
                         verbose=0)

# 4. 预测与评估
y_pred_lstm_scaled = model_lstm.predict(X_test_lstm, verbose=0)
y_pred_lstm_original = scaler.inverse_transform(y_pred_lstm_scaled).flatten()
mae_lstm = mean_absolute_error(y_test_original_matched, y_pred_lstm_original)
```

**GRU 训练流程（分类任务）**：

```python
# 1. 准备序列数据
train_data_gru_in = y_train_final[target_clf].values
test_data_gru_in = y_test_final[target_clf].values
X_train_gru, y_train_gru = create_dataset_lstm_gru(train_data_gru_in, LOOK_BACK)
X_test_gru, y_test_gru = create_dataset_lstm_gru(test_data_gru_in, LOOK_BACK)

# 2. 定义模型
model_gru = Sequential([
    GRU(GRU_UNITS, input_shape=(LOOK_BACK, 1)),
    Dense(num_classes, activation='softmax')
])
model_gru.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])

# 3. 训练模型，使用Early Stopping
early_stopping_gru = EarlyStopping(monitor='val_accuracy', patience=DL_EARLY_STOPPING_PATIENCE, restore_best_weights=True, mode='max', verbose=0)
history_gru = model_gru.fit(X_train_gru, y_train_gru,
                           epochs=DL_EPOCHS,
                           batch_size=DL_BATCH_SIZE,
                           validation_data=(X_test_gru, y_test_gru),
                           callbacks=[early_stopping_gru],
                           verbose=0)

# 4. 预测与评估
y_prob_gru = model_gru.predict(X_test_gru, verbose=0)
y_pred_gru = np.argmax(y_prob_gru, axis=1)
accuracy_gru = accuracy_score(y_test_clf_matched, y_pred_gru)
f1_w_gru = f1_score(y_test_clf_matched, y_pred_gru, average='weighted')
```

LSTM/GRU 训练的关键点包括：

- 将数据转换为 3D 格式 [样本数, 时间步长, 特征数]
- 使用 Early Stopping 避免过拟合
- LSTM 用于回归任务，GRU 用于分类任务
- 注意测试集的截断和对齐，确保评估的公平性

#### 6.2.3 Prophet 模型训练

Prophet 模型的训练流程相对简单，主要涉及数据格式转换和模型配置。

```python
# 1. 准备数据（Prophet需要特定的列名 'ds' 和 'y'）
prophet_train_df = train_df[['date', target]].rename(columns={'date': 'ds', target: 'y'})
prophet_test_df = test_df[['date', target]].rename(columns={'date': 'ds', target: 'y'})

# 2. 初始化并训练模型
model_prophet = Prophet()
model_prophet.fit(prophet_train_df)

# 3. 预测与评估
future_ds = prophet_test_df[['ds']]
forecast = model_prophet.predict(future_ds)
y_pred_prophet = forecast['yhat'].values
y_test_prophet_original = prophet_test_df['y'].values
mae_prophet = mean_absolute_error(y_test_prophet_original, y_pred_prophet)

# 4. 保存模型
with open(model_save_path_prophet, 'w') as fout:
    fout.write(model_to_json(model_prophet))
```

Prophet 训练的关键点包括：

- 数据格式转换，将日期列重命名为'ds'，目标列重命名为'y'
- 预测结果包含多个成分：yhat（预测值）、yhat_lower（下限）、yhat_upper（上限）
- 使用 model_to_json 和 model_from_json 函数保存和加载模型
- 评估同样使用 MAE 作为指标

### 6.3 模型评估指标

#### 6.3.1 回归模型评估指标

对于预测温度、AQI 指数、PM2.5 和 O3 等数值目标的回归模型，系统主要使用以下评估指标：

**1. 平均绝对误差（MAE）**：

```python
mae = mean_absolute_error(y_test_target_original.values, y_pred_original)
```

MAE 衡量预测值与实际值之间的平均绝对差异，单位与原始数据相同，直观易懂。MAE 对异常值不敏感，是系统中最主要的评估指标。

系统中各模型在不同目标上的 MAE 指标（从`model_metrics.json`文件）：

```json
{
  "lgbm_avg_temp_mae": 0.119,
  "lgbm_aqi_index_mae": 4.38,
  "lgbm_pm25_mae": 4.398,
  "lgbm_o3_mae": 9.074,
  "lstm_avg_temp_mae": 1.257,
  "lstm_aqi_index_mae": 12.399,
  "lstm_pm25_mae": 10.02,
  "lstm_o3_mae": 11.626,
  "prophet_avg_temp_mae": 2.515,
  "prophet_aqi_index_mae": 21.707,
  "prophet_pm25_mae": 16.284,
  "prophet_o3_mae": 16.245
}
```

从上述指标可以看出，LightGBM 在所有回归任务上都表现最好，LSTM 次之，Prophet 表现最差。特别是在平均温度预测上，LightGBM 的 MAE 仅为 0.119，精度令人印象深刻。

**2. 其他回归指标**：
虽然系统主要使用 MAE，但在完整的评估中，也可以考虑以下指标：

- **均方误差（MSE）**：更敏感于大误差，但单位是原始数据的平方
- **均方根误差（RMSE）**：MSE 的平方根，单位与原始数据相同
- **决定系数（R²）**：衡量模型解释数据变异的程度，范围为[0,1]

在实际开发中，MAE 因其直观性和稳健性被选为主要指标。

#### 6.3.2 分类模型评估指标

对于预测天气状况的分类模型，系统主要使用以下评估指标：

**1. 准确率（Accuracy）**：

```python
accuracy = accuracy_score(y_test_target_clf, y_pred_clf)
```

准确率衡量正确预测的比例，简单直观，但在类别不平衡时可能有误导性。

**2. 加权 F1 分数（Weighted F1-Score）**：

```python
f1_w = f1_score(y_test_target_clf, y_pred_clf, average='weighted')
```

F1 分数是精确率和召回率的调和平均，加权 F1 考虑了各类别的样本数量，在类别不平衡时更为合理。

系统中分类模型的评估指标（从`model_metrics.json`文件）：

```json
{
  "lgbm_weather_accuracy": 0.411,
  "lgbm_weather_f1_weighted": 0.378,
  "gru_weather_accuracy": 0.367,
  "gru_weather_f1_weighted": 0.257
}
```

从上述指标可以看出，LightGBM 在天气状况分类任务上也表现优于 GRU，无论是准确率还是加权 F1 分数。不过，整体而言，分类任务的性能不如回归任务，这可能是因为天气状况的变化更加复杂和不可预测。

### 6.4 模型优化策略

#### 6.4.1 超参数调优

系统针对不同模型实现了以下超参数调优策略：

**1. LightGBM 超参数**：

```python
LGBM_PARAMS_REGRESSION = {
    'learning_rate': 0.05,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'lambda_l1': 0.1,
    'lambda_l2': 0.1,
    'num_leaves': 31,
    # 其他参数...
}
```

关键超参数包括：

- **learning_rate**：较小的学习率(0.05)可以提高模型稳定性
- **feature_fraction**：特征抽样比例，有助于防止过拟合
- **bagging_fraction**：样本抽样比例，增加模型多样性
- **lambda_l1/lambda_l2**：L1/L2 正则化强度，控制模型复杂度
- **num_leaves**：树的最大叶子数，平衡拟合能力和复杂度

**2. LSTM/GRU 超参数**：

```python
LOOK_BACK = 15      # 回看窗口长度
LSTM_UNITS = 64     # LSTM单元数
GRU_UNITS = 64      # GRU单元数
DL_EPOCHS = 100     # 最大训练轮数
DL_BATCH_SIZE = 32  # 批大小
DL_EARLY_STOPPING_PATIENCE = 10  # Early Stopping耐心值
```

关键超参数包括：

- **LOOK_BACK**：历史窗口大小，影响序列模型捕捉的时间范围
- **LSTM_UNITS/GRU_UNITS**：隐藏单元数，控制模型复杂度
- **DL_EARLY_STOPPING_PATIENCE**：多少轮验证性能不提升后停止训练

**3. Early Stopping 策略**：
对所有模型都应用了 Early Stopping 策略，避免过拟合：

```python
# LightGBM的Early Stopping
callbacks = [lgb.early_stopping(stopping_rounds=50, verbose=False)]

# LSTM的Early Stopping
early_stopping = EarlyStopping(monitor='val_loss', patience=DL_EARLY_STOPPING_PATIENCE, restore_best_weights=True, verbose=0)

# GRU的Early Stopping
early_stopping_gru = EarlyStopping(monitor='val_accuracy', patience=DL_EARLY_STOPPING_PATIENCE, restore_best_weights=True, mode='max', verbose=0)
```

系统中的超参数是基于经验和试验选择的，未来可以考虑使用网格搜索、随机搜索或贝叶斯优化等方法进行更系统的调优。

#### 6.4.2 特征选择

系统实现了以下特征选择策略：

**1. 手动特征选择**：

```python
excluded_cols = [
    'weather_condition_original', 'temperature_range', 'wind_info', 'city',
    'weather_category', # 原始分类字符串
    'quality_level', # 原始质量等级文本
]
feature_cols = [col for col in df_featured.columns if col not in excluded_cols + numerical_targets + ['weather_category_encoded','date']]
```

系统排除了原始文本特征和目标变量，只使用有预测价值的特征。

**2. 特征工程优化**：
系统精心设计了时间特征、滞后特征和滚动特征，这些特征对预测性能有显著影响：

```python
df_featured = create_time_features(df_featured)
df_featured = create_lag_features(df_featured, cols_exist_for_lag_roll, LAG_DAYS)
df_featured = create_rolling_features(df_featured, cols_exist_for_lag_roll, ROLLING_WINDOWS)
```

**3. 交叉特征**：
系统创建了交叉特征，如使用平均气温作为 PM2.5 和 O3 预测的辅助特征：

```python
if 'avg_temp' in df_featured.columns:
     df_featured = create_lag_features(df_featured, ['avg_temp'], LAG_DAYS)
     df_featured = create_rolling_features(df_featured, ['avg_temp'], ROLLING_WINDOWS)
```

这些特征选择策略有效提升了模型性能，特别是对 LightGBM 这种基于特征的模型。未来可以考虑使用特征重要性分析进一步优化特征集。

#### 6.4.3 集成策略

虽然系统代码中没有显式实现模型集成，但设计中包含了多模型预测的框架，为未来的集成策略奠定了基础：

**1. 多模型训练**：
系统同时训练了不同类型的模型（LightGBM、LSTM/GRU、Prophet），这是集成学习的前提。

**2. 预测 API 设计**：
系统的预测 API 支持指定模型类型，便于获取不同模型的预测结果：

```python
@predict_api_bp.route('/predict/<model>/<target>/<city>')
@login_required
def predict_by_model_target_city(model, target, city):
    # ...
```

**3. 前端模型选择**：
前端界面允许用户选择不同的模型进行预测，并展示模型评估指标：

```javascript
const modelInfoData = {
  model: apiResponseData?.model || 'N/A',
  city: apiResponseData?.city || 'N/A',
  metrics: apiResponseData?.metrics || {},
}
```

未来可以实现的集成策略包括：

- **简单平均**：对不同模型的预测结果取平均
- **加权平均**：根据模型的历史性能分配权重
- **Stacking**：使用另一个模型学习如何组合基模型的预测
- **动态选择**：根据输入特征或时间范围动态选择最适合的模型

## 第七章 系统实现

### 7.1 开发环境搭建

#### 7.1.1 硬件环境

系统的开发和测试环境如下：

- **处理器**：Intel Core i5/i7 或同等性能的处理器
- **内存**：8GB RAM 或更高（推荐 16GB 以上，特别是训练深度学习模型时）
- **存储**：至少 10GB 可用空间，用于代码、数据库和模型文件
- **网络**：稳定的互联网连接，用于数据爬取

系统设计的轻量级特性使其可以在普通 PC 或笔记本电脑上运行，不需要特殊的硬件加速（如 GPU），除非需要大规模重新训练深度学习模型。

#### 7.1.2 软件环境

系统的软件环境配置如下：

- **操作系统**：Windows 10/11 或 Linux（Ubuntu 18.04+）
- **Python 环境**：Python 3.8+
- **数据库**：SQLite 3
- **关键依赖库**（根据 requirements.txt）：
  - **Web 框架**：Flask 2.2.5，Flask-Login 0.6.3，Flask-CORS 5.0.0
  - **数据处理**：pandas 1.3.5，numpy 1.21.6
  - **机器学习**：lightgbm 4.6.0，scikit-learn 1.0.2
  - **深度学习**：tensorflow 2.11.0，keras 2.11.0
  - **时间序列**：prophet 1.1.6
  - **数据爬取**：requests，beautifulsoup4
  - **Web 服务器**：waitress 2.1.2（生产环境）
  - **其他**：python-dotenv，joblib，matplotlib

项目使用 pip 进行依赖管理，可以通过以下命令安装所有依赖：

```bash
pip install -r requirements.txt
```

环境变量通过.env 文件和 python-dotenv 库进行管理，主要包括：

- FLASK_DEBUG：控制是否开启调试模式
- SECRET_KEY：Flask 应用的密钥

#### 7.1.3 项目结构

项目的主要文件和目录结构如下：

```
/WeatherAnalysis/
├── app.py                # Flask应用主入口
├── database.py           # 数据库操作函数
├── models.py             # 用户模型定义
├── utils.py              # 工具函数和数据处理
├── train_models.py       # 模型训练脚本
├── weather_spider.py     # 天气数据爬虫
├── aqi_spider.py         # 空气质量数据爬虫
├── create_db.py          # 数据库初始化脚本
├── fix_db_quality.py     # 数据质量修复工具
├── check_weather.py      # 天气状况检查工具
├── extract_weather.py    # 天气数据提取工具
├── requirements.txt      # 依赖列表
├── .env                  # 环境变量配置
├── data.db               # 主数据库文件
├── user_info.db          # 用户数据库文件
├── blueprints/           # Flask蓝图模块
│   ├── __init__.py
│   ├── auth.py           # 认证相关路由
│   ├── pages.py          # 页面路由
│   ├── data_api.py       # 数据API
│   └── predict_api.py    # 预测API
├── static/               # 静态资源
│   ├── css/              # CSS样式文件
│   ├── js/               # JavaScript文件
│   │   ├── predict_dashboard.js
│   │   ├── global.js
│   │   └── echarts_config.js
│   └── img/              # 图片资源
├── templates/            # HTML模板
│   ├── layout.html       # 基础布局模板
│   ├── home.html         # 首页模板
│   ├── predict_dashboard.html # 预测仪表盘模板
│   └── ...               # 其他模板
└── trained_models/       # 训练好的模型
    ├── lgbm_avg_temp_model.pkl
    ├── lstm_avg_temp_model.h5
    ├── prophet_avg_temp_model.json
    ├── scaler_avg_temp.pkl
    ├── weather_label_encoder.pkl
    ├── model_metrics.json
    └── ...                # 其他模型文件
```

这种项目结构遵循了 Flask 应用的最佳实践，将不同功能的代码分离到不同的模块中，便于维护和扩展。

### 7.2 数据采集模块实现

#### 7.2.1 爬虫实现

系统的数据采集通过两个爬虫脚本实现：`weather_spider.py`和`aqi_spider.py`。

**气象数据爬虫（weather_spider.py）**：

```python
def parse_date_weather(date_str):
    """将 'YYYY年MM月DD日' 转换为 'YYYY-MM-DD' 格式"""
    try:
        date_str_formatted = date_str.replace('年', '-').replace('月', '-').replace('日', '')
        datetime.strptime(date_str_formatted, '%Y-%m-%d')
        return date_str_formatted
    except ValueError:
        print(f"警告：无法解析的日期格式: {date_str}")
        return None

# 爬取主循环
for city, city_code in CITY_MAP.items():
    for year_month in year_months:
        url = f'http://www.tianqihoubao.com/lishi/{city_code}/month/{year_month}.html'
        response = requests.get(url, timeout=60)

        if response.status_code == 200:
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')
            items = table.find_all('tr')

            for item in items[1:]:  # 跳过表头
                data = item.find_all('td')
                date_raw = remove_space(data[0].text)
                date_formatted = parse_date_weather(date_raw)
                tianqi = remove_space(data[1].text)
                qiwen = remove_space(data[2].text)
                feng = remove_space(data[3].text)

                data_tuple = (city, date_formatted, tianqi, qiwen, feng)
                month_data.append(data_tuple)

            # 批量插入数据
            cursor.executemany('''
                INSERT OR IGNORE INTO weather_data
                (city, date, weather_condition, temperature_range, wind_info)
                VALUES (?, ?, ?, ?, ?)
            ''', month_data)
            conn.commit()
```

**空气质量数据爬虫（aqi_spider.py）**：

```python
def safe_float(value_str, default=None):
    """安全地将字符串转换为浮点数"""
    try:
        return float(value_str)
    except (ValueError, TypeError):
        return default

# 爬取主循环与天气爬虫类似，但处理不同的数据
for city, city_code in CITY_MAP.items():
    for year_month in year_months:
        url = f'http://www.tianqihoubao.com/aqi/{city_code}-{year_month}.html'
        response = requests.get(url, timeout=60)

        if response.status_code == 200:
            # 解析HTML获取AQI数据
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')
            items = table.find_all('tr')

            for item in items[1:]:
                data = item.find_all('td')
                date_raw = remove_space(data[0].text)
                date_formatted = parse_date_aqi(date_raw)
                zhiliangdengji = remove_space(data[1].text)
                aqi = safe_int(remove_space(data[2].text))
                pm25 = safe_float(remove_space(data[4].text))
                # 其他污染物数据...

                data_tuple = (city, date_formatted, zhiliangdengji, aqi, pm25, pm10, so2, no2, co, o3)
                month_data.append(data_tuple)

            # 批量插入数据
            cursor.executemany('''
                INSERT OR IGNORE INTO aqi_data
                (city, date, quality_level, aqi_index, pm25, pm10, so2, no2, co, o3)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', month_data)
            conn.commit()
```

这两个爬虫脚本包含了完善的错误处理、重试机制和数据清洗逻辑，确保数据采集的稳定性和数据质量。

#### 7.2.2 数据入库实现

数据入库过程在爬虫脚本中实现，使用 SQLite 数据库存储。数据库结构由`create_db.py`脚本定义：

```python
def create_database(db_file, create_table_statements, create_index_statements=None):
    """创建数据库文件并执行 SQL 语句来创建表和索引。"""
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        for statement in create_table_statements:
            cursor.execute(statement)

        if create_index_statements:
            for statement in create_index_statements:
                cursor.execute(statement)

        conn.commit()

    except sqlite3.Error as e:
        print(f"处理数据库 '{db_file}' 时发生错误: {e}")
        if conn: conn.rollback()
    finally:
        if conn: conn.close()

# 数据表定义
WEATHER_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS weather_data (
    city TEXT NOT NULL,
    date TEXT NOT NULL,
    weather_condition TEXT,
    temperature_range TEXT,
    wind_info TEXT,
    PRIMARY KEY (city, date)
);
"""

AQI_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS aqi_data (
    city TEXT NOT NULL,
    date TEXT NOT NULL,
    quality_level TEXT,
    aqi_index INTEGER,
    pm25 REAL,
    pm10 REAL,
    so2 REAL,
    no2 REAL,
    co REAL,
    o3 REAL,
    PRIMARY KEY (city, date)
);
"""

# 创建索引
WEATHER_INDEX_SQL = """
CREATE INDEX IF NOT EXISTS idx_weather_city_date ON weather_data (city, date);
"""

AQI_INDEX_SQL = """
CREATE INDEX IF NOT EXISTS idx_aqi_city_date ON aqi_data (city, date);
"""

# 主执行逻辑
if __name__ == "__main__":
    data_db_tables = [WEATHER_TABLE_SQL, AQI_TABLE_SQL]
    data_db_indexes = [WEATHER_INDEX_SQL, AQI_INDEX_SQL]
    create_database(DATA_DB_FILE, data_db_tables, data_db_indexes)

    user_db_tables = [USER_TABLE_SQL]
    create_database(USER_DB_FILE, user_db_tables)
```

数据入库时使用了`INSERT OR IGNORE`语句，避免因主键冲突导致的数据重复，实现了增量更新的效果。数据质量问题通过`fix_db_quality.py`脚本处理，修复可能的乱码：

```python
def fix_db_quality():
    fix_map = {
        "гХ": "优",
        "СМ": "良",
        "ЧсЖШЮлШО": "轻度污染",
        # 其他映射...
    }

    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()

    for garbled, correct in fix_map.items():
        check_sql = f"SELECT COUNT(*) FROM {table_name} WHERE {column_name} = ?"
        cursor.execute(check_sql, (garbled,))
        count = cursor.fetchone()[0]

        if count > 0:
            update_sql = f"UPDATE {table_name} SET {column_name} = ? WHERE {column_name} = ?"
            cursor.execute(update_sql, (correct, garbled))

    conn.commit()
    conn.close()
```

### 7.3 预测分析模块实现

#### 7.3.1 模型加载实现

系统在 Flask 应用启动时预加载所有模型，提高预测时的响应速度。模型加载在`app.py`中的`load_all_models_and_helpers`函数实现：

```python
def load_all_models_and_helpers(app):
    """
    加载所有新训练的模型、Scalers 和 LabelEncoder。
    """
    logger = app.logger
    model_dir = app.config['MODEL_DIR']

    # 定义需要加载的文件和对应的键名
    models_to_load = {
        # LightGBM
        'lgbm_avg_temp': 'lgbm_avg_temp_model.pkl',
        'lgbm_aqi_index': 'lgbm_aqi_index_model.pkl',
        'lgbm_pm25': 'lgbm_pm25_model.pkl',
        'lgbm_o3': 'lgbm_o3_model.pkl',
        'lgbm_weather': 'lgbm_weather_model.pkl',
        # LSTM
        'lstm_avg_temp': 'lstm_avg_temp_model.h5',
        'lstm_aqi_index': 'lstm_aqi_index_model.h5',
        'lstm_pm25': 'lstm_pm25_model.h5',
        'lstm_o3': 'lstm_o3_model.h5',
        # GRU
        'gru_weather': 'gru_weather_model.h5',
        # Prophet
        'prophet_avg_temp': 'prophet_avg_temp_model.json',
        'prophet_aqi_index': 'prophet_aqi_index_model.json',
        'prophet_pm25': 'prophet_pm25_model.json',
        'prophet_o3': 'prophet_o3_model.json',
    }
    scalers_to_load = {
        'avg_temp': 'scaler_avg_temp.pkl',
        'aqi_index': 'scaler_aqi_index.pkl',
        'pm25': 'scaler_pm25.pkl',
        'o3': 'scaler_o3.pkl',
    }
    encoders_to_load = {
        'weather': 'weather_label_encoder.pkl',
    }

    # 初始化存储字典
    app.config['PRELOADED_MODELS'] = {}
    app.config['PRELOADED_SCALERS'] = {}
    app.config['PRELOADED_ENCODERS'] = {}

    # 加载模型
    for key, filename in models_to_load.items():
        path = os.path.join(model_dir, filename)
        model_type = filename.split('.')[-1]

        if not os.path.exists(path):
            logger.warning(f"模型文件未找到: {path}。跳过加载 {key}。")
            continue

        if model_type == 'pkl':  # LightGBM
            model = joblib.load(path)
        elif model_type == 'h5':  # LSTM/GRU
            if KERAS_AVAILABLE:
                model = keras_load_model(path)
            else:
                continue
        elif model_type == 'json':  # Prophet
            if PROPHET_AVAILABLE:
                with open(path, 'r') as fin:
                    model = model_from_json(fin.read())
            else:
                continue

        app.config['PRELOADED_MODELS'][key] = model

    # 加载Scalers和Encoders（类似逻辑）
    # ...
```

这种预加载设计确保了预测 API 的快速响应，不需要在每次请求时重新加载模型。系统还处理了模型库不可用的情况，确保应用即使在某些模型无法加载时仍能部分工作。

#### 7.3.2 单目标预测实现

系统的单目标预测功能在`blueprints/predict_api.py`中实现，支持不同模型对单一目标的预测：

```python
@predict_api_bp.route('/predict/<model>/<target>/<city>')
@login_required
def predict_by_model_target_city(model, target, city):
    """根据指定的模型类型、预测目标和城市进行预测"""
    # 基本验证
    if not model or not target or not city:
        return jsonify({"error": "必须提供模型类型、预测目标和城市"}), 400

    model = model.lower()
    target = target.lower()

    valid_models = ['lgbm', 'lstm', 'prophet', 'gru']
    valid_targets = ['avg_temp', 'aqi_index', 'pm25', 'o3', 'weather']

    if model not in valid_models:
        return jsonify({"error": f"不支持的模型类型: {model}"}), 400
    if target not in valid_targets:
        return jsonify({"error": f"不支持的预测目标: {target}"}), 400

    # 特殊情况处理
    if model == 'prophet' and target == 'weather':
        return jsonify({"error": "Prophet 模型不支持天气分类预测"}), 400
    if model == 'gru' and target != 'weather':
        return jsonify({"error": "GRU 模型仅支持天气分类预测"}), 400

    # 调用预测函数
    try:
        result = fetchPredictionData(target, model, city)
        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f"预测失败: {e}", exc_info=True)
        return jsonify({"error": f"预测过程中发生错误: {str(e)}"}), 500
```

实际的预测逻辑在`fetchPredictionData`函数中实现，该函数根据目标和模型类型调用不同的预测逻辑：

```python
def fetchPredictionData(target, model, city):
    # ...
    # 实现预测逻辑
    # ...
```
