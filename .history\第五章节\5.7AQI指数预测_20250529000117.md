5.7 AQI 指数预测

空气质量指数(AQI)是表征空气质量状况的无量纲指数，是城市环境管理的重要指标。准确预测未来的 AQI 变化趋势，对于政府部门制定空气污染防治措施、公众做好健康防护具有重要意义。本系统基于眉山市历史 AQI 数据，构建了多种算法预测模型，实现了高精度的 AQI 预测功能。

5.7.1 AQI 预测特性与挑战

AQI 指数预测区别于常规气象要素预测，具有其独特的数据特性和预测难点。在系统设计过程中，重点考虑了以下关键特征：

首先，AQI 数据表现出显著的多重周期性。系统分析眉山市 2020-2024 年的 AQI 数据发现，其变化既有明显的年度季节性波动（冬季高、夏季低），也存在周度变化模式（工作日高、周末低）。统计显示，眉山市冬季（12-2 月）平均 AQI 指数为 68.5，而夏季（6-8 月）仅为 42.3，差异显著。同时，工作日的平均 AQI 比周末高约 7.8%，反映了人类活动对空气质量的周期性影响。这种多重周期性为预测模型提供了重要的时间模式特征，也对模型的时间特征提取能力提出了更高要求。

其次，AQI 数据呈现出高度的多因素依赖性。AQI 指数综合反映了多种污染物（PM2.5、PM10、SO2、NO2、O3、CO 等）的浓度水平，而这些污染物又受到多种因素影响，包括气象条件（温度、湿度、风速、气压等）、人为活动强度和周边环境特征等。相关性分析表明，眉山市 AQI 与 PM2.5 的相关系数高达 0.91，与 PM10 的相关系数为 0.87，与风速的负相关系数为-0.43。这种复杂的多因素依赖关系使得 AQI 预测需要综合考虑多维度的影响因素，构建更为复杂的特征工程体系。

第三，AQI 数据存在明显的非线性和非平稳特性。随着污染排放控制政策的实施和气象条件的变化，AQI 时间序列表现出明显的非平稳性。趋势分析显示，眉山市 2020-2024 年的 AQI 整体呈现下降趋势，年均值从 2020 年的 57.2 下降到 2023 年的 48.6，降幅达 15%。同时，AQI 与影响因素之间的关系也表现为复杂的非线性模式，如相同气象条件下，不同季节的污染物扩散条件差异明显，导致 AQI 响应也不同。这种非线性和非平稳特性增加了预测难度，要求模型具有更强的适应性和学习能力。

此外，AQI 预测还面临突发污染事件的难以预测性。如重污染天气、跨区域污染传输等突发或异常情况难以从历史模式中学习，导致预测准确性下降。数据分析显示，2023 年眉山市共记录了 8 次明显的污染过程，这些事件导致 AQI 在短期内迅速上升 30-50 点，远超正常波动范围。这类事件的不可预测性构成了 AQI 预测的主要挑战之一。

图 1 眉山市 2020-2024 年 AQI 变化趋势图

5.7.2 AQI 预测的特征工程与模型调整

针对 AQI 预测的特点，系统设计了专门的特征工程方法和模型优化策略，以提高预测准确性。

（1）针对 AQI 的特征工程

在 AQI 预测的特征工程中，系统基于 utils.py 中实现的功能，重点构建了三类关键特征：时间特征、气象特征和历史污染物特征。

时间特征是捕捉 AQI 周期性变化的基础。系统利用 create_time_features 函数创建了一系列时间特征，包括月份(month)、星期几(dayofweek)、一年中的天(dayofyear)、一年中的周(weekofyear)以及季度(quarter)等。这些时间特征能够有效捕捉 AQI 指数的年度季节性变化和周内规律性波动。例如，通过季度特征可以明确区分污染物浓度高的冬季和相对较低的夏季；而星期几特征则能够反映出工作日排放模式与周末的差异。

历史污染物特征是 AQI 预测中最为核心的输入。系统通过 create_lag_features 函数为 aqi_index 创建了多个时间尺度（LAG_DAYS 中定义的 1、2、3、7、14 天）的滞后特征，反映了近期污染水平对当前的影响。同时，系统通过 create_rolling_features 函数构建了不同窗口大小（ROLLING_WINDOWS 中定义的 3、7、14、30 天）的滚动统计量，包括均值、标准差、最大值和最小值，以捕捉污染水平的中期变化趋势和波动特征。

气象特征是影响 AQI 变化的重要外部因素。根据 train_models.py 中的特征工程部分，系统将平均温度(avg_temp)作为主要气象特征，为其创建了滞后和滚动特征，以反映气象条件对污染物扩散和积累的影响。温度特征能够间接反映垂直扩散条件，对 AQI 预测具有重要参考价值。此外，系统还利用天气类别(weather_category)作为分类特征，帮助模型理解不同天气状况（如晴天、阴天、雨天等）对空气质量的不同影响。

图 2 AQI 预测特征重要性排序图

（2）模型参数定制与调优

针对 AQI 预测任务的特点，系统对各预测模型进行了细致的参数优化和结构调整。

LightGBM 作为系统中 AQI 预测的主力模型，根据代码中的 LGBM_PARAMS_BY_TARGET 配置，其参数进行了专门优化。系统选择了 regression_l1（MAE）作为优化目标，提高了模型对异常值的鲁棒性。为适应 AQI 数据的复杂模式，设置了 n_estimators 参数为 1000，并通过较小的 learning_rate(0.03)保持训练稳定性。特别针对 AQI 预测中的特征多样性，系统设置了 feature_fraction(0.8)参数，在每次迭代中随机选择 80%的特征构建决策树，增强了模型的泛化能力并减少过拟合风险。此外，系统调整了树结构参数，将 num_leaves 设为 35，max_depth 设为 7，在表达能力和泛化性能间取得平衡。

LSTM 模型在 AQI 预测中主要针对时序依赖关系进行了优化。根据 LSTM_PARAMS_BY_TARGET 配置，系统为 aqi_index 设置了 look_back 参数为 180 天，该长度能够覆盖季节性变化同时不引入过多噪声。为提高模型对复杂时序模式的学习能力，配置了 96 个神经元的隐藏层，并应用了 dropout(0.25)和 recurrent_dropout(0.25)正则化，以应对 AQI 数据的噪声和波动。在训练策略上，系统使用了 batch_size 为 32 的 mini-batch 训练方式，设置了最大 epochs 为 100，并通过 patience 为 12 的 early stopping 机制防止过拟合。

Prophet 模型在 AQI 预测中主要针对多重周期性进行了优化。根据 PROPHET_PARAMS_BY_TARGET 配置，系统为 aqi_index 设置了 additive 模式的 seasonality_mode 参数，考虑到 AQI 的季节性波动相对稳定。同时，启用了年度季节性(yearly_seasonality=True)和周度季节性(weekly_seasonality=True)，以捕捉 AQI 数据的多重周期性变化。系统设置了 changepoint_prior_scale 参数为 0.1，允许模型灵活地适应趋势变化，并将 seasonality_prior_scale 参数设为 5.0，以合理模拟季节性强度。

图 3 AQI 预测模型训练过程可视化

5.7.3 AQI 预测结果分析与比较

基于眉山市 2020-2024 年的历史空气质量数据，系统对各模型的 AQI 预测性能进行了全面评估和比较分析，揭示了不同算法在 AQI 预测任务上的特点和适用场景。

从整体预测精度看，根据 trained_models/model_metrics.json 中记录的实际指标，LightGBM 模型表现最为优异，MAE 为 6.75，RMSE 为 10.23，R² 达到 0.874，MAPE 为 12.43%。这意味着该模型预测的 AQI 值与实际值平均相差约 6.8 点，且能解释 87.4%的 AQI 变异。相比之下，LSTM 模型的性能稍逊，MAE 为 13.85，RMSE 为 17.32，R² 为 0.753，MAPE 为 19.84%。Prophet 模型在三种算法中表现最弱，MAE 为 17.85，RMSE 为 22.54，R² 为 0.584，MAPE 为 31.26%。这一结果表明，对于复杂多变的 AQI 预测任务，基于梯度提升树的 LightGBM 模型具有明显优势。

分析不同污染水平下的预测性能，三种模型表现出不同特点。在低污染水平（AQI<50）条件下，所有模型均表现较好，LightGBM 的表现尤为出色。在中等污染水平（50≤AQI<100）下，LightGBM 模型依然保持稳定性能，而 LSTM 和 Prophet 的误差则相应增加。在高污染水平（AQI≥100）下，所有模型的预测误差均明显增加，但 LightGBM 模型仍然保持了相对较低的误差。这表明随着污染程度增加，预测难度随之提高，但 LightGBM 模型在各污染级别上均保持了相对稳定的性能。

从时效性角度分析，系统比较了不同预测时长下的模型表现。在短期预测中，LightGBM 模型表现最佳；随着预测时长延长，其预测误差逐渐增加，但仍保持较高准确度。LSTM 模型在短期预测中表现一般，随时间延长性能下降不明显。Prophet 模型则在各时长上表现相对稳定但精度较低。这一结果表明，LightGBM 模型更适合进行高精度的短期 AQI 预测，而长期预测则可能需要多模型综合考虑。

季节性能比较显示，模型预测性能存在明显的季节差异。在春季（3-5 月）和秋季（9-11 月），气象条件相对温和稳定，所有模型表现均较好。夏季（6-8 月）由于降水频繁、大气扩散条件好，AQI 整体水平较低且波动小，此时模型表现通常最佳。冬季（12-2 月）则是最具预测挑战的季节，由于采暖排放增加、逆温现象频发等因素，AQI 波动大且易出现高值，所有模型在此季节的误差均较大。这一现象表明，冬季污染预测需要更专业的模型优化和更丰富的特征支持。

图 4 不同模型 AQI 预测效果对比图

误差根源分析揭示了模型预测误差的主要来源。对于 LightGBM 模型，主要预测误差来自于两个方面：一是突发污染事件，如区域性重污染过程；二是极端气象条件，如强对流天气引起的快速变化。LSTM 模型则主要受训练数据量限制，以及对突变的敏感度不足。Prophet 模型的误差主要源于其过度依赖历史季节性模式，对外部因素（如气象条件）的响应能力有限。

图 5 AQI 预测残差分析图

实用性评估表明，基于 LightGBM 的 AQI 预测模型具有较高的应用价值。从计算效率看，该模型预测速度快，可满足实时预测需求；从可解释性看，模型能够输出特征重要性分析结果，有助于理解影响因素；从适应性看，模型支持增量更新，可根据最新数据不断自我调整。在实际部署中，系统可实现基于滑动窗口的模型更新策略，定期使用最新数据重新训练模型，保持预测性能的时效性。

总体而言，系统在 AQI 预测任务上取得了良好性能，特别是 LightGBM 模型展现出的高精度预测能力为空气质量管理提供了有力的决策支持。未来工作将重点提升对极端污染事件的预测能力，并探索融合更多实时监测数据，进一步提高预测准确性和实用性。
