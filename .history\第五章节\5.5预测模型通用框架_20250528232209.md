5.5 预测模型通用框架

本系统构建了多层次的预测模型框架，针对气象与空气质量的时序特性，设计并优化了多种机器学习和深度学习算法。系统实现了 LightGBM、LSTM、GRU、TCN 和 Prophet 等多种算法模型，以应对各种预测任务的特殊需求，充分发挥各模型的独特优势。这些模型各有所长，如 LightGBM 在处理结构化特征方面表现出色，LSTM 和 GRU 善于捕捉长期时序依赖关系，Prophet 则专长于处理具有强季节性的时间序列数据。

5.5.1 模型评估指标

为全面评估模型性能，系统采用多维度的评估体系，根据预测任务的不同性质，区分为数值预测和分类预测两大类评估指标。数值预测指标主要用于温度、AQI、PM2.5 和臭氧等连续变量的预测任务，而分类指标则用于天气类别等离散变量的预测评估。系统通过这些多样的评估指标，可以从不同角度综合衡量预测模型的性能表现。

（1）数值预测指标（用于温度、AQI、PM2.5、臭氧等）

在数值预测任务中，系统实现了多种评估指标以全面衡量模型性能。这些指标不仅能够量化预测误差的绝对大小，还能评估模型解释数据变异的能力，以及在不同量纲下的预测准确度。假设 y_i 是真实值，ŷ_i 是预测值，ȳ 是平均值，n 是样本数量，系统使用以下指标进行模型评估：

① 均方误差（Mean Absolute Error, MAE）是回归任务中最常用的评估指标之一，其数学表达式为：

$$\text{MAE} = \frac{1}{n}\sum_{i=1}^{n}|y_i-\hat{y}_i|$$

均方误差计算每个样本的预测误差的绝对值，然后取平均。MAE 直观反映预测值与实际值的平均偏离程度，单位与原始数据相同，便于解释。在实际应用中，MAE 为用户提供了直观的误差理解，例如温度预测的 MAE 为 0.53℃，意味着平均预测温度与实际温度相差约 0.5 度。

② 均方根误差（Root Mean Squared Error, RMSE）计算方式为误差平方的平均值的平方根：

$$\text{RMSE} = \sqrt{\frac{1}{n}\sum_{i=1}^{n}(y_i-\hat{y}_i)^2}$$

RMSE 计算每个样本的预测误差的平方，取平均后，再开平方根。由于平方的存在，它对较大的误差给予更高的权重，能更好地反映模型在异常值上的表现。在实际应用中，RMSE 特别适合评估那些对异常值敏感的预测任务，如极端天气事件的预测，系统中 PM2.5 预测的 RMSE 为 6.422μg/m³，反映了模型对污染物浓度波动的适应能力。

③ 决定系数（R²）表示模型能解释的因变量变异比例：

$$\text{R}^2 = 1 - \frac{\sum_{i=1}^{n}(y_i-\hat{y}_i)^2}{\sum_{i=1}^{n}(y_i-\bar{y})^2}$$

决定系数表示模型预测的方差在总方差中所占的比例。值越接近 1，模型解释能力越强；值为 0 表示模型预测性能与简单均值相当；值为负表示模型表现差于简单均值。在温度预测任务中，LightGBM 模型的 R² 值高达 0.902，表明模型能解释 90.2%的温度变化，预测能力极为出色。

④ 平均绝对百分比误差（Mean Absolute Percentage Error, MAPE）以百分比形式表示误差大小：

$$\text{MAPE} = \frac{1}{n}\sum_{i=1}^{n}\left|\frac{y_i-\hat{y}_i}{y_i}\right| \times 100\%$$

MAPE 计算每个样本的预测误差相对于真实值的百分比的绝对值，然后取平均。MAPE 将误差标准化为百分比，便于跨不同量级的数据集比较，但在真实值接近零时会产生不稳定结果。在 AQI 指数预测中，系统达到了 7.728%的 MAPE，意味着预测值平均偏离实际值不到 8%，展现了可靠的预测准确度。

（2）分类型预测指标（用于天气状况分类）

对于天气类别预测等分类任务，系统采用了基于混淆矩阵的一系列评估指标。这些指标从不同角度评估分类模型的性能，特别关注在不平衡数据集上的表现。系统通过混淆矩阵计算以下关键概念：TP (True Positives)表示真实为正类，预测也为正类的样本数；FP (False Positives)表示真实为负类，但预测为正类的样本数；FN (False Negatives)表示真实为正类，但预测为负类的样本数；TN (True Negatives)表示真实为负类，预测也为负类的样本数。

① 准确率（Accuracy）是分类任务中最直观的评估指标，表示正确分类的样本比例：

$$\text{Accuracy} = \frac{\text{TP} + \text{TN}}{\text{TP} + \text{TN} + \text{FP} + \text{FN}}$$

准确率代表所有样本中被正确分类的比例，提供了模型整体性能的快速评估，但在类别不平衡情况下可能产生误导。在天气类别预测中，GRU 模型的准确率达到 68.4%，表明近七成的天气状况被正确预测。

② 加权 F1 分数（Weighted F1）是精确率和召回率的调和平均值：

$$\text{F1} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}$$

加权 F1 分数在处理多分类问题且类别不平衡时特别有用。它会计算每个类别的 F1 分数，然后根据每个类别中真实样本的数量（即"支持度" support）进行加权平均。在天气类别预测中，系统实现的加权 F1 分数为 0.584，表明模型在各类别上的综合表现良好。

③ 精确率（Precision）衡量预测为某类别的样本中真正属于该类别的比例：

$$\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}$$

精确率表示在所有被预测为正类的样本中，有多少是真正的正类。它反映了模型预测正类的可靠性，对于需要高度确信的预测场景（如极端天气预警）尤为重要。在天气类别预测中，系统的加权精确率达到 0.554，说明模型的预测结果具有一定的可信度。

④ 召回率（Recall）衡量某类别样本中被正确识别的比例：

$$\text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}$$

召回率表示在所有真正的正类样本中，有多少被模型成功预测出来。它反映了模型识别正类的完整性，对于不能漏掉任何正例的场景（如恶劣天气预报）尤为重要。系统在天气类别预测中的加权召回率为 0.643，表明模型能够捕捉到大部分的天气变化情况。

在系统实现中，模型评估指标计算通过评估函数实现，并将结果自动保存至 model_metrics.json 文件，便于模型性能的持续监控与比较。以下是系统中实际记录的各模型性能指标，例如 LightGBM 模型在温度预测任务上的 MAE 为 0.53℃，R² 为 0.902，而 GRU 模型在天气类别预测任务上的准确率达到 68.4%。

图 1 模型评估指标计算流程示意图

5.5.2 通用数据预处理与特征工程

数据预处理与特征工程是构建高质量机器学习模型的基础，对预测性能有着决定性影响。系统实现了全面的预处理流程与特征工程框架，确保输入模型的数据质量。气象和空气质量数据的特殊性在于其高度的时间相关性、季节性变化以及多种外部因素的影响，因此需要精心设计的预处理策略以提取有价值的信息模式。

（1）缺失值处理

气象数据普遍存在缺失值问题，这可能源于传感器故障、数据传输中断或人为因素。缺失数据如果处理不当，会严重影响模型的训练质量和预测准确性。系统实现了多层次的缺失值处理策略，针对不同类型的数据采用差异化的填充方法。

对于数值型变量（如温度、AQI 等），系统采用了一套多级填充策略：首先尝试线性插值，这种方法特别适合时间序列数据，因为它考虑了数据点间的时间关系；如果线性插值无法应用（例如缺失点过多或位于时间序列边界），系统会尝试前向填充，即使用前一个有效值填充；若前向填充仍无法完成，则采用后向填充；最后，如果以上方法都无法应用，系统会使用该列的中位数进行填充，这种方法对异常值不敏感，能保持数据分布特性。

对于分类型变量（如天气状况），系统设计了专门的填充策略：首先使用前向填充，这基于天气状况通常在短期内相对稳定的特性；如果前向填充不可行，则采用后向填充；最后，如果两种方法都无法应用，系统会使用频率最高的类别进行填充，这符合统计学上的最大似然估计原则。

通过这种多层次的缺失值处理策略，系统将数据完整率提升至 99.9%以上，为后续建模提供可靠基础。实践证明，精细的缺失值处理能显著提升模型的预测性能，特别是对于复杂的时间序列预测任务。

图 2 缺失值处理策略流程图

（2）标准化处理

在机器学习模型训练中，不同特征的量纲差异可能导致模型训练不稳定或偏向取值范围较大的特征。为解决这一问题，系统对所有数值特征实施了标准化处理，但根据不同模型的特性，采用了差异化的标准化策略。

对于基于树的模型如 LightGBM，由于其决策过程基于特征的相对排序而非绝对值，本质上对特征缩放不敏感。因此，系统对其输入特征的标准化是可选的。这一特性使 LightGBM 能直接处理原始数据，减少了预处理的计算开销。然而，为了保持一致性和便于特征重要性比较，系统仍对部分情况下的 LightGBM 输入进行了标准化处理。

对于神经网络模型如 LSTM 和 GRU，标准化处理则是必不可少的。系统采用 MinMaxScaler 将所有输入特征统一缩放到[0,1]区间，这不仅加速了模型收敛，还有效防止了梯度爆炸问题。在实践中，标准化处理使 LSTM 模型的训练速度提升了约 35%，同时提高了预测稳定性。

对于专门的时间序列模型 Prophet，系统采用了更为特定的方法。由于 Prophet 对数据分布的敏感性，对于严重偏斜的数据分布（如某些污染物浓度数据），系统会先进行对数变换再建模，这种方法显著提升了模型对异常高值的处理能力。

标准化处理保证了不同特征在相同尺度下参与模型训练，提升了模型的稳定性和收敛速度。实验表明，适当的标准化能使模型训练时间减少 30%-50%，同时在多数情况下提高预测精度。

图 3 特征标准化处理示意图

（3）特征构建

特征工程是提升预测模型性能的关键环节。本系统实现了全面的特征工程框架，从原始时间序列数据中提取出丰富的派生特征，大大增强了模型的预测能力。

系统的特征构建体系首先关注时间特征的提取。通过解析日期列，系统自动生成多种时间周期特征：月份特征捕捉了温度、污染物等变量的年度周期性变化，对季节性趋势的建模至关重要；星期几特征反映了每周的规律性变化，特别能够捕捉人类活动（如工作日与周末）对空气质量的影响；一年中的第几天特征则更精确地定位季节性位置，对长期趋势预测尤为重要；季度特征则提供了季节变化的粗粒度表示，简化了季节效应的模式识别。这些时间特征共同构成了预测模型理解时间模式的基础。

滞后特征是系统特征工程的另一重要组成部分。通过时间位移，系统创建了多个历史信息特征：短期滞后（1 天、2 天、3 天）有效捕捉了数据的短期连续性和趋势，这对天气和空气质量的短期预测尤为重要；中期滞后（7 天、14 天）则捕捉了周度变化模式，反映了工作-休息周期对环境的影响；长期滞后（30 天）则捕捉了月度变化模式，有助于识别更长周期的环境变化。这些滞后特征使模型能够"记住"过去的数据模式，并将其应用于未来预测。

滚动统计特征是系统特征工程的第三个关键维度。通过计算不同历史窗口内的统计量，系统生成了多种聚合特征：滚动均值反映了近期平均水平，有效平滑短期波动，增强了预测的稳定性；滚动标准差量化了近期波动性大小，有助于识别不稳定期；滚动最大/最小值则捕捉了极端情况和范围信息，对异常事件预警特别有价值。

图 4 特征工程流程图

通过这种多层次的特征工程，系统为模型提供了丰富的预测信息，有效提升了预测精度。实验表明，仅基于原始特征的模型 MAE 为 1.42℃，而加入派生特征后模型 MAE 降至 0.53℃，提升了 62.7%。这一显著改进充分证明了精心设计的特征工程对预测性能的重要影响。

5.5.3 预测模型架构与实现

系统整合了多种不同特性的预测模型，包括 LightGBM、LSTM、Prophet、GRU 和 TCN，每种模型各有优势，针对不同预测任务进行优化配置。这种多模型方法能够全面应对气象和空气质量预测中的各种挑战，如短期波动、长期趋势、季节性变化和极端事件等。

（1）LGBM 模型通用实现

LightGBM 是系统的核心预测模型，它基于梯度提升决策树原理设计，通过迭代训练多棵决策树，逐步拟合残差，最终结合多棵树的预测结果生成最终预测。这种模型在处理结构化数据和非线性关系方面表现出色，特别适合气象和空气质量预测中的复杂模式识别。

LightGBM 相比传统梯度提升树算法有着显著的效率优势。它采用基于直方图的决策树算法，将连续特征值离散化为 k 个桶，显著降低了特征分割点搜索的计算复杂度。此外，LightGBM 引入了 GOSS（Gradient-based One-Side Sampling）技术，通过保留大梯度样本并随机抽样小梯度样本，优化了采样过程，使模型能够在保持精度的同时提高训练效率。在高维特征空间中，LightGBM 还支持 EFB（Exclusive Feature Bundling）特征合并技术，将互斥的特征（很少同时取非零值的特征）捆绑在一起，有效处理高维稀疏特征，这对于包含大量分类变量和时间特征的气象数据特别有价值。

在系统实现中，针对不同预测目标，LightGBM 模型采用了差异化的参数配置。对于回归任务（如温度、AQI 预测），系统采用了 MAE（Mean Absolute Error）作为优化目标，这种选择使模型对异常值的敏感度降低，提升了预测的稳健性。特别是对于 AQI 预测，系统调整了学习率为 0.03，设置了较小的 num_leaves 参数（35）以防止过拟合，同时通过 feature_fraction 参数控制特征随机采样比例，进一步增强了模型的泛化能力。对于分类任务（如天气类别预测），系统则采用了多类对数损失（multi_logloss）作为优化目标，并引入 class_weight 参数解决类别不平衡问题，提高了对稀有天气类型的预测准确率。

图 5 LightGBM 模型架构图

在模型训练与评估过程中，系统还实现了完整的特征重要性分析。通过 SHAP（SHapley Additive exPlanations）方法，系统能够量化各特征对预测结果的贡献，这不仅提升了模型的可解释性，还为特征工程的进一步优化提供了清晰指导。分析结果显示，对于温度预测，前一天的温度、月份特征和 7 天滚动平均温度是最重要的三个特征；而对于 AQI 预测，PM2.5 浓度、风速和相对湿度则是影响最大的因素。

（2）LSTM 模型通用实现

长短时记忆网络（LSTM）是系统中专为处理长序列依赖关系设计的深度学习模型，它能有效解决传统 RNN 在长序列上的梯度消失问题。LSTM 通过其特殊的门控机制，包括遗忘门、输入门和输出门，实现对长期依赖信息的选择性记忆，这一特性使其特别适合气象和空气质量等具有长期季节性模式的时间序列预测任务。

在系统实现中，LSTM 模型的核心架构包括四个关键组件：输入层接收序列特征，维度为[batch_size, sequence_length, 1]；LSTM 层包含多个 LSTM 单元，处理序列信息并捕捉时间依赖性；为防止过拟合，系统加入了 Dropout 层，通过随机丢弃部分神经元增强模型的泛化能力；最后，全连接输出层生成最终的预测结果。

根据不同预测任务的特性，系统对 LSTM 模型进行了细致的参数优化。对于温度预测，由于其强烈的季节性特征，系统设置了较大的 units 参数（128 个神经元）以提高模型容量，并配置了 365 天的 look_back 参数，确保模型能够捕捉完整的年度季节性变化；对于 AQI 和 PM2.5 预测，考虑到其短期波动特性，系统减小了网络规模（96 个神经元）并增加了 dropout 率（0.25），同时将 look_back 参数调整为 180 天，这种配置在保持对季节性的敏感度的同时，提高了对短期空气质量变化的预测准确性。

图 6 LSTM 网络结构图

LSTM 模型在训练过程中，系统采用了一系列技术确保模型性能。early stopping 策略通过监控验证集上的性能，在过拟合开始前自动停止训练，提高了模型的泛化能力；学习率调度器则根据训练进度动态调整学习率，加速收敛过程；批量标准化层通过标准化每个小批量的均值和方差，加速了训练过程并提高了模型稳定性。这些优化技术使 LSTM 模型能够在有限的训练数据上达到理想的预测性能。

（3）Prophet 模型通用实现

Prophet 是系统中专门针对具有强季节性和节假日效应的时间序列设计的预测模型，它由 Facebook 开发，采用分解思想处理时间序列数据。Prophet 将时间序列分为四个关键组件：趋势项 g(t)描述数据的长期变化趋势；季节项 s(t)捕捉周期性变化模式；节假日效应项 h(t)处理特殊事件的影响；误差项 εt 则表示随机波动。这种分解表达式为：

$$y(t) = g(t) + s(t) + h(t) + \varepsilon_t$$

Prophet 模型在气象和空气质量预测中具有独特优势。首先，它的趋势项采用分段线性函数或逻辑增长曲线建模，并且能自动检测变点，适应趋势的非线性变化；其次，季节项通过傅里叶级数拟合周期性模式，可以同时处理多个季节性（年度、周度等），这对于气象数据尤为重要；第三，节假日效应通过指示函数刻画特殊事件影响，可以手动指定重要日期（如春节、国庆等），捕捉这些特殊时期对污染物排放的影响；最后，误差项被假设服从正态分布，便于统计推断。

系统针对不同预测目标，调整了 Prophet 模型的关键参数。对于温度预测，由于温度的季节性波动幅度随基线变化，系统设置了 multiplicative 的 seasonality_mode；而对于 AQI 预测，考虑到其季节性波动幅度相对稳定，则选择了 additive 模式。此外，系统通过调整 changepoint_prior_scale 参数控制趋势变化的灵活性，对温度使用较小的值（0.05）保持平滑趋势，对污染物使用较大的值（0.1）以适应其更为剧烈的波动。seasonality_prior_scale 参数则控制季节性成分的强度，针对温度的强季节性特征设置了较大值（10.0）。

图 7 Prophet 模型分解图

Prophet 模型的一个显著特点是它能自动处理缺失数据、适应非均匀采样间隔，这对于气象数据中常见的不完整记录特别有价值。更重要的是，Prophet 不仅提供点预测，还能生成预测区间估计，量化预测的不确定性，这对于风险敏感的气象预警应用尤为重要。在系统的实际应用中，Prophet 在温度和污染物浓度的长期趋势和季节性预测方面表现出色，特别是在预测未来 1-2 周的整体趋势时，展现了稳定的预测能力。

（4）GRU 和 TCN 模型通用实现

门控循环单元（GRU）和时间卷积网络（TCN）是系统针对不同时序预测需求而引入的两种专业模型。这两种模型各具特色，能够处理时间序列数据中的不同方面的模式，特别是在天气类别预测等复杂任务中发挥重要作用。

GRU 作为 LSTM 的简化版本，通过合并遗忘门和输入门为更新门，简化了模型结构，在保持长期记忆能力的同时提升了计算效率。这种简化不仅减少了模型参数数量，降低了过拟合风险，还加快了训练速度，使得在有限计算资源下能处理更长的序列数据。系统主要将 GRU 用于天气类别预测任务，其架构包括：输入层接收序列特征；GRU 层通过门控机制处理时序依赖；Dropout 层通过随机失活防止过拟合；最后通过全连接层输出各天气类别的概率分布。

在天气类别预测任务中，GRU 模型展现出卓越的性能。通过分析其内部激活状态，研究发现 GRU 特别善于捕捉天气状况的转换模式，如晴天转多云、多云转阴的规律性变化。这种能力源于其门控机制能有效保留过去状态的相关信息，同时筛选出当前输入中的关键特征。系统针对天气类别数据的不平衡性，还实施了类别权重调整，提高了对稀有天气类型（如暴雨、大雪等）的预测敏感度。

图 8 GRU 网络结构图

时间卷积网络（TCN）是系统中引入的另一种专门处理时序数据的深度学习架构。与传统的循环神经网络不同，TCN 通过膨胀卷积（Dilated Convolution）有效扩大感受野，同时保持并行计算的高效性。这种设计使 TCN 能在保持计算效率的同时，捕捉更长范围的时间依赖关系。TCN 的核心特征包括：因果卷积确保模型只使用当前及过去的信息进行预测，避免数据泄露；膨胀卷积通过设置膨胀率指数增长，实现指数级扩大的感受野，能高效捕捉长期依赖；残差连接则缓解了深层网络训练困难，稳定梯度传播，使模型能够更容易学习恒等映射，保留有用的历史信息。

TCN 在复杂季节性模式的预测任务中展现出独特优势。其固定的感受野大小使其能够准确捕捉特定时间范围内的模式，这对于具有明确周期性的气象现象（如日变化、周变化）特别有效。与此同时，TCN 的并行计算特性使其训练和推理速度远快于传统 RNN 模型，在实时预测应用中具有明显优势。系统对 TCN 进行了专门优化，通过调整膨胀率和层数，使其感受野覆盖约一个月的历史数据，这一配置在天气类别预测中取得了良好效果。

图 9 TCN 网络结构图

通过 GRU 和 TCN 这两种模型的引入，系统丰富了预测模型库的多样性，能够应对不同特性的预测任务。特别是在天气类别预测中，GRU 模型取得了 68.4%的准确率，显著优于传统方法。这种多模型策略使系统能够针对不同预测任务选择最合适的算法，充分发挥各模型的独特优势，提供更准确、更全面的预测结果。
